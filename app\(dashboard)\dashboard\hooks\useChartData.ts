import { useMemo } from "react";
import { SalesData } from "../types/dashboard";
import { transformMonthlyStats } from "../utils/dashboardUtils";

interface UseChartDataProps {
  ROC: any;
}

export const useChartData = ({ ROC }: UseChartDataProps) => {
  const chartData: SalesData[] = useMemo(() => {
    return transformMonthlyStats(ROC?.monthly_stats || []);
  }, [ROC]);

  const hasChartData = useMemo(() => {
    return chartData.some(data => data.sales > 0 || data.leads > 0);
  }, [chartData]);

  return {
    chartData,
    hasChartData,
  };
};
