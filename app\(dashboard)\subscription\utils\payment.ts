import { PlanTier } from '../types/subscription';
import { PaymentGateway } from '@/lib/services/payment';
import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

/**
 * Generate success URL for payment
 * @param planId - Selected plan
 * @param billingCycle - Billing cycle
 * @returns Success URL string
 */
export function generateSuccessUrl(planId: PlanTier, billingCycle: 'monthly' | 'yearly'): string {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  return baseUrl + SUBSCRIPTION_CONSTANTS.URLS.SUCCESS_URL_TEMPLATE(planId, billingCycle);
}

/**
 * Generate cancel URL for payment
 * @returns Cancel URL string
 */
export function generateCancelUrl(): string {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  return baseUrl + SUBSCRIPTION_CONSTANTS.URLS.CANCEL_URL;
}

/**
 * Get button text based on plan and subscription status
 * @param selectedPlan - Currently selected plan
 * @param currentPlan - User's current plan
 * @param isActive - Whether current subscription is active
 * @param loading - Whether payment is processing
 * @returns Button text string
 */
export function getSubscribeButtonText(
  selectedPlan: PlanTier,
  currentPlan: PlanTier | null,
  isActive: boolean,
  loading: boolean
): string {
  if (loading) return SUBSCRIPTION_CONSTANTS.BUTTON_TEXTS.PROCESSING;

  if (selectedPlan === 'starter') {
    return SUBSCRIPTION_CONSTANTS.BUTTON_TEXTS.SWITCH_TO_FREE;
  }

  if (currentPlan === selectedPlan && isActive) {
    return SUBSCRIPTION_CONSTANTS.BUTTON_TEXTS.CURRENT_PLAN;
  }

  return SUBSCRIPTION_CONSTANTS.BUTTON_TEXTS.SUBSCRIBE_NOW;
}

/**
 * Check if subscribe button should be disabled
 * @param selectedPlan - Currently selected plan
 * @param currentPlan - User's current plan
 * @param isActive - Whether current subscription is active
 * @param loading - Whether payment is processing
 * @returns Boolean indicating if button should be disabled
 */
export function isSubscribeButtonDisabled(
  selectedPlan: PlanTier,
  currentPlan: PlanTier | null,
  isActive: boolean,
  loading: boolean
): boolean {
  return loading || (currentPlan === selectedPlan && isActive);
}

/**
 * Prepare payment checkout data
 * @param params - Payment parameters
 * @returns Payment checkout configuration
 */
export function preparePaymentCheckout(params: {
  gateway: PaymentGateway;
  planId: PlanTier;
  billingCycle: 'monthly' | 'yearly';
  user: any;
}) {
  const { gateway, planId, billingCycle, user } = params;
  
  return {
    gateway,
    planId,
    customerId: user.id,
    customerName: user.user_metadata?.full_name || user.email,
    customerEmail: user.email,
    successUrl: generateSuccessUrl(planId, billingCycle),
    cancelUrl: generateCancelUrl(),
  };
}
