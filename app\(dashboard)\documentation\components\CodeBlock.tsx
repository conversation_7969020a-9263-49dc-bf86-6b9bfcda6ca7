import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Check } from "lucide-react";
import { formatCode } from "../utils/documentationUtils";

interface CodeBlockProps {
  code: string;
  language?: string;
  title?: string;
  copyId: string;
  copied: string | null;
  onCopy: (id: string, text: string) => void;
  className?: string;
}

export const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = "typescript",
  title,
  copyId,
  copied,
  onCopy,
  className = "",
}) => {
  const formattedCode = formatCode(code);

  return (
    <div className={`relative mt-2 rounded-md bg-slate-950 p-4 ${className}`}>
      {title && (
        <div className="mb-3 pb-2 border-b border-slate-700">
          <h4 className="text-sm font-medium text-slate-200">{title}</h4>
        </div>
      )}
      
      <div className="absolute right-4 top-4">
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-slate-400 hover:text-slate-100 hover:bg-slate-800"
          onClick={() => onCopy(copyId, formattedCode)}
        >
          {copied === copyId ? (
            <Check size={14} className="text-green-400" />
          ) : (
            <Copy size={14} />
          )}
        </Button>
      </div>

      <pre className="overflow-x-auto text-sm text-slate-100">
        <code className={`font-mono language-${language}`}>
          {formattedCode}
        </code>
      </pre>
    </div>
  );
};

interface InlineCodeProps {
  children: React.ReactNode;
  className?: string;
}

export const InlineCode: React.FC<InlineCodeProps> = ({ children, className = "" }) => {
  return (
    <code className={`px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded text-sm font-mono ${className}`}>
      {children}
    </code>
  );
};

interface CodeSnippetProps {
  snippet: string;
  label?: string;
  copyId: string;
  copied: string | null;
  onCopy: (id: string, text: string) => void;
}

export const CodeSnippet: React.FC<CodeSnippetProps> = ({
  snippet,
  label,
  copyId,
  copied,
  onCopy,
}) => {
  return (
    <div className="flex items-center space-x-2 bg-gray-50 dark:bg-gray-900 p-3 rounded-lg">
      {label && (
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 min-w-0 flex-shrink-0">
          {label}:
        </span>
      )}
      <code className="flex-1 text-sm font-mono text-gray-900 dark:text-gray-100 truncate">
        {snippet}
      </code>
      <Button
        variant="ghost"
        size="icon"
        className="h-6 w-6 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex-shrink-0"
        onClick={() => onCopy(copyId, snippet)}
      >
        {copied === copyId ? (
          <Check size={12} className="text-green-500" />
        ) : (
          <Copy size={12} />
        )}
      </Button>
    </div>
  );
};
