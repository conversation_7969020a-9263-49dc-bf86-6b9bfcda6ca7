"use client";

import { Features } from "./(home)/home/<USER>";
import { Footer } from "./(home)/home/<USER>";
import { Header } from "./(home)/home/<USER>";
import { Pricing } from "./(home)/home/<USER>";
import { Testimonials } from "./(home)/home/<USER>";
import { Hero } from "./(home)/home/<USER>";
import { useAuth } from "@/lib/hooks/useAuth";
import { AuthLoadingScreen } from "@/components/auth/AuthLoadingScreen";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Home() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // If user is authenticated, redirect to dashboard
    if (isAuthenticated && !loading) {
      router.replace("/dashboard");
    }
  }, [isAuthenticated, loading, router]);

  // Show loading screen while checking authentication
  if (loading) {
    return <AuthLoadingScreen />;
  }

  // If user is authenticated, show loading while redirecting
  if (isAuthenticated) {
    return <AuthLoadingScreen />;
  }

  return (
    <div className="flex flex-col min-h-screen">
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-grow">
          <Hero />
          <div className="mx-auto max-w-[1440px] px-4 sm:px-6 lg:px-8">
            <Features />
            <Pricing />
            <Testimonials />
          </div>
        </main>
        <Footer />
      </div>
    </div>
  );
}
