import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ChevronDown, ChevronUp } from "lucide-react";
import { IntegrationTableProps } from "../types/integration";
import { StatusBadge } from "./StatusBadge";
import { DocumentationButton } from "./DocumentationButton";
import {
  INTEGRATION_MESSAGES,
  INTEGRATION_LABELS
} from "../constants/integration";

export const IntegrationTable: React.FC<IntegrationTableProps> = ({
  platforms,
  expandedRow,
  onToggleRow,
  onOpenDocumentation,
}) => {
  if (platforms.length === 0) {
    return (
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="hidden md:table-cell">{INTEGRATION_LABELS.PLATFORM}</TableHead>
              <TableHead className="hidden md:table-cell">{INTEGRATION_LABELS.CATEGORY}</TableHead>
              <TableHead className="hidden md:table-cell">{INTEGRATION_LABELS.DESCRIPTION}</TableHead>
              <TableHead className="hidden md:table-cell">{INTEGRATION_LABELS.STATUS}</TableHead>
              <TableHead className="hidden md:table-cell">{INTEGRATION_LABELS.DOCUMENTATION}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8">
                {INTEGRATION_MESSAGES.NO_RESULTS}
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table className="text-xs">
        <TableHeader className="hidden md:table-header-group">
          <TableRow className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-black">
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">{INTEGRATION_LABELS.PLATFORM}</TableHead>
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">{INTEGRATION_LABELS.CATEGORY}</TableHead>
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">{INTEGRATION_LABELS.DESCRIPTION}</TableHead>
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">{INTEGRATION_LABELS.STATUS}</TableHead>
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">{INTEGRATION_LABELS.DOCUMENTATION}</TableHead>
          </TableRow>
        </TableHeader>

        <TableBody className="w-auto">
          {platforms.map((platform) => (
            <React.Fragment key={platform.id}>
              {/* Mobile View (Collapsed) */}
              <TableRow className="flex md:hidden lg:hidden items-center justify-between border-b border-gray-200 dark:border-gray-800 p-2 last:border-none hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                <div className="flex flex-col gap-0 md:hidden">
                  <div className="text-[1rem] font-medium text-gray-900 dark:text-gray-100">{platform.name}</div>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="text-gray-500 dark:text-gray-400">{platform.category}</div>
                    <StatusBadge status={platform.status} />
                  </div>
                </div>
                <TableCell>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onToggleRow(platform.id)}
                    className="h-8 w-8 border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-gray-600 dark:text-gray-400 rounded-md"
                  >
                    {expandedRow === platform.id ? (
                      <ChevronUp />
                    ) : (
                      <ChevronDown />
                    )}
                  </Button>
                </TableCell>
              </TableRow>

              {/* Mobile View (Expanded) */}
              {expandedRow === platform.id && (
                <TableRow className="md:hidden lg:hidden border-b border-gray-200 dark:border-gray-800">
                  <TableCell colSpan={5} className="py-3 px-4">
                    <div className="rounded-md">
                      <p className="text-sm text-gray-700 dark:text-gray-300 py-2">
                        {platform.description}
                      </p>

                      {platform.documentationUrl && (
                        <div className="flex justify-end mt-2">
                          <DocumentationButton
                            url={platform.documentationUrl}
                            size="sm"
                          />
                        </div>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              )}

              {/* Desktop View */}
              <TableRow
                key={`${platform.id}-desktop`}
                className="hidden md:table-row border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
              >
                <TableCell className="py-3 px-4 font-medium text-gray-900 dark:text-gray-100">{platform.name}</TableCell>
                <TableCell className="py-3 px-4 text-gray-700 dark:text-gray-300">{platform.category}</TableCell>
                <TableCell className="py-3 px-4 text-gray-700 dark:text-gray-300">{platform.description}</TableCell>
                <TableCell className="py-3 px-4">
                  <StatusBadge status={platform.status} />
                </TableCell>
                <TableCell className="py-3 px-4">
                  <DocumentationButton url={platform.documentationUrl} />
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
