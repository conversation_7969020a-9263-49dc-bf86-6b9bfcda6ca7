import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useDispatch } from "react-redux";
import { toast } from "sonner";
import { useCreateFormMutation, useUpdateFormMutation, useDeleteFormMutation } from "@/lib/store/services/forms";
import { formsApis } from "@/lib/store/services/forms";
import { FormValues } from "../types/forms";
import { FORM_MESSAGES } from "../constants/forms";
import {
  formatFormDataForSubmission,
  formatFormDataForUpdate,
  generatePreviewHtml,
  createPreviewUrl,
  getFormIdForEmbed,
  getDefaultHtml,
  getDefaultCss,
  getDefaultJs
} from "../utils/formUtils";

interface UseFormActionsProps {
  workspaceId: string;
  searchParams: ReadonlyURLSearchParams | null;
  startFormLoading: () => void;
  stopFormLoading: () => void;
  updatePreviewUrl: (url: string | null) => void;
  navigateToEditor: () => void;
  form: any; // React Hook Form instance
}

export const useFormActions = ({
  workspaceId,
  searchParams,
  startFormLoading,
  stopFormLoading,
  updatePreviewUrl,
  navigateToEditor,
  form,
}: UseFormActionsProps) => {
  const router = useRouter();
  const dispatch = useDispatch();

  // RTK Query mutations
  const [createForm] = useCreateFormMutation();
  const [updateForm] = useUpdateFormMutation();
  const [deleteFormMutation] = useDeleteFormMutation();

  // Fetch form details for editing
  const fetchFormDetails = useCallback(async (id: string) => {
    try {
      startFormLoading();

      const { data: formData, error } = await dispatch(
        formsApis.endpoints.getFormById.initiate(id)
      ).unwrap();

      if (error) {
        throw new Error(FORM_MESSAGES.ERROR.LOAD_FAILED);
      }

      // Reset form with fetched data
      form.reset({
        name: formData.name,
        description: formData.description || "",
        html_content: formData.html_content,
        css_content: formData.css_content || "",
        js_content: formData.js_content || "",
        is_active: formData.is_active,
        lead_source_id: formData.lead_source_id || "",
      });

      navigateToEditor();
    } catch (error) {
      console.error('Error fetching form details:', error);
      toast.error(FORM_MESSAGES.ERROR.LOAD_FAILED);
    } finally {
      stopFormLoading();
    }
  }, [dispatch, form, navigateToEditor, startFormLoading, stopFormLoading]);

  // Create or update form
  const onSubmit = useCallback(async (values: FormValues) => {
    try {
      startFormLoading();

      const formId = getFormIdForEmbed(searchParams);
      let result;

      if (formId) {
        // Update existing form
        const updateData = formatFormDataForUpdate(values);
        result = await updateForm({
          id: formId,
          ...updateData,
        }).unwrap();

        toast.success(FORM_MESSAGES.SUCCESS.UPDATED);
      } else {
        // Create new form
        const createData = formatFormDataForSubmission(values, workspaceId);
        result = await createForm(createData).unwrap();

        toast.success(FORM_MESSAGES.SUCCESS.CREATED);

        // Redirect to the form editor with the new form ID
        if (result?.form?.id) {
          router.push(`/forms?id=${result.form.id}`);
          updatePreviewUrl(`${window.location.origin}/embed/forms/${result.form.id}`);
        }
      }
    } catch (error) {
      console.error('Error saving form:', error);
      toast.error(FORM_MESSAGES.ERROR.SAVE_FAILED);
    } finally {
      stopFormLoading();
    }
  }, [
    createForm,
    updateForm,
    router,
    searchParams,
    startFormLoading,
    stopFormLoading,
    updatePreviewUrl,
    workspaceId,
  ]);

  // Delete form
  const deleteForm = useCallback(async (id: string) => {
    if (!confirm(FORM_MESSAGES.CONFIRM.DELETE)) {
      return;
    }

    try {
      startFormLoading();

      await deleteFormMutation(id).unwrap();
      toast.success(FORM_MESSAGES.SUCCESS.DELETED);

      // If currently editing this form, redirect to forms list
      const currentFormId = getFormIdForEmbed(searchParams);
      if (currentFormId === id) {
        router.push('/forms');
      }
    } catch (error) {
      console.error('Error deleting form:', error);
      toast.error(FORM_MESSAGES.ERROR.DELETE_FAILED);
    } finally {
      stopFormLoading();
    }
  }, [deleteFormMutation, router, searchParams, startFormLoading, stopFormLoading]);

  // Generate form preview
  const generatePreview = useCallback(() => {
    const formValues = form.getValues();
    const formId = getFormIdForEmbed(searchParams);
    const baseUrl = window.location.origin;

    const fullHtml = generatePreviewHtml(formValues, formId, baseUrl);
    const url = createPreviewUrl(fullHtml);

    updatePreviewUrl(url);

    // Open the preview in a new tab
    window.open(url, '_blank');
  }, [form, searchParams, updatePreviewUrl]);

  // Create new form
  const createNewForm = useCallback(() => {
    form.reset({
      name: "",
      description: "",
      html_content: getDefaultHtml(),
      css_content: getDefaultCss(),
      js_content: getDefaultJs(),
      is_active: true,
      lead_source_id: "none",
    });

    router.push('/forms');
    navigateToEditor();
  }, [form, router, navigateToEditor]);

  // Edit existing form
  const editForm = useCallback((id: string) => {
    router.push(`/forms?id=${id}`);
  }, [router]);

  // Preview form in new tab
  const previewForm = useCallback((id: string) => {
    const baseUrl = window.location.origin;
    window.open(`${baseUrl}/embed/forms/${id}`, '_blank');
  }, []);

  return {
    // Form operations
    onSubmit,
    createNewForm,
    editForm,
    deleteForm,
    fetchFormDetails,

    // Preview operations
    generatePreview,
    previewForm,
  };
};
