"use client";

import React, { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, Calendar, CreditCard, ArrowRight } from "lucide-react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { SUBSCRIPTION_PLANS, PlanTier, UserSubscription } from "@/lib/types/subscription";
import { format, addDays } from "date-fns";
import { Skeleton } from "@/components/ui/skeleton";

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';

// Create a wrapper component that uses useSearchParams
function SubscriptionSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClientComponentClient();
  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);

  useEffect(() => {
    const fetchSubscriptionStatus = async () => {
      try {
        setLoading(true);

        // Get the session ID or plan from the URL
        const sessionId = searchParams?.get("session_id") || null;
        const planId = searchParams?.get("plan") as PlanTier | null;
        const cycle = searchParams?.get("cycle") as "monthly" | "yearly" | null;

        if (!sessionId && !planId) {
          // If no session ID or plan, redirect to subscription page
          router.push("/dashboard/subscription");
          return;
        }

        // Get the current user
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
          router.push("/login");
          return;
        }

        if (sessionId) {
          // For Stripe, verify the session
          const response = await fetch("/api/payments/stripe/verify-session", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ sessionId }),
          });

          const data = await response.json();

          if (data.error) {
            throw new Error(data.error);
          }

          // Update user subscription in Supabase
          const { error } = await supabase.auth.updateUser({
            data: {
              subscription: {
                planId: data.planId,
                status: "active",
                currentPeriodEnd: data.currentPeriodEnd,
                cancelAtPeriodEnd: false,
                paymentMethod: "stripe",
              },
            },
          });

          if (error) throw error;

          setSubscription({
            planId: data.planId,
            status: "active",
            currentPeriodEnd: data.currentPeriodEnd,
            cancelAtPeriodEnd: false,
            paymentMethod: "stripe",
          });
        } else if (planId) {
          // For PayPal or direct updates, create a subscription based on the plan
          const currentPeriodEnd = format(
            addDays(new Date(), cycle === "yearly" ? 365 : 30),
            "yyyy-MM-dd'T'HH:mm:ss'Z'"
          );

          // Get payment method with type safety
          const paymentMethod = searchParams?.get("payment_method") || "unknown";
          const safePaymentMethod = (
            paymentMethod === "stripe" ||
            paymentMethod === "paypal" ||
            paymentMethod === "razorpay"
          ) ? paymentMethod : undefined;

          // Update user subscription in Supabase
          const { error } = await supabase.auth.updateUser({
            data: {
              subscription: {
                planId,
                status: "active",
                currentPeriodEnd,
                cancelAtPeriodEnd: false,
                paymentMethod: safePaymentMethod,
              },
            },
          });

          if (error) throw error;

          setSubscription({
            planId,
            status: "active",
            currentPeriodEnd,
            cancelAtPeriodEnd: false,
            paymentMethod: safePaymentMethod,
          });
        }
      } catch (error) {
        console.error("Error verifying subscription:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptionStatus();
  }, [router, searchParams, supabase]);

  return (
    <div className="container max-w-2xl py-12">
      <Card className="text-center overflow-hidden">
        {loading ? (
          <div className="p-8">
            <div className="flex justify-center mb-6">
              <Skeleton className="h-20 w-20 rounded-full" />
            </div>
            <Skeleton className="h-8 w-3/4 mx-auto mb-4" />
            <Skeleton className="h-4 w-1/2 mx-auto mb-8" />
            <Skeleton className="h-24 w-full rounded-lg mb-6" />
            <Skeleton className="h-10 w-full rounded-lg mb-3" />
            <Skeleton className="h-10 w-full rounded-lg" />
          </div>
        ) : (
          <>
            <div className="bg-gradient-to-r from-primary/20 to-blue-600/20 h-16"></div>
            <CardHeader className="relative pt-12 pb-6">
              <div className="absolute -top-10 left-1/2 transform -translate-x-1/2">
                <div className="h-20 w-20 rounded-full bg-primary/10 flex items-center justify-center border-4 border-background">
                  <CheckCircle2 className="h-10 w-10 text-primary" />
                </div>
              </div>
              <CardTitle className="text-2xl md:text-3xl">Subscription Successful!</CardTitle>
              <CardDescription className="text-base mt-2">
                {subscription ? (
                  <>
                    You are now subscribed to the{" "}
                    <Badge variant="outline" className="font-semibold text-sm ml-1 mr-1">
                      {subscription.planId && SUBSCRIPTION_PLANS[subscription.planId]
                        ? SUBSCRIPTION_PLANS[subscription.planId].name
                        : "Selected"}{" "}
                      Plan
                    </Badge>
                  </>
                ) : (
                  "Your subscription has been processed"
                )}
              </CardDescription>
            </CardHeader>

            <CardContent>
              <div className="bg-muted/30 rounded-lg p-6 mb-6">
                <p className="mb-4">
                  Thank you for your subscription! You now have access to all the features included in your plan.
                </p>

                {subscription && (
                  <div className="flex flex-col md:flex-row justify-between items-center gap-4 mt-6">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Calendar className="h-5 w-5 text-primary" />
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-medium">Next Renewal</p>
                        <p className="text-sm text-muted-foreground">
                          {subscription.currentPeriodEnd
                            ? new Date(subscription.currentPeriodEnd).toLocaleDateString(undefined, {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })
                            : "Next billing cycle"}
                        </p>
                      </div>
                    </div>

                    {subscription.paymentMethod && (
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                          <CreditCard className="h-5 w-5 text-primary" />
                        </div>
                        <div className="text-left">
                          <p className="text-sm font-medium">Payment Method</p>
                          <p className="text-sm text-muted-foreground capitalize">
                            {subscription.paymentMethod}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="flex flex-col gap-3">
                <Button
                  className="w-full py-6 gap-2"
                  onClick={() => router.push("/dashboard")}
                  size="lg"
                >
                  Go to Dashboard
                  <ArrowRight className="h-4 w-4" />
                </Button>

                {subscription && subscription.planId !== 'starter' && (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => router.push("/dashboard/subscription/manage")}
                  >
                    Manage Subscription
                  </Button>
                )}
              </div>
            </CardContent>
          </>
        )}
      </Card>
    </div>
  );
}

// Export the main component with Suspense
export default function SubscriptionSuccessPage() {
  return (
    <Suspense fallback={<div className="container max-w-md py-12">Loading...</div>}>
      <SubscriptionSuccessContent />
    </Suspense>
  );
}
