"use client";
import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import React from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Mail, Phone, Calendar, Database, Plus, Edit, Save, Trash } from "lucide-react";
import { useLoading } from "@/lib/providers/LoadingProvider";
import { useLoadingState } from "@/lib/hooks/useLoadingState";
import { SectionLoader } from "@/components/ui/global-loader";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import {
  useGetLeadByIdQuery,
  useAddNotesMutation,
} from "@/lib/store/services/leadsApi";
import { formatDate } from "@/utils/date";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { supabase } from "@/lib/supabaseClient";
import { useUpdateLeadMutation } from "@/lib/store/services/leadsApi";
import { toast } from "sonner";
import { useGetActiveWorkspaceQuery } from "@/lib/store/services/workspace";
import { useGetStatusQuery } from "@/lib/store/services/status";
import { Input } from "@/components/ui/input";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { extractUserNameAndTimestamp } from "@/utils/message";
import { Player } from "@lottiefiles/react-lottie-player";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";

const IndividualLeadPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const isCollapsed = useSelector(
    (state: RootState) => state.sidebar.isCollapsed
  );
  const [expandedItems, setExpandedItems] = useState<{
    [key: number]: boolean;
  }>({});
  const [updateLead] = useUpdateLeadMutation();
  const [addNotes] = useAddNotesMutation();
  const leadId = params?.id as string;
  const {
    data: leadsData,
    isLoading,
    error,
  } = useGetLeadByIdQuery(
    { id: leadId },
    {
      pollingInterval: 5000, // 2 seconds
    }
  );
  const currentLead = leadsData?.data?.[0];
  const [newNote, setNewNote] = useState("");
  const [user, setUser] = useState<any>(null);
  const [notes, setNotes] = useState<Array<{ message: string }>>([]);
  const { data: activeWorkspace, isLoading: isLoadingWorkspace } =
    useGetActiveWorkspaceQuery();
  const workspaceId = activeWorkspace?.data.id;
  // Type the notes state properly
  const { data: statusData, isLoading: isLoadingStatus }: any =
    useGetStatusQuery(workspaceId);

  // Custom fields state
  const [newCustomField, setNewCustomField] = useState({ key: "", value: "" });
  const [editingCustomField, setEditingCustomField] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState("");
  const [customFields, setCustomFields] = useState<Record<string, any>>({});

  useEffect(() => {
    // Set initial notes when lead data loads
    if (currentLead?.text_area) {
      setNotes(currentLead.text_area);
    }

    // Set initial custom fields
    if (currentLead?.custom_data) {
      setCustomFields(currentLead.custom_data);
    }
  }, [currentLead]);

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user?.user_metadata);
    };

    fetchUser();
  }, []);

  const handleGoBack = () => {
    router.push("/leads");
  };

  // Use our custom hook to manage loading state
  useLoadingState(
    [
      { isLoading },
      { isLoading: isLoadingWorkspace },
      { isLoading: isLoadingStatus }
    ],
    {
      message: "Loading lead details...",
      dependencies: [leadId]
    }
  );

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="p-6">
          <CardTitle className="text-red-500">
            Error loading lead data
          </CardTitle>
          <CardDescription>Please try again later</CardDescription>
          <Button className="mt-4" onClick={handleGoBack}>
            Back to Leads
          </Button>
        </Card>
      </div>
    );
  }

  // Show not found state
  if (!currentLead) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="p-6">
          <CardTitle>Lead not found</CardTitle>
          <CardDescription>
            The requested lead could not be found
          </CardDescription>
          <Button className="mt-4" onClick={handleGoBack}>
            Back to Leads
          </Button>
        </Card>
      </div>
    );
  }

  const handleAddNote = () => {
    if (newNote.trim()) {
      const author = user?.firstName || user?.name || "Unknown";
      const timestamp = new Date().toLocaleString(); // Or use .toISOString() for a standard format
      const newNoteText = `${newNote} (added by ${author} at ${timestamp})`;
      const newNoteObj = { message: newNoteText };
      const updatedNotes = [...notes, newNoteObj];

      setNotes(updatedNotes);
      setNewNote("");
      addNotes({ id: leadId, Note: updatedNotes });
    }
  };

  const handleStatusChange = async (id: number, value: string) => {
    try {
      const parsedStatus = JSON.parse(value);
      const result = await updateLead({
        id: leadId,
        leads: { status: parsedStatus },
        workspaceId: workspaceId as string,
      });
      toast.success("Status updated successfully");
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
    }
  };

  // Add a new custom field
  const handleAddCustomField = async () => {
    if (!newCustomField.key.trim()) {
      toast.error("Field name cannot be empty");
      return;
    }

    try {
      // Create updated custom_data object
      const updatedCustomData = {
        ...customFields,
        [newCustomField.key]: newCustomField.value,
      };

      // Update the lead with the new custom field
      await updateLead({
        id: leadId,
        leads: {
          custom_data: updatedCustomData
        },
        workspaceId: workspaceId as string,
      });

      // Update local state
      setCustomFields(updatedCustomData);
      setNewCustomField({ key: "", value: "" });
      toast.success("Custom field added successfully");
    } catch (error) {
      console.error("Error adding custom field:", error);
      toast.error("Failed to add custom field");
    }
  };

  // Save edited custom field
  const handleSaveCustomField = async (key: string) => {
    try {
      // Create updated custom_data object
      const updatedCustomData = {
        ...customFields,
        [key]: editedValue,
      };

      // Update the lead with the edited custom field
      await updateLead({
        id: leadId,
        leads: {
          custom_data: updatedCustomData
        },
        workspaceId: workspaceId as string,
      });

      // Update local state
      setCustomFields(updatedCustomData);
      setEditingCustomField(null);
      toast.success("Custom field updated successfully");
    } catch (error) {
      console.error("Error updating custom field:", error);
      toast.error("Failed to update custom field");
    }
  };

  // Delete custom field
  const handleDeleteCustomField = async (key: string) => {
    try {
      // Create a copy of custom fields without the deleted field
      const { [key]: removedField, ...remainingFields } = customFields;

      // Update the lead with the remaining custom fields
      await updateLead({
        id: leadId,
        leads: {
          custom_data: remainingFields
        },
        workspaceId: workspaceId as string,
      });

      // Update local state
      setCustomFields(remainingFields);
      toast.success("Custom field deleted successfully");
    } catch (error) {
      console.error("Error deleting custom field:", error);
      toast.error("Failed to delete custom field");
    }
  };

  const truncate = (text: string, length = 50) => {
    return text.length > length ? `${text.slice(0, length)}...` : text;
  };

  // Toggle the expanded state
  const handleToggle = (index: number) => {
    setExpandedItems((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const result = extractUserNameAndTimestamp(
    notes.map((note) => note?.message)
  );

  const sanitizedPhone = currentLead?.phone.replace(/\D/g, "");

  return (
    <div
      className={`transition-all duration-500 ease-in-out px-4 py-6 ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
        } w-auto overflow-hidden`}
    >
      <Card className="p-2">
        <CardHeader className="md:flex md:flex-row grid grid-cols-3  md:items-center md:justify-between">
          <div className="flex flex-col md:flex-row col-start-1 col-end-3 md:items-center gap-4">
            <CardTitle>{currentLead?.name}</CardTitle>
            <CardDescription className="my-2">
              <Badge className="text-[9px]">
                {currentLead?.lead_source_id}
              </Badge>
            </CardDescription>
            <div className="flex ">
              <button
                className=""
                onClick={() => {
                  window.open(`tel:${currentLead?.phone}`, "_blank");
                }}
              >
                <Player
                  autoplay
                  loop
                  src="https://res.cloudinary.com/dyiso4ohk/raw/upload/v1736332984/Call_o3ga1m.json"
                  className="fixed-player"
                  style={{
                    width: "50px",
                  }}
                />
              </button>
              <button
                className="p-2 rounded-full hover:bg-gray-100"
                onClick={() => {
                  window.open(`https://wa.me/${sanitizedPhone}`, "_blank");
                }}
              >
                <Player
                  autoplay
                  loop
                  src="https://res.cloudinary.com/dyiso4ohk/raw/upload/v1736331912/Whatsapp_vemsbg.json"
                  className="fixed-player"
                  style={{
                    width: "50px",
                  }}
                />
              </button>
            </div>
          </div>

          <div className="flex flex-col md:flex-row col-start-3 col-end-4 space-x-2 gap-4">
            <Button variant="outline" onClick={handleGoBack}>
              Back to Leads
            </Button>
            {/* <Button
              variant="outline"
              onClick={() => {
                const customFieldsTab = document.querySelector('[value="custom-fields"]') as HTMLElement;
                if (customFieldsTab) customFieldsTab.click();
              }}
              className="flex items-center"
            >
              <Database className="mr-2 h-4 w-4" /> Custom Fields
            </Button> */}
            <Select
              defaultValue={JSON.stringify({
                name: currentLead?.status?.name || "Pending",
                color: currentLead?.status?.color || "#ea1212",
              })}
              onValueChange={(value) =>
                handleStatusChange(currentLead.id, value)
              } // Uncomment and use for status change handler
            >
              <SelectTrigger
                className="group relative md:w-[200px]  overflow-hidden rounded-md border-0 bg-white px-4 py-3 shadow-lg transition-all duration-200 hover:-translate-y-0.5 hover:shadow-xl dark:bg-gray-800"
                style={{
                  outline: `2px solid ${currentLead?.status?.color || "gray"}`,
                }}
              >
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div
                      className="absolute -inset-1 rounded-lg bg-gray-400 opacity-20 blur-sm transition-opacity duration-200 group-hover:opacity-30"
                      style={{ backgroundColor: currentLead?.status?.color }}
                    />
                    <div
                      className="relative h-3 w-3 rounded-lg bg-gray-400"
                      style={{ backgroundColor: currentLead?.status?.color }}
                    />
                  </div>
                  <span className="text-sm font-medium">
                    {currentLead?.status?.name}
                  </span>
                </div>
              </SelectTrigger>

              <SelectContent className="overflow-hidden rounded-xl border-0 bg-white p-2 shadow-2xl dark:bg-gray-800">
                {statusData?.data.map(
                  (status: { name: string; color: string }) => (
                    <SelectItem
                      key={status.name}
                      value={JSON.stringify({
                        name: status?.name,
                        color: status?.color,
                      })}
                      className="cursor-pointer rounded-lg outline-none transition-colors focus:bg-transparent"
                    >
                      <div className="group flex items-center gap-3 rounded-lg p-2 transition-all hover:bg-gray-50 dark:hover:bg-gray-700/50">
                        <div className="relative">
                          {/* Glow effect */}
                          <div
                            className="absolute -inset-1 rounded-lg opacity-20 blur-sm transition-all duration-200 group-hover:opacity-40"
                            style={{ backgroundColor: status?.color }}
                          />
                          {/* Main dot */}
                          <div
                            className="relative h-3 w-3 rounded-lg transition-transform duration-200 group-hover:scale-110"
                            style={{ backgroundColor: status?.color }}
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                          {status.name}
                        </span>
                      </div>
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs defaultValue="overview">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="interactions">Interactions</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
              {/* <TabsTrigger value="custom-fields">Custom Fields</TabsTrigger> */}
            </TabsList>

            <TabsContent value="overview">
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="p-2">
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Mail className="mr-2 text-gray-500" size={20} />
                        <span>{currentLead?.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="mr-2 text-gray-500" size={20} />
                        <span>{currentLead?.phone}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar className="mr-2 text-gray-500" size={20} />
                        <span>{formatDate(currentLead?.created_at)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex justify-between items-center">
                      <span>Lead Information</span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setNewCustomField({ key: "", value: "" })}
                        className="flex items-center"
                        data-testid="add-custom-field-btn"
                      >
                        <Plus className="mr-2 h-4 w-4" /> Add Field
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {newCustomField.key !== undefined && (
                      <Card className="mb-4 p-4 border border-primary/30">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <label className="text-sm font-medium mb-1 block">Field Name</label>
                            <Input
                              placeholder="Enter field name"
                              value={newCustomField.key}
                              onChange={(e) => setNewCustomField(prev => ({ ...prev, key: e.target.value }))}
                            />
                          </div>
                          <div>
                            <label className="text-sm font-medium mb-1 block">Field Value</label>
                            <Input
                              placeholder="Enter field value"
                              value={newCustomField.value}
                              onChange={(e) => setNewCustomField(prev => ({ ...prev, value: e.target.value }))}
                            />
                          </div>
                        </div>
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setNewCustomField({ key: undefined, value: "" })}
                          >
                            Cancel
                          </Button>
                          <Button
                            size="sm"
                            onClick={handleAddCustomField}
                          >
                            Save Field
                          </Button>
                        </div>
                      </Card>
                    )}

                    {currentLead?.custom_data && (
                      <Card className="relative border-border/40">
                        <CardContent className="p-4 pt-0">
                          <ScrollArea className="h-[400px] pr-4">
                            <div className="w-full max-w-2xl mx-auto space-y-2 ">
                              {Object.entries(
                                currentLead?.custom_data || {}
                              ).map(([key, value], index) => (
                                <Card
                                  key={index}
                                  className="border-0 shadow-none bg-accent/40 hover:bg-accent/60 transition-colors"
                                >
                                  <div className="p-4">
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="font-medium">{key}</span>
                                      <div className="flex space-x-2">
                                        {editingCustomField === key ? (
                                          <>
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              onClick={() => setEditingCustomField(null)}
                                            >
                                              Cancel
                                            </Button>
                                            <Button
                                              size="sm"
                                              onClick={() => handleSaveCustomField(key)}
                                            >
                                              <Save className="h-4 w-4" />
                                            </Button>
                                          </>
                                        ) : (
                                          <>
                                            <Button
                                              variant="outline"
                                              size="sm"
                                              onClick={() => {
                                                setEditingCustomField(key);
                                                setEditedValue(value as string);
                                              }}
                                            >
                                              <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                              variant="destructive"
                                              size="sm"
                                              onClick={() => handleDeleteCustomField(key)}
                                            >
                                              <Trash className="h-4 w-4" />
                                            </Button>
                                          </>
                                        )}
                                      </div>
                                    </div>

                                    {editingCustomField === key ? (
                                      <Input
                                        value={editedValue}
                                        onChange={(e) => setEditedValue(e.target.value)}
                                        className="w-full"
                                      />
                                    ) : (
                                      <p className="text-[14px] text-gray-800 leading-relaxed bg-white p-2 rounded-md shadow-sm">
                                        {typeof value === 'string' ? value : JSON.stringify(value)}
                                      </p>
                                    )}
                                  </div>
                                </Card>
                              ))}
                            </div>
                          </ScrollArea>
                        </CardContent>
                      </Card>
                    )}
                  </CardContent>
                </Card>
              </div>

              <div className="mt-6 flex items-center space-x-4">
                <Badge
                  variant="secondary"
                  style={{ backgroundColor: currentLead?.status?.color }}
                >
                  Status: {currentLead?.status?.name || "Pending"}
                </Badge>
              </div>
            </TabsContent>
            <TabsContent value="interactions">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                {/* <TableBody>
                  {lead.interactions.map((interaction, index) => (
                    <TableRow key={index}>
                      <TableCell>{interaction.date}</TableCell>
                      <TableCell>{interaction.type}</TableCell>
                      <TableCell>{interaction.notes}</TableCell>
                    </TableRow>
                  ))}
                </TableBody> */}
              </Table>
            </TabsContent>
            <TabsContent value="notes">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Textarea
                    placeholder="Add a note..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    rows={6}
                    className="w-full resize-vertical"
                  />
                  <Button
                    onClick={handleAddNote}
                    className="mt-2"
                    variant="default"
                  >
                    Add Note
                  </Button>
                </div>

                <div className="grid gap-3">
                  {/* Header */}
                  <div className="grid grid-cols-12 gap-2 bg-primary text-white p-4 rounded-lg">
                    <div className="col-span-4 font-semibold">Author</div>
                    <div className="col-span-4 font-semibold pl-2">
                      Message
                    </div>
                    <div className="col-span-4 font-semibold text-right">
                      Timestamp
                    </div>
                  </div>

                  {/* Notes */}
                  {result.map((noteItem, index) => (
                    <Tooltip key={index}>
                      <TooltipTrigger asChild>
                        <div className="group grid grid-cols-12 gap-2 items-center rounded-lg border border-border bg-card p-4 transition-all duration-200 hover:bg-accent hover:border-accent">
                          <div className="col-span-4 text-sm text-muted-foreground">
                            {noteItem?.userName || "Not Added"}
                          </div>
                          <div className="col-span-4 text-sm text-muted-foreground break-all pl-2">
                            {noteItem?.message || "Not Added"}
                          </div>
                          <div className="col-span-4 text-sm text-muted-foreground text-right">
                            {noteItem?.timestamp || "Not Added"}
                          </div>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="bg-popover">
                        <p className="text-popover-foreground">
                          Click to copy
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                </div>
              </div>
            </TabsContent>

            {/* Custom Fields Tab */}
            {/* <TabsContent value="custom-fields">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Add Custom Field</CardTitle>
                    <CardDescription>
                      Add custom fields to store additional information about this lead
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium mb-1 block">Field Name</label>
                          <Input
                            placeholder="Enter field name"
                            value={newCustomField.key}
                            onChange={(e) => setNewCustomField(prev => ({ ...prev, key: e.target.value }))}
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium mb-1 block">Field Value</label>
                          <Input
                            placeholder="Enter field value"
                            value={newCustomField.value}
                            onChange={(e) => setNewCustomField(prev => ({ ...prev, value: e.target.value }))}
                          />
                        </div>
                      </div>
                      <Button
                        onClick={handleAddCustomField}
                        className="w-full md:w-auto"
                      >
                        <Plus className="mr-2 h-4 w-4" /> Add Field
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Custom Fields</CardTitle>
                    <CardDescription>
                      View and manage custom fields for this lead
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {Object.keys(customFields).length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        No custom fields added yet
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {Object.entries(customFields).map(([key, value]) => (
                          <Card key={key} className="p-4 border border-border/40">
                            <div className="flex justify-between items-center">
                              <div className="font-medium">{key}</div>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setEditingCustomField(key);
                                    setEditedValue(value as string);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  onClick={() => handleDeleteCustomField(key)}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>

                            {editingCustomField === key ? (
                              <div className="mt-2 flex space-x-2">
                                <Input
                                  value={editedValue}
                                  onChange={(e) => setEditedValue(e.target.value)}
                                  className="flex-1"
                                />
                                <Button
                                  onClick={() => handleSaveCustomField(key)}
                                  size="sm"
                                >
                                  <Save className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="mt-2 text-gray-600 break-words">
                                {typeof value === 'string' ? value : JSON.stringify(value)}
                              </div>
                            )}
                          </Card>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent> */}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default IndividualLeadPage;
