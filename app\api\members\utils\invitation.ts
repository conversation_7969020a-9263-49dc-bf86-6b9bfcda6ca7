import crypto from 'crypto';

/**
 * Generate a secure invitation token
 */
export function createInvitationToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * Validate email format
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
}

/**
 * Check if invitation token is valid and not expired
 */
export async function validateInvitationToken(token: string) {
  const { supabase } = await import('@/lib/supabaseServer');
  
  const { data: invitation, error } = await supabase
    .from('workspace_invitations')
    .select(`
      id,
      workspace_id,
      email,
      role,
      expires_at,
      accepted_at,
      workspaces (
        id,
        name,
        status
      )
    `)
    .eq('token', token)
    .single();
  
  if (error || !invitation) {
    return { valid: false, error: 'Invalid invitation token' };
  }
  
  if (invitation.accepted_at) {
    return { valid: false, error: 'Invitation has already been accepted' };
  }
  
  if (new Date(invitation.expires_at) < new Date()) {
    return { valid: false, error: 'Invitation has expired' };
  }
  
  if (invitation.workspaces?.status !== 'active') {
    return { valid: false, error: 'Workspace is not active' };
  }
  
  return { 
    valid: true, 
    invitation: {
      id: invitation.id,
      workspaceId: invitation.workspace_id,
      email: invitation.email,
      role: invitation.role,
      workspaceName: invitation.workspaces?.name
    }
  };
}

/**
 * Accept an invitation and create workspace membership
 */
export async function acceptInvitation(token: string, userId: string, userEmail: string) {
  const { supabase } = await import('@/lib/supabaseServer');
  
  // Validate token first
  const validation = await validateInvitationToken(token);
  if (!validation.valid) {
    return { success: false, error: validation.error };
  }
  
  const invitation = validation.invitation!;
  
  // Verify email matches
  if (invitation.email.toLowerCase() !== userEmail.toLowerCase()) {
    return { success: false, error: 'Email mismatch' };
  }
  
  try {
    // Start transaction
    const { data: member, error: memberError } = await supabase
      .from('workspace_members')
      .upsert({
        workspace_id: invitation.workspaceId,
        user_id: userId,
        email: userEmail.toLowerCase(),
        role: invitation.role,
        status: 'accepted',
        is_active: false // Will be set to true if it's their first workspace
      }, {
        onConflict: 'user_id,workspace_id'
      })
      .select()
      .single();
    
    if (memberError) {
      console.error('Error creating workspace member:', memberError);
      return { success: false, error: 'Failed to create workspace membership' };
    }
    
    // Mark invitation as accepted
    const { error: invitationError } = await supabase
      .from('workspace_invitations')
      .update({ 
        accepted_at: new Date().toISOString() 
      })
      .eq('id', invitation.id);
    
    if (invitationError) {
      console.error('Error updating invitation:', invitationError);
      // Don't fail the whole process for this
    }
    
    // Check if this is the user's first workspace
    const { count: workspaceCount } = await supabase
      .from('workspace_members')
      .select('id', { count: 'exact' })
      .eq('user_id', userId)
      .eq('status', 'accepted');
    
    // If this is their first workspace, make it active
    if (workspaceCount === 1) {
      await supabase
        .from('workspace_members')
        .update({ is_active: true })
        .eq('id', member.id);
    }
    
    // Create audit log
    const { createAuditLog } = await import('./audit');
    await createAuditLog({
      workspaceId: invitation.workspaceId,
      action: 'accepted',
      userId,
      email: userEmail,
      newRole: invitation.role,
      performedBy: userId,
      metadata: {
        invitationId: invitation.id,
        acceptedAt: new Date().toISOString()
      }
    });
    
    return { 
      success: true, 
      member: {
        id: member.id,
        workspaceId: invitation.workspaceId,
        workspaceName: invitation.workspaceName,
        role: invitation.role
      }
    };
    
  } catch (error) {
    console.error('Error accepting invitation:', error);
    return { success: false, error: 'Internal server error' };
  }
}

/**
 * Decline an invitation
 */
export async function declineInvitation(token: string, userEmail: string) {
  const { supabase } = await import('@/lib/supabaseServer');
  
  // Validate token first
  const validation = await validateInvitationToken(token);
  if (!validation.valid) {
    return { success: false, error: validation.error };
  }
  
  const invitation = validation.invitation!;
  
  // Verify email matches
  if (invitation.email.toLowerCase() !== userEmail.toLowerCase()) {
    return { success: false, error: 'Email mismatch' };
  }
  
  try {
    // Mark invitation as declined by setting accepted_at to a special value
    const { error } = await supabase
      .from('workspace_invitations')
      .update({ 
        accepted_at: '1970-01-01T00:00:00Z' // Special value to indicate declined
      })
      .eq('id', invitation.id);
    
    if (error) {
      console.error('Error declining invitation:', error);
      return { success: false, error: 'Failed to decline invitation' };
    }
    
    // Create audit log
    const { createAuditLog } = await import('./audit');
    await createAuditLog({
      workspaceId: invitation.workspaceId,
      action: 'declined',
      email: userEmail,
      performedBy: null, // No user ID since they declined
      metadata: {
        invitationId: invitation.id,
        declinedAt: new Date().toISOString()
      }
    });
    
    return { success: true };
    
  } catch (error) {
    console.error('Error declining invitation:', error);
    return { success: false, error: 'Internal server error' };
  }
}
