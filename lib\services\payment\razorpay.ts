import { PlanTier } from '@/lib/types/subscription';

declare global {
  interface Window {
    Razorpay: any;
  }
}

export interface RazorpayCheckoutOptions {
  planId: PlanTier;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  successCallback: (paymentId: string, orderId: string, signature: string) => void;
  failureCallback: (error: any) => void;
}

export const loadRazorpayScript = (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (window.Razorpay) {
      return resolve(true);
    }
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });
};

export const createRazorpayCheckoutSession = async (options: RazorpayCheckoutOptions) => {
  try {
    const { planId, customerId, customerName, customerEmail, successCallback, failureCallback } = options;

    // Input validation - industry best practice
    if (!planId) {
      console.error('Missing required parameter: planId');
      throw new Error('Missing required payment parameter: planId');
    }

    if (!customerName || !customerEmail) {
      console.error('Missing customer information:', { customerName, customerEmail });
      throw new Error('Missing customer information for payment');
    }

    // Load the Razorpay script if not already loaded
    console.log('Loading Razorpay SDK...');
    const isLoaded = await loadRazorpayScript();
    if (!isLoaded) {
      console.error('Failed to load Razorpay SDK');
      throw new Error('Failed to load payment gateway. Please check your internet connection and try again.');
    }
    console.log('Razorpay SDK loaded successfully');

    // Call backend API to create a Razorpay order with proper error handling
    console.log('Creating Razorpay order for plan:', planId);
    let response;
    try {
      response = await fetch('/api/payments/razorpay/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          customerId,
        }),
      });
    } catch (fetchError) {
      console.error('Network error creating Razorpay order:', fetchError);
      throw new Error('Network error. Please check your internet connection and try again.');
    }

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error('Failed to create Razorpay order:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      throw new Error(`Payment initialization failed (${response.status}): ${response.statusText}`);
    }

    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('Error parsing Razorpay order response:', jsonError);
      throw new Error('Invalid response from payment gateway');
    }

    if (!data || !data.id || !data.amount || !data.currency) {
      console.error('Invalid response from Razorpay order creation:', data);
      throw new Error('Invalid payment gateway response. Please try again later.');
    }

    console.log('Razorpay order created successfully:', { orderId: data.id });

    // Initialize Razorpay checkout with industry best practices
    console.log('Initializing Razorpay checkout...');

    // Validate key exists
    if (!process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID) {
      console.error('Missing Razorpay key ID');
      throw new Error('Payment gateway configuration error. Please contact support.');
    }

    // Create checkout options with proper validation
    const checkoutOptions = {
      key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
      amount: data.amount,
      currency: data.currency,
      order_id: data.id,
      name: 'INFILABS CRM',
      description: `Subscription to ${planId.charAt(0).toUpperCase() + planId.slice(1)} Plan`,
      image: '/logo.svg',
      prefill: {
        name: customerName,
        email: customerEmail,
      },
      theme: {
        color: '#4f46e5', // Using indigo-600 for consistency with the app's color scheme
      },
      modal: {
        ondismiss: function() {
          console.log('Checkout form closed by user');
          failureCallback({ code: 'MODAL_CLOSED', description: 'Payment cancelled by user' });
        }
      },
      handler: function(response: any) {
        if (!response.razorpay_payment_id || !response.razorpay_order_id || !response.razorpay_signature) {
          console.error('Incomplete payment response:', response);
          failureCallback({ code: 'INCOMPLETE_RESPONSE', description: 'Incomplete payment response' });
          return;
        }

        console.log('Payment successful:', {
          paymentId: response.razorpay_payment_id,
          orderId: response.razorpay_order_id
        });

        successCallback(
          response.razorpay_payment_id,
          response.razorpay_order_id,
          response.razorpay_signature
        );
      },
    };

    // Create and configure Razorpay instance
    const razorpay = new window.Razorpay(checkoutOptions);

    // Set up event handlers with proper error handling
    razorpay.on('payment.failed', function(response: any) {
      console.error('Payment failed:', response.error);
      failureCallback(response.error || {
        code: 'PAYMENT_FAILED',
        description: 'Payment failed. Please try again.'
      });
    });

    // Open the checkout modal
    try {
      razorpay.open();
      console.log('Razorpay checkout opened successfully');
    } catch (openError) {
      console.error('Error opening Razorpay checkout:', openError);
      throw new Error('Failed to open payment gateway. Please try again.');
    }

    return { orderId: data.id, amount: data.amount, currency: data.currency };
  } catch (error) {
    console.error('Razorpay checkout error:', error);
    throw error;
  }
};

/**
 * Verifies a Razorpay payment with the server
 * @param paymentId - The Razorpay payment ID
 * @param orderId - The Razorpay order ID
 * @param signature - The Razorpay signature
 * @returns The verification result from the server
 */
export const verifyRazorpayPayment = async (
  paymentId: string,
  orderId: string,
  signature: string
) => {
  // Input validation - industry best practice
  if (!paymentId || !orderId || !signature) {
    console.error('Missing payment verification parameters:', { paymentId, orderId, signature });
    throw new Error('Missing payment verification parameters');
  }

  try {
    console.log('Verifying payment:', { paymentId, orderId });

    // Make the verification request with proper error handling
    let response;
    try {
      response = await fetch('/api/payments/razorpay/verify-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentId,
          orderId,
          signature,
        }),
      });
    } catch (fetchError) {
      console.error('Network error verifying payment:', fetchError);
      throw new Error('Network error during payment verification. Please check your connection.');
    }

    // Handle HTTP errors
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error('Failed to verify payment:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      throw new Error(`Payment verification failed (${response.status}): ${response.statusText}`);
    }

    // Parse the response with error handling
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('Error parsing payment verification response:', jsonError);
      throw new Error('Invalid response from payment verification');
    }

    // Validate the response data
    if (!data || data.error) {
      console.error('Invalid payment verification response:', data);
      throw new Error(data.error || 'Payment verification failed');
    }

    console.log('Payment verified successfully');
    return data;
  } catch (error) {
    console.error('Error verifying payment:', error);
    throw error;
  }
};
