"use client";

import React, { useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Filter, SquareCode, Plus } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";

// Hooks
import { useLeadsData } from "./hooks/useLeadsData";
import { useLeadFilters } from "./hooks/useLeadFilters";
import { useLeadSelection } from "./hooks/useLeadSelection";
import { useLeadActions } from "./hooks/useLeadActions";

// Components
import FilterComponent from "./filter";
import { LeadTable } from "./components/LeadTable";
import { LeadDialog } from "./components/LeadDialog";
import { DeleteDialog } from "./components/DeleteDialog";
import { BulkActions } from "./components/BulkActions";
import { ExportActions } from "./components/ExportActions";
import { Pagination } from "./components/Pagination";
import { LeadsSkeleton, LoadingMoreSkeleton } from "./components/LeadsSkeleton";

// API hooks
import { useGetWebhooksQuery } from "@/lib/store/services/webhooks";
import { useGetWorkspaceMembersQuery } from "@/lib/store/services/workspace";
import { useGetStatusQuery } from "@/lib/store/services/status";
import { useCreateManyLeadMutation } from "@/lib/store/services/leadsApi";

// Types
import { RootState } from "@/lib/store/store";

const LeadManagement: React.FC = () => {
  const router = useRouter();
  const isCollapsed = useSelector(
    (state: RootState) => state.sidebar.isCollapsed
  );

  // Search state
  const [searchQuery, setSearchQuery] = useState("");

  // Custom hooks
  const {
    leads,
    totalLeads,
    workspaceId,
    isLoadingLeads,
    isFetchingLeads,
    isLoadingWorkspace,
    currentPage,
    hasMore,
    sortField,
    sortDirection,
    handleSortChange,
    handleNextPage,
    handlePreviousPage,
    setLeads,
  } = useLeadsData();

  // Fetch additional data
  const { data: leadSources, isLoading: isLoadingLeadSources } = useGetWebhooksQuery({ id: workspaceId });
  const { data: workspaceMembers, isLoading: isLoadingMembers } = useGetWorkspaceMembersQuery(workspaceId);
  const { data: statusData, isLoading: isLoadingStatus } = useGetStatusQuery(workspaceId);
  const [createManyLead] = useCreateManyLeadMutation();

  // Filter hook
  const {
    filters,
    filteredLeads,
    showFilters,
    handleFilterChange,
    handleFilterReset,
    toggleFilters,
  } = useLeadFilters({ leads, leadSources, searchQuery });

  // Selection hook
  const {
    selectedLeads,
    toggleLeadSelection,
    deselectAll,
    toggleSelectAllOnPage,
    isAllSelected,
    setSelectedLeads,
  } = useLeadSelection({ leads: filteredLeads });

  // Actions hook
  const {
    dialogMode,
    expandedRow,
    form,
    isCreateLoading,
    isUpdateLoading,
    openCreateDialog,
    openEditDialog,
    resetDialog,
    onSubmit,
    handleDelete,
    handleStatusChange,
    handleAssignChange,
    handleView,
    toggleRow,
    initiateDirectContact,
    setDialogMode,
  } = useLeadActions({
    workspaceId,
    leads,
    setLeads,
    selectedLeads,
    setSelectedLeads,
  });

  // Handle import success
  const handleImportSuccess = (importedLeads: any[]) => {
    setLeads([...leads, ...importedLeads]);
  };

  // Check if any critical data is still loading
  const isInitialLoading = isLoadingWorkspace || isLoadingLeads || isLoadingStatus || isLoadingMembers || isLoadingLeadSources;

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return <LeadsSkeleton isCollapsed={isCollapsed} showFilters={showFilters} />;
  }

  // Early return for no leads
  if (!isLoadingLeads && leads.length === 0 && !searchQuery && Object.values(filters).every(v => !v)) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="max-w-lg w-full p-8 bg-white shadow-xl rounded-lg flex flex-col items-center">
          <CardTitle className="text-2xl font-semibold text-center text-gray-800">
            No Leads Found in this Workspace
          </CardTitle>
          <CardDescription className="mt-2 text-lg text-gray-600 text-center">
            It seems there are no leads available in this workspace at the moment.
          </CardDescription>
          <Button
            className="mt-6 px-6 py-2 bg-primary text-white rounded-md shadow-md hover:bg-primary-dark focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            onClick={() => router.push("/dashboard")}
          >
            Back to Dashboard
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className={`p-2 sm:p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} w-full max-w-full overflow-x-hidden`}>
      <Card className="w-full rounded-[8px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800 shadow-sm">
        <CardHeader className="p-3 sm:p-4 md:p-6 pb-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              <div className="flex items-center gap-2 sm:gap-3 min-w-0">
                <div className="bg-black/10 dark:bg-white/10 p-1.5 sm:p-2 rounded-full flex-shrink-0">
                  <SquareCode className="h-5 w-5 sm:h-6 sm:w-6 text-black dark:text-white" />
                </div>
                <div className="min-w-0">
                  <CardTitle className="text-lg sm:text-xl md:text-2xl font-bold text-black dark:text-white truncate">
                    Lead Management
                  </CardTitle>
                  <CardDescription className="text-sm text-black dark:text-white mt-1 hidden sm:block">
                    Manage and track your leads efficiently
                  </CardDescription>
                </div>
              </div>

              <div className="flex flex-col gap-2 sm:gap-3 w-full sm:w-auto">
                <div className="flex items-center gap-2 w-full">
                  <Input
                    placeholder="Search leads..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 sm:w-48 md:w-64 border-gray-200 dark:border-gray-800 text-sm"
                  />
                  <Button
                    variant="outline"
                    onClick={toggleFilters}
                    className="border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-900 flex-shrink-0"
                    size="sm"
                  >
                    <Filter className="h-4 w-4 text-black dark:text-white sm:mr-2" />
                    <span className="hidden sm:inline">{showFilters ? "Hide" : "Show"} Filters</span>
                  </Button>
                </div>

                <div className="flex items-center gap-2 w-full">
                  <div className="flex-1 sm:flex-initial">
                    <ExportActions
                      leads={filteredLeads}
                      leadSources={leadSources}
                      createManyLead={createManyLead}
                      workspaceId={workspaceId}
                      onImportSuccess={handleImportSuccess}
                    />
                  </div>
                  <Button
                    onClick={openCreateDialog}
                    className="bg-black hover:bg-black/90 text-white dark:bg-white dark:hover:bg-white/90 dark:text-black flex-shrink-0"
                    size="sm"
                  >
                    <Plus className="h-4 w-4 sm:mr-2" />
                    <span className="hidden sm:inline">Add Lead</span>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-800">
              <FilterComponent
                values={filters}
                onChange={handleFilterChange}
                onReset={handleFilterReset}
                leadSources={leadSources?.data || []}
                owner={workspaceMembers?.data || []}
                status={Array.isArray(statusData) ? statusData : []}
              />
            </div>
          )}
        </CardHeader>

        <CardContent className="p-3 sm:p-4 md:p-6">
          <BulkActions
            selectedLeads={selectedLeads}
            onDeselectAll={deselectAll}
            onSetDialogMode={setDialogMode}
          />

          <LeadTable
            leads={filteredLeads}
            selectedLeads={selectedLeads}
            expandedRow={expandedRow}
            sortField={sortField}
            sortDirection={sortDirection}
            workspaceMembers={workspaceMembers}
            statusData={{ data: Array.isArray(statusData) ? statusData : [] }}
            onToggleLeadSelection={toggleLeadSelection}
            onToggleSelectAll={toggleSelectAllOnPage}
            onSortChange={handleSortChange}
            onToggleRow={toggleRow}
            onEditLead={openEditDialog}
            onViewLead={handleView}
            onStatusChange={handleStatusChange}
            onAssignChange={handleAssignChange}
            onInitiateContact={initiateDirectContact}
            isAllSelected={isAllSelected}
          />

          {/* Loading more skeleton for infinite scroll */}
          {isFetchingLeads && currentPage > 0 && (
            <LoadingMoreSkeleton />
          )}

          <Pagination
            currentPage={currentPage}
            totalLeads={totalLeads}
            hasMore={hasMore}
            isLoading={isLoadingLeads}
            isFetching={isFetchingLeads}
            filteredLeadsCount={filteredLeads.length}
            onNextPage={handleNextPage}
            onPreviousPage={handlePreviousPage}
          />
        </CardContent>
      </Card>

      <LeadDialog
        isOpen={dialogMode === "create" || dialogMode === "edit"}
        mode={dialogMode}
        form={form}
        isLoading={isCreateLoading || isUpdateLoading}
        onClose={resetDialog}
        onSubmit={onSubmit}
      />

      <DeleteDialog
        isOpen={dialogMode === "delete"}
        selectedLeadsCount={selectedLeads.length}
        onClose={() => setDialogMode(null)}
        onConfirm={handleDelete}
      />
    </div>
  );
};

export default LeadManagement;