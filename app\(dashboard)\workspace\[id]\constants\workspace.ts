import { Building, Users, Lock, Bell, Tag, Tags } from "lucide-react";

export const WORKSPACE_CONSTANTS = {
  // Tab Configuration
  TABS: {
    GENERAL: 'general' as const,
    MEMBERS: 'members' as const,
    NOTIFICATIONS: 'notifications' as const,
    SECURITY: 'security' as const,
    STATUS: 'status' as const,
    TAGS: 'tags' as const,
  },

  // Default Values
  DEFAULTS: {
    STATUS_COLOR: '#0ea5e9',
    TAGS_COLOR: '#0ea5e9',
    ACTIVE_TAB: 'general',
    NEW_STATUS: {
      name: '',
      color: '#0ea5e9',
      countInStatistics: false,
      showInWorkspace: false,
      count_statistics: false,
    },
    NEW_TAGS: {
      name: '',
      color: '#0ea5e9',
    },
    WORKSPACE_SETTINGS: {
      name: '',
      industry: '',
      company_size: '',
      timezone: '',
      notifications: {
        email: false,
        sms: false,
        inApp: false,
      },
      security: {
        twoFactor: false,
        ipRestriction: false,
      },
    },
  },

  // Messages
  MESSAGES: {
    // Success Messages
    SETTINGS_SAVED: 'Settings saved successfully',
    STATUS_ADDED: 'Status added successfully',
    STATUS_UPDATED: 'Status updated successfully',
    STATUS_DELETED: 'Status deleted successfully',
    TAGS_ADDED: 'Tags added successfully',
    TAGS_UPDATED: 'Tags updated successfully',
    TAGS_DELETED: 'Tags deleted successfully',
    MEMBER_DELETED: 'Member deleted successfully',
    INVITE_RESENT: 'Invite resent successfully',

    // Error Messages
    SETTINGS_SAVE_ERROR: 'Failed to save settings',
    STATUS_ADD_ERROR: 'Failed to add status',
    STATUS_UPDATE_ERROR: 'Failed to update status',
    STATUS_DELETE_ERROR: 'Failed to delete status',
    STATUS_IN_USE_ERROR: 'This status is currently in use and cannot be deleted',
    TAGS_ADD_ERROR: 'Failed to add tags',
    TAGS_UPDATE_ERROR: 'Failed to update tags',
    TAGS_DELETE_ERROR: 'Failed to delete tags',
    MEMBER_DELETE_ERROR: 'Failed to delete member',
    INVITE_RESEND_ERROR: 'Failed to resend invite',
    INVALID_MEMBER_INFO: 'Invalid member information',
    UNEXPECTED_ERROR: 'An unexpected error occurred',

    // Validation Messages
    STATUS_NAME_REQUIRED: 'Status name is required',
    TAGS_NAME_REQUIRED: 'Tags name is required',
  },

  // UI Text
  UI_TEXT: {
    PAGE_TITLE: 'Workspace Settings',
    
    // Tab Labels
    TAB_GENERAL: 'General',
    TAB_MEMBERS: 'Members',
    TAB_NOTIFICATIONS: 'Notifications',
    TAB_SECURITY: 'Security',
    TAB_STATUS: 'Status',
    TAB_TAGS: 'Tags',

    // General Settings
    GENERAL_TITLE: 'General Settings',
    GENERAL_DESCRIPTION: 'Manage your workspace basic information and preferences',
    WORKSPACE_NAME: 'Workspace Name',
    INDUSTRY: 'Industry',
    COMPANY_SIZE: 'Company Size',
    TIMEZONE: 'Timezone',
    EDIT_BUTTON: 'Edit',
    SAVE_BUTTON: 'Save Changes',
    CANCEL_BUTTON: 'Cancel',
    SAVING_BUTTON: 'Saving...',

    // Status Management
    STATUS_TITLE: 'Status Management',
    STATUS_DESCRIPTION: 'Create and manage status options for your workspace',
    ADD_STATUS_BUTTON: 'Add New Status',
    STATUS_NAME_PLACEHOLDER: 'Status name',
    PICK_COLOR_LABEL: 'Pick Color:',
    COUNT_QUALIFIED_LABEL: 'Count As Qualified',
    EDIT_STATUS_TITLE: 'Edit Status',
    EDIT_STATUS_DESCRIPTION: 'Modify the status settings',
    DELETE_STATUS_TITLE: 'Delete Status',
    DELETE_STATUS_DESCRIPTION: 'Are you sure you want to delete the status "{name}"? This action cannot be undone.',
    ADD_STATUS_DIALOG_TITLE: 'Add New Status',
    ADD_STATUS_DIALOG_DESCRIPTION: 'Create a new status for your workspace',

    // Tags Management
    TAGS_TITLE: 'Tags Management',
    TAGS_DESCRIPTION: 'Create and manage Tags options for your workspace',
    ADD_TAGS_BUTTON: 'Add New Tag',
    TAGS_NAME_PLACEHOLDER: 'Tags name',
    EDIT_TAGS_TITLE: 'Edit Tags',
    EDIT_TAGS_DESCRIPTION: 'Modify the tags settings',
    DELETE_TAGS_TITLE: 'Delete Tags',
    DELETE_TAGS_DESCRIPTION: 'Are you sure you want to delete the tags "{name}"? This action cannot be undone.',
    ADD_TAGS_DIALOG_TITLE: 'Add New Tag',
    ADD_TAGS_DIALOG_DESCRIPTION: 'Create a new tag for your workspace',

    // Notifications
    NOTIFICATIONS_TITLE: 'Notification Settings',
    NOTIFICATIONS_DESCRIPTION: 'Configure how you receive notifications',
    EMAIL_NOTIFICATIONS: 'Email Notifications',
    SMS_NOTIFICATIONS: 'SMS Notifications',
    IN_APP_NOTIFICATIONS: 'In-App Notifications',

    // Security
    SECURITY_TITLE: 'Security Settings',
    SECURITY_DESCRIPTION: 'Manage your workspace security preferences',
    TWO_FACTOR_AUTH: 'Two-Factor Authentication',
    IP_RESTRICTION: 'IP Address Restriction',

    // Common
    DELETE_BUTTON: 'Delete',
    DELETING_BUTTON: 'Deleting...',
    ADD_BUTTON: 'Add',
    ADDING_BUTTON: 'Adding...',
    UPDATE_BUTTON: 'Update',
    UPDATING_BUTTON: 'Updating...',
    LOADING_TEXT: 'Loading...',
    NO_DATA_TEXT: 'No data available',
  },

  // Storage Keys
  STORAGE_KEYS: {
    ACTIVE_TAB: 'activeTab',
  },

  // Colors
  COLORS: {
    DEFAULT_STATUS: '#0ea5e9',
    DEFAULT_TAGS: '#0ea5e9',
  },

  // Validation
  VALIDATION: {
    MIN_NAME_LENGTH: 1,
    MAX_NAME_LENGTH: 50,
    COLOR_REGEX: /^#[0-9A-F]{6}$/i,
  },
} as const;

export const TAB_CONFIG = [
  { id: WORKSPACE_CONSTANTS.TABS.GENERAL, icon: Building, label: WORKSPACE_CONSTANTS.UI_TEXT.TAB_GENERAL },
  { id: WORKSPACE_CONSTANTS.TABS.MEMBERS, icon: Users, label: WORKSPACE_CONSTANTS.UI_TEXT.TAB_MEMBERS },
  { id: WORKSPACE_CONSTANTS.TABS.NOTIFICATIONS, icon: Bell, label: WORKSPACE_CONSTANTS.UI_TEXT.TAB_NOTIFICATIONS },
  { id: WORKSPACE_CONSTANTS.TABS.SECURITY, icon: Lock, label: WORKSPACE_CONSTANTS.UI_TEXT.TAB_SECURITY },
  { id: WORKSPACE_CONSTANTS.TABS.STATUS, icon: Tag, label: WORKSPACE_CONSTANTS.UI_TEXT.TAB_STATUS },
  { id: WORKSPACE_CONSTANTS.TABS.TAGS, icon: Tags, label: WORKSPACE_CONSTANTS.UI_TEXT.TAB_TAGS },
] as const;

export const INDUSTRY_OPTIONS = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Manufacturing',
  'Real Estate',
  'Consulting',
  'Marketing',
  'Other',
] as const;

export const COMPANY_SIZE_OPTIONS = [
  '1-10 employees',
  '11-50 employees',
  '51-200 employees',
  '201-500 employees',
  '501-1000 employees',
  '1000+ employees',
] as const;

export const TIMEZONE_OPTIONS = [
  'UTC-12:00',
  'UTC-11:00',
  'UTC-10:00',
  'UTC-09:00',
  'UTC-08:00',
  'UTC-07:00',
  'UTC-06:00',
  'UTC-05:00',
  'UTC-04:00',
  'UTC-03:00',
  'UTC-02:00',
  'UTC-01:00',
  'UTC+00:00',
  'UTC+01:00',
  'UTC+02:00',
  'UTC+03:00',
  'UTC+04:00',
  'UTC+05:00',
  'UTC+06:00',
  'UTC+07:00',
  'UTC+08:00',
  'UTC+09:00',
  'UTC+10:00',
  'UTC+11:00',
  'UTC+12:00',
] as const;
