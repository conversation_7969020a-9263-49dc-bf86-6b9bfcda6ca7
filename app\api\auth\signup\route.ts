import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Sign up a new user
 * @route POST /api/auth/signup
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { email, password, name } = body;
    
    if (!email || !password) {
      return NextResponse.json({ 
        error: "Email and password are required" 
      }, { status: 400 });
    }
    
    // Sign up with Supabase
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
        },
      },
    });
    
    if (error) {
      return NextResponse.json({ 
        error: error.message || AUTH_MESSAGES.SIGNUP_FAILED 
      }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Sign up successful. Please check your email for verification.",
      user: data.user,
      session: data.session,
    }, { status: 201 });
  } catch (error) {
    console.error("Error signing up:", error);
    return NextResponse.json({ 
      error: AUTH_MESSAGES.SIGNUP_FAILED 
    }, { status: 500 });
  }
}
