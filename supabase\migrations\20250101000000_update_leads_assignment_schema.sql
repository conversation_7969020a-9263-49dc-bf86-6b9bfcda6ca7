-- Migration to update leads table for ID-based status and member assignment
-- This migration adds new columns and preserves existing data

-- Add new columns for ID-based references
ALTER TABLE leads 
ADD COLUMN IF NOT EXISTS status_id INTEGER REFERENCES status(id),
ADD COLUMN IF NOT EXISTS assigned_to_id INTEGER REFERENCES workspace_members(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS leads_status_id_idx ON leads(status_id);
CREATE INDEX IF NOT EXISTS leads_assigned_to_id_idx ON leads(assigned_to_id);

-- Add comments for clarity
COMMENT ON COLUMN leads.status_id IS 'References status.id for lead status';
COMMENT ON COLUMN leads.assigned_to_id IS 'References workspace_members.id for lead assignment';

-- Note: The old 'status' (jsonb) and 'assign_to' (text) columns are kept for backward compatibility
-- They can be removed in a future migration after data migration is complete
