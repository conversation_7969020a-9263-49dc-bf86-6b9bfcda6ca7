import { v4 as uuidv4 } from "uuid";
import { LeadSource, LeadSourceFormData } from "../types/leadSources";

/**
 * Generates a webhook URL for a lead source
 */
export const generateWebhookUrl = (sourceId: string, workspaceId: string): string => {
  return `${process.env.NEXT_PUBLIC_BASE_URL}/leads?action=getLeads&sourceId=${sourceId}&workspaceId=${workspaceId}`;
};

/**
 * Generates a unique ID for a new lead source
 */
export const generateSourceId = (): string => {
  return uuidv4().toString();
};

/**
 * Formats a lead source for creation
 */
export const formatLeadSourceForCreation = (
  data: LeadSourceFormData,
  workspaceId: string
): Partial<LeadSource> => {
  const newId = generateSourceId();
  const webhookUrl = generateWebhookUrl(newId, workspaceId);

  return {
    id: newId,
    name: data.name,
    type: data.type,
    description: data.description || "",
    webhook_url: webhookUrl,
    workspace_id: workspaceId,
    status: true,
  };
};

/**
 * Formats a lead source for update
 */
export const formatLeadSourceForUpdate = (
  data: LeadSourceFormData,
  existingSource: LeadSource
): Partial<LeadSource> => {
  return {
    ...existingSource,
    name: data.name,
    type: data.type,
    description: data.description || "",
  };
};

/**
 * Copies text to clipboard
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    return false;
  }
};

/**
 * Truncates text to a specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};

/**
 * Gets the display label for a source type
 */
export const getSourceTypeLabel = (typeId: string): string => {
  const typeMap: Record<string, string> = {
    website: "Website",
    crm: "CRM",
    marketing: "Marketing",
    social: "Social Media",
    email: "Email Campaign",
    referral: "Referral",
    other: "Other",
  };
  
  return typeMap[typeId] || typeId;
};

/**
 * Validates if a workspace is selected
 */
export const validateWorkspace = (workspaceData: any): boolean => {
  return workspaceData?.data?.id !== undefined;
};

/**
 * Gets error message from API error
 */
export const getErrorMessage = (error: any, defaultMessage: string): string => {
  return error?.data?.error || defaultMessage;
};

/**
 * Formats lead source stats
 */
export const formatLeadSourceStats = (source: LeadSource) => {
  return {
    count: 0, // This would come from actual data
    processingRate: "N/A",
    qualificationRate: "N/A",
  };
};

/**
 * Checks if a lead source is active
 */
export const isSourceActive = (source: LeadSource): boolean => {
  return source.status === true;
};

/**
 * Sorts lead sources by name
 */
export const sortLeadSourcesByName = (sources: LeadSource[]): LeadSource[] => {
  return [...sources].sort((a, b) => a.name.localeCompare(b.name));
};

/**
 * Filters lead sources by type
 */
export const filterLeadSourcesByType = (sources: LeadSource[], type: string): LeadSource[] => {
  if (!type) return sources;
  return sources.filter(source => source.type === type);
};

/**
 * Filters lead sources by status
 */
export const filterLeadSourcesByStatus = (sources: LeadSource[], status: boolean): LeadSource[] => {
  return sources.filter(source => source.status === status);
};
