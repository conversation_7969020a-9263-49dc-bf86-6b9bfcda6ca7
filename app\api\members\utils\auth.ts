import { NextRequest } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Authenticates a user from the request's authorization header
 * @param request The Next.js request object
 * @returns Object containing the authenticated user or an error
 */
export async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  const token = authHeader.split(" ")[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  return { user, token };
}

/**
 * Checks if a user has permission to manage members in a workspace
 * @param userId The user ID to check
 * @param workspaceId The workspace ID to check permissions for
 * @returns Object indicating if the user has permission and any error
 */
export async function checkWorkspaceAdminPermission(userId: string, workspaceId: string) {
  try {
    // Check if the user is the owner of the workspace
    const { data: workspace, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();

    if (workspaceError) {
      return { 
        hasPermission: false, 
        error: workspaceError.message, 
        status: 500,
        isOwner: false
      };
    }

    // If user is the workspace owner, they have permission
    if (workspace.owner_id === userId) {
      return { 
        hasPermission: true, 
        error: null, 
        status: 200, 
        isOwner: true 
      };
    }

    // If not the owner, check if the user is an admin
    const { data: membership, error: membershipError } = await supabase
      .from("workspace_members")
      .select("role")
      .eq("workspace_id", workspaceId)
      .eq("user_id", userId)
      .single();

    if (membershipError) {
      return { 
        hasPermission: false, 
        error: membershipError.message, 
        status: 500,
        isOwner: false
      };
    }

    // Check if the user is an admin
    const isAdmin = membership?.role === "admin" || membership?.role === "SuperAdmin";

    if (!isAdmin) {
      return { 
        hasPermission: false, 
        error: "Only admins can manage members", 
        status: 403,
        isOwner: false
      };
    }

    return { 
      hasPermission: true, 
      error: null, 
      status: 200, 
      isOwner: false,
      isAdmin: true,
      role: membership.role
    };
  } catch (error) {
    console.error("Permission check error:", error);
    return { 
      hasPermission: false, 
      error: "Failed to check permissions", 
      status: 500,
      isOwner: false
    };
  }
}

/**
 * Checks if a user has permission to access a workspace
 * @param userId The user ID to check
 * @param workspaceId The workspace ID to check permissions for
 * @returns Object indicating if the user has permission and any error
 */
export async function checkWorkspacePermission(userId: string, workspaceId: string) {
  try {
    // Check if the user is the owner of the workspace
    const { data: workspace, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();

    if (workspaceError) {
      return { hasPermission: false, error: workspaceError.message, status: 500 };
    }

    // If user is the workspace owner, they have permission
    if (workspace.owner_id === userId) {
      return { 
        hasPermission: true, 
        error: null, 
        status: 200, 
        isOwner: true 
      };
    }

    // If not the owner, check if the user is a member
    const { data: membership, error: membershipError } = await supabase
      .from("workspace_members")
      .select("*")
      .eq("workspace_id", workspaceId)
      .eq("user_id", userId)
      .single();

    if (membershipError) {
      return { hasPermission: false, error: membershipError.message, status: 500 };
    }

    if (!membership) {
      return { 
        hasPermission: false, 
        error: "You are not a member of this workspace", 
        status: 403 
      };
    }

    return { 
      hasPermission: true, 
      error: null, 
      status: 200, 
      isOwner: false,
      role: membership.role
    };
  } catch (error) {
    console.error("Workspace permission check error:", error);
    return { hasPermission: false, error: "Failed to check workspace permissions", status: 500 };
  }
}
