import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../../utils/auth";
import { calculateWorkspaceAnalytics } from "../../utils/analytics";

/**
 * Get analytics for a workspace
 * @route GET /api/workspace/analytics/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const workspaceId = params.workspaceId;
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to view this workspace
    const permission = await checkWorkspacePermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Parse and validate workspace ID format
    const workspaceIdInt = parseInt(workspaceId);
    if (isNaN(workspaceIdInt)) {
      return NextResponse.json({ error: "Invalid workspace ID format" }, { status: 400 });
    }
    
    try {
      // Calculate analytics for the workspace
      const analytics = await calculateWorkspaceAnalytics(workspaceId);
      
      return NextResponse.json(analytics, { status: 200 });
    } catch (error) {
      console.error("Error calculating workspace analytics:", error);
      return NextResponse.json({ 
        error: "Failed to calculate workspace analytics" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching workspace analytics:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
