export interface LeadSource {
  webhook?: string;
  created_at?: string;
  description?: string;
  id: string;
  name: string;
  status: boolean;
  type: string;
  user_id?: string;
  webhook_url?: string;
  workspace_id?: string | null;
}

export interface SourceType {
  id: string;
  label: string;
}

export interface LeadSourceFormData {
  name: string;
  type: string;
  description?: string;
  status?: boolean;
}

export interface LeadSourceDialogMode {
  mode: "create" | "edit" | "delete" | null;
  source?: LeadSource | null;
}

export interface LeadSourceLoadingStates {
  isWorkspaceLoading: boolean;
  isSourcesLoading: boolean;
  isWebhookAdding: boolean;
  isDeleting: boolean;
  isUpdating: boolean;
  isStatusChanging: boolean;
}

export interface LeadSourceStats {
  count: number;
  processingRate: string;
  qualificationRate: string;
}

export interface WebhookResponse {
  data: LeadSource[];
  success: boolean;
  message?: string;
}

export interface WorkspaceData {
  data: {
    id: string;
    name: string;
  };
}
