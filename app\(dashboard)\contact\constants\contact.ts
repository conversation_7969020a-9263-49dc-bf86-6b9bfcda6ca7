export const CONTACT_CONSTANTS = {
  // Pagination
  PAGINATION: {
    DEFAULT_ITEMS_PER_PAGE: 10,
    ITEMS_PER_PAGE_OPTIONS: [10, 25, 50, 100],
  },

  // Polling
  POLLING: {
    INTERVAL: 10000, // 10 seconds
  },

  // Table Headers
  TABLE_HEADERS: [
    "Name",
    "Email", 
    "Phone",
    "Email Validation",
    "Platform",
    "Bussiness Info",
    "Tag",
    "Address",
  ],

  DEFAULT_SELECTED_HEADERS: [
    "Name",
    "Email",
    "Phone", 
    "Email Validation",
    "Platform",
    "Bussiness Info",
    "Tag",
  ],

  // Column Widths
  DEFAULT_COLUMN_WIDTHS: {
    Name: 150,
    Email: 200,
    Phone: 150,
    "Email Validation": 140,
    Platform: 170,
    "Bussiness Info": 180,
    Tag: 200,
    Address: 200,
  },

  // Contact Methods
  CONTACT_METHODS: {
    WHATSAPP: "WhatsApp",
    CALL: "Call", 
    SMS: "SMS",
  },

  // Status Options
  STATUS_OPTIONS: {
    ALL: "all",
    ACTIVE: "Active",
    INACTIVE: "Inactive", 
    PENDING: "Pending",
  },

  // Messages
  MESSAGES: {
    UPDATE_SUCCESS: "Update successfully",
    UPDATE_FAILED: "Update failed",
    LOADING: "Loading contacts...",
    NO_CONTACTS: "No contacts found",
    SEARCH_PLACEHOLDER: "Search contacts...",
    FILTER_PLACEHOLDER: "Filter by status",
    ADD_NAME_PLACEHOLDER: "Double-click to add name",
    ADD_EMAIL_PLACEHOLDER: "Double-click to add email", 
    ADD_PHONE_PLACEHOLDER: "Double-click to add phone",
    ADD_INFO_PLACEHOLDER: "Double-click to add info",
    ENTER_NAME: "Enter Name...",
    ENTER_EMAIL: "Enter Email...",
    ENTER_PHONE: "Enter Phone...",
    ENTER_BUSINESS_INFO: "Enter Business Info...",
  },

  // Form Defaults
  FORM_DEFAULTS: {
    NEW_CONTACT: {
      name: "",
      email: "",
      phone: "",
      status: "Active",
    },
    ADDRESS: {
      address1: "",
      address2: "",
      country: "",
      zipCode: "",
    },
  },

  // Validation
  VALIDATION: {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
  },

  // UI
  UI: {
    MOBILE_BREAKPOINT: 768,
    HOVER_MENU_OFFSET: 2,
    DROPDOWN_WIDTH: 140,
    RESIZABLE_HANDLE_WIDTH: 2,
  },
} as const;

export const CONTACT_ACTIONS = {
  SEARCH: "SEARCH",
  FILTER: "FILTER", 
  PAGINATE: "PAGINATE",
  EDIT: "EDIT",
  UPDATE: "UPDATE",
  ADD: "ADD",
  REMOVE: "REMOVE",
  SORT: "SORT",
  RESIZE_COLUMN: "RESIZE_COLUMN",
  TOGGLE_COLUMN: "TOGGLE_COLUMN",
} as const;

export const CONTACT_EDIT_MODES = {
  NAME: "name",
  EMAIL: "email",
  PHONE: "phone", 
  BUSINESS_INFO: "businessInfo",
  EMAIL_VALIDATION: "emailValidation",
  TAGS: "tags",
  ADDRESS: "address",
} as const;
