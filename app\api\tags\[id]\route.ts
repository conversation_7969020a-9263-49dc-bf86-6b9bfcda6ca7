import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkTagPermission } from "../utils/auth";

/**
 * Get a tag by ID
 * @route GET /api/tags/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Tag ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to view this tag
    const permission = await checkTagPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Fetch the tag
    const { data, error } = await supabase
      .from("tags")
      .select("*")
      .eq("id", id)
      .single();
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching tag:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Update a tag
 * @route PUT /api/tags/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Tag ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    const { updatedTags } = body;
    
    if (!updatedTags) {
      return NextResponse.json({ error: "Updated tag data is required" }, { status: 400 });
    }
    
    const { name, color, count_statistics } = updatedTags;
    
    // Check if user has permission to update this tag
    const permission = await checkTagPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Update the tag
    const { data, error } = await supabase
      .from("tags")
      .update({
        name: name,
        color: color,
        count_statistics: count_statistics,
      })
      .eq("id", id)
      .select();
    
    if (error) {
      console.error("Update error:", error);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Tag updated successfully",
      data
    }, { status: 200 });
  } catch (error) {
    console.error("Error updating tag:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Delete a tag
 * @route DELETE /api/tags/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Tag ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to delete this tag
    const permission = await checkTagPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Delete the tag
    const { error: deleteError } = await supabase
      .from("tags")
      .delete()
      .eq("id", id);
    
    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Tag deleted successfully"
    }, { status: 200 });
  } catch (error) {
    console.error("Error deleting tag:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
