// Updated to use the new API endpoint
import axios from "axios";

export interface EmailOptions {
  requestReadReceipt?: boolean;
  deliveryNotification?: boolean;
}

export const sendMail = async (
  to: string,
  subject: string,
  html: string,
  options: EmailOptions = {}
) => {
  try {
    // Call the new API endpoint
    const response = await axios.post("/api/email/send", {
      to,
      subject,
      html,
      options
    });

    if (!response.data.success) {
      throw new Error(response.data.error || "Failed to send email");
    }

    console.log("Message sent: %s", response.data.messageId);
    return { messageId: response.data.messageId };
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
};
