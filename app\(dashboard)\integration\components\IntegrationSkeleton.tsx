import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardHeader,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { IntegrationSkeletonProps } from "../types/integration";
import { TABLE_CONFIG } from "../constants/integration";

export const IntegrationSkeleton: React.FC<IntegrationSkeletonProps> = ({
  isCollapsed
}) => {
  return (
    <div
      className={`grid align-center gap-0 md:gap-2 md:rounded-none rounded-[4px] transition-all duration-500 ease-in-out px-2 py-6 w-auto
      ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"}
      overflow-hidden`}
    >
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden">
        {/* Header Skeleton */}
        <CardHeader className="flex flex-row justify-between items-center bg-gray-100 dark:bg-gray-800 md:bg-white md:dark:bg-gray-900">
          <div className="flex gap-6 items-center">
            <div className="md:hidden lg:hidden">
              <Skeleton className="w-4 h-4" />
            </div>
            <Skeleton className="h-6 w-48 md:h-8 md:w-64" />
          </div>

          {/* Search skeleton */}
          <div className="relative">
            <Skeleton className="h-10 w-full md:w-64" />
          </div>
        </CardHeader>

        {/* Filter buttons skeleton */}
        <div className="flex flex-wrap gap-2 px-4 py-2 bg-gray-50 dark:bg-gray-800">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="h-8 w-16" />
          ))}
        </div>

        <CardContent className="p-0">
          {/* Desktop Table Skeleton */}
          <div className="hidden md:block overflow-x-auto">
            <div className="border-b border-gray-200 dark:border-gray-800">
              {/* Table Header */}
              <div className="grid grid-cols-5 gap-4 py-3 px-4 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-24" />
              </div>

              {/* Table Rows */}
              {Array.from({ length: TABLE_CONFIG.SKELETON_ROWS }).map((_, index) => (
                <div key={index} className="grid grid-cols-5 gap-4 py-3 px-4 border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-8 w-20" />
                </div>
              ))}
            </div>
          </div>

          {/* Mobile Cards Skeleton */}
          <div className="md:hidden space-y-2 p-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <Skeleton className="h-5 w-32 mb-2" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-5 w-16 rounded-full" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-8 rounded-md" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
