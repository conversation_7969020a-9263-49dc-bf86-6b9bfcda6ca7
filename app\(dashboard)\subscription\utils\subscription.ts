import { differenceInDays, addDays, format } from 'date-fns';
import { UserSubscription, PlanTier } from '../types/subscription';
import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

/**
 * Calculate days remaining in subscription
 * @param subscription - User subscription data
 * @returns Number of days remaining (0 if expired)
 */
export function calculateDaysRemaining(subscription: UserSubscription | null): number {
  if (!subscription?.currentPeriodEnd) return 0;

  const endDate = new Date(subscription.currentPeriodEnd);
  const today = new Date();
  const days = differenceInDays(endDate, today);
  return days > 0 ? days : 0;
}

/**
 * Check if subscription is active
 * @param subscription - User subscription data
 * @returns Boolean indicating if subscription is active
 */
export function isSubscriptionActive(subscription: UserSubscription | null): boolean {
  if (!subscription) return false;
  
  const daysRemaining = calculateDaysRemaining(subscription);
  return subscription.status === 'active' && daysRemaining > 0;
}

/**
 * Check if subscription is expired
 * @param subscription - User subscription data
 * @returns Boolean indicating if subscription is expired
 */
export function isSubscriptionExpired(subscription: UserSubscription | null): boolean {
  if (!subscription) return false;
  
  const daysRemaining = calculateDaysRemaining(subscription);
  return daysRemaining === 0;
}

/**
 * Generate subscription end date
 * @param billingCycle - Monthly or yearly billing
 * @returns ISO date string for subscription end
 */
export function generateSubscriptionEndDate(billingCycle: 'monthly' | 'yearly'): string {
  const days = billingCycle === 'monthly' 
    ? SUBSCRIPTION_CONSTANTS.PLAN_DURATIONS.MONTHLY 
    : SUBSCRIPTION_CONSTANTS.PLAN_DURATIONS.YEARLY;
    
  return format(addDays(new Date(), days), "yyyy-MM-dd'T'HH:mm:ss'Z'");
}

/**
 * Create subscription object for free plan
 * @returns UserSubscription object for starter plan
 */
export function createFreeSubscription(): UserSubscription {
  return {
    planId: 'starter',
    status: 'active',
    currentPeriodEnd: format(
      addDays(new Date(), SUBSCRIPTION_CONSTANTS.PLAN_DURATIONS.FREE_PLAN), 
      "yyyy-MM-dd'T'HH:mm:ss'Z'"
    ),
    cancelAtPeriodEnd: false,
  };
}

/**
 * Create subscription object for paid plan
 * @param planId - Plan identifier
 * @param billingCycle - Billing cycle
 * @param paymentData - Payment information
 * @returns UserSubscription object
 */
export function createPaidSubscription(
  planId: PlanTier,
  billingCycle: 'monthly' | 'yearly',
  paymentData?: {
    paymentMethod?: 'stripe' | 'paypal' | 'razorpay';
    paymentId?: string;
    subscriptionId?: string;
  }
): UserSubscription {
  return {
    planId,
    status: 'active',
    currentPeriodEnd: generateSubscriptionEndDate(billingCycle),
    cancelAtPeriodEnd: false,
    ...paymentData,
  };
}

/**
 * Get subscription status display text
 * @param subscription - User subscription data
 * @param daysRemaining - Days remaining in subscription
 * @returns Status display text
 */
export function getSubscriptionStatusText(
  subscription: UserSubscription | null,
  daysRemaining: number | null
): string {
  if (!subscription) return 'No active subscription';
  
  if (daysRemaining !== null) {
    if (daysRemaining > 0) {
      return `Your subscription renews in ${daysRemaining} days`;
    } else {
      return 'Your subscription has expired';
    }
  }
  
  return `Subscription status: ${subscription.status}`;
}
