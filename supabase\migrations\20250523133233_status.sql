-- Migration for table: status
CREATE TABLE IF NOT EXISTS "status" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "name" text,
  "color" text,
  "work_id" integer,
  "user_id" uuid NOT NULL,
  "workspace_show" boolean,
  "count_statistics" boolean
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "status_user_id_idx" ON "status" ("user_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "status" ADD CONSTRAINT "fk_status_user_id"
--   FOREIGN KEY ("user_id") REFERENCES "auth.users" (id);

-- Enable Row Level Security
ALTER TABLE "status" ENABLE ROW LEVEL SECURITY;

