import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { Contact, ContactUpdateData, EditState, AddressData } from '../types/contact';
import { CONTACT_CONSTANTS } from '../constants/contact';
import { generateContactUrls, isValidUpdateData, handleTagOperations } from '../utils/contactUtils';
import { useUpdateLeadMutation } from '@/lib/store/services/leadsApi';

export function useContactActions() {
  const router = useRouter();
  const [updateLead, { isLoading: isUpdating }] = useUpdateLeadMutation();

  // Edit states
  const [editState, setEditState] = useState<EditState>({
    editNameId: null,
    editEmailId: null,
    editPhoneId: null,
    editInfoId: null,
    editEmailValidationId: null,
  });

  // Form states
  const [nameInfo, setNameInfo] = useState('');
  const [emailInfo, setEmailInfo] = useState('');
  const [phoneInfo, setPhoneInfo] = useState('');
  const [businessInfo, setBusinessInfo] = useState('');
  const [emailValidation, setEmailValidation] = useState(false);
  const [addressData, setAddressData] = useState<AddressData>(
    CONTACT_CONSTANTS.FORM_DEFAULTS.ADDRESS
  );

  // Dropdown states
  const [expandedRow, setExpandedRow] = useState<number | null>(null);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [dropdownOpenRemove, setDropdownOpenRemove] = useState<string | null>(null);

  // Tag management
  const [selectedTags, setSelectedTags] = useState<Record<string, string[]>>({});

  // Edit mode handlers
  const enterEditMode = useCallback((mode: keyof EditState, contactId: number, currentValue?: any) => {
    setEditState(prev => ({ ...prev, [mode]: contactId }));
    
    switch (mode) {
      case 'editNameId':
        setNameInfo(currentValue || '');
        break;
      case 'editEmailId':
        setEmailInfo(currentValue || '');
        break;
      case 'editPhoneId':
        setPhoneInfo(currentValue || '');
        break;
      case 'editInfoId':
        setBusinessInfo(currentValue || '');
        break;
      case 'editEmailValidationId':
        setEmailValidation(currentValue || false);
        break;
    }
  }, []);

  const exitEditMode = useCallback((mode: keyof EditState) => {
    setEditState(prev => ({ ...prev, [mode]: null }));
  }, []);

  // Update contact data
  const handleUpdate = useCallback(async (
    id: string | number,
    updatedData: ContactUpdateData
  ) => {
    if (!isValidUpdateData(updatedData)) {
      return;
    }

    try {
      await updateLead({
        id,
        leads: updatedData,
      }).unwrap();
      
      toast.success(CONTACT_CONSTANTS.MESSAGES.UPDATE_SUCCESS);
    } catch (error) {
      console.error('Update failed:', error);
      toast.error(CONTACT_CONSTANTS.MESSAGES.UPDATE_FAILED);
    }
  }, [updateLead]);

  // Contact method handlers
  const initiateDirectContact = useCallback((contact: Contact, method: string) => {
    const urls = generateContactUrls(contact);
    
    switch (method) {
      case CONTACT_CONSTANTS.CONTACT_METHODS.WHATSAPP:
        window.open(urls.whatsapp, '_blank');
        break;
      case CONTACT_CONSTANTS.CONTACT_METHODS.CALL:
        window.location.href = urls.call;
        break;
      case CONTACT_CONSTANTS.CONTACT_METHODS.SMS:
        window.location.href = urls.sms;
        break;
      default:
        break;
    }
  }, []);

  // Email handlers
  const sendEmail = useCallback((contact: Contact) => {
    const urls = generateContactUrls(contact);
    window.location.href = urls.email;
  }, []);

  const openInGmail = useCallback((contact: Contact) => {
    const urls = generateContactUrls(contact);
    window.open(urls.gmail, '_blank');
  }, []);

  // Navigation
  const handleView = useCallback((id: number) => {
    router.push(`/leads/${id}`);
  }, [router]);

  // Tag management
  const handleTagChange = useCallback((id: string, value: string) => {
    setSelectedTags(prev => {
      const currentTags = prev?.[id] ?? [];
      const updatedTags = handleTagOperations.add(currentTags, value);
      
      handleUpdate(id, { tags: updatedTags });
      
      return { ...prev, [id]: updatedTags };
    });
  }, [handleUpdate]);

  const handleRemoveTag = useCallback(async (contactId: string, tagToRemove: string) => {
    setSelectedTags(prev => {
      if (!prev || !prev[contactId]) return prev;

      const updatedTags = handleTagOperations.remove(prev[contactId], tagToRemove);
      
      handleUpdate(contactId, { tags: updatedTags.length ? updatedTags : [] });

      return {
        ...prev,
        [contactId]: updatedTags.length ? updatedTags : [],
      };
    });
  }, [handleUpdate]);

  // Row expansion
  const toggleRow = useCallback((id: number) => {
    setExpandedRow(prev => prev === id ? null : id);
  }, []);

  // Dropdown management
  const toggleDropdown = useCallback((header: string) => {
    setDropdownOpenRemove(prev => prev === header ? null : header);
  }, []);

  // Keyboard handlers
  const handleKeyDown = useCallback((
    e: React.KeyboardEvent,
    mode: keyof EditState,
    contactId: number,
    updateData: ContactUpdateData
  ) => {
    if (e.key === 'Enter') {
      handleUpdate(contactId, updateData);
      exitEditMode(mode);
    } else if (e.key === 'Escape') {
      exitEditMode(mode);
    }
  }, [handleUpdate, exitEditMode]);

  // Address management
  const updateAddressData = useCallback((contactId: number, contact: Contact) => {
    if (contactId) {
      setAddressData({
        address1: contact.address1 || '',
        address2: contact.address2 || '',
        country: contact.country || '',
        zipCode: contact.zipCode || '',
      });
    }
  }, []);

  return {
    // States
    editState,
    nameInfo,
    emailInfo,
    phoneInfo,
    businessInfo,
    emailValidation,
    addressData,
    expandedRow,
    openDropdownId,
    dropdownOpen,
    dropdownOpenRemove,
    selectedTags,
    isUpdating,

    // Setters
    setNameInfo,
    setEmailInfo,
    setPhoneInfo,
    setBusinessInfo,
    setEmailValidation,
    setAddressData,
    setExpandedRow,
    setOpenDropdownId,
    setDropdownOpen,
    setDropdownOpenRemove,
    setSelectedTags,

    // Actions
    enterEditMode,
    exitEditMode,
    handleUpdate,
    initiateDirectContact,
    sendEmail,
    openInGmail,
    handleView,
    handleTagChange,
    handleRemoveTag,
    toggleRow,
    toggleDropdown,
    handleKeyDown,
    updateAddressData,
  };
}
