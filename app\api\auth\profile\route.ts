import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../utils/auth";

/**
 * Get the current user's profile
 * @route GET /api/auth/profile
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get user profile data
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();
    
    if (profileError && profileError.code !== "PGRST116") { // PGRST116 is "no rows returned" error
      return NextResponse.json({ 
        error: profileError.message 
      }, { status: 400 });
    }
    
    // Combine auth user data with profile data
    const userData = {
      id: user.id,
      email: user.email,
      emailVerified: user.email_confirmed_at ? true : false,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      ...user.user_metadata,
      ...profile,
    };
    
    return NextResponse.json({ user: userData }, { status: 200 });
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return NextResponse.json({ 
      error: "Failed to fetch user profile" 
    }, { status: 500 });
  }
}
