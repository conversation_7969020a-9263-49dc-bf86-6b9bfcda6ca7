import { NextRequest } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Authenticates a user from the request's authorization header
 * @param request The Next.js request object
 * @returns Object containing the authenticated user or an error
 */
export async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  const token = authHeader.split(" ")[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  return { user, token };
}

/**
 * Checks if a user has permission to manage a webhook
 * @param userId The user ID to check
 * @param webhookId The webhook ID to check permissions for
 * @returns Object indicating if the user has permission and any error
 */
export async function checkWebhookPermission(userId: string, webhookId: string) {
  try {
    // Fetch the webhook details
    const { data: webhook, error: webhookError } = await supabase
      .from("webhooks")
      .select("user_id, workspace_id")
      .eq("id", webhookId)
      .single();

    if (webhookError) {
      return { hasPermission: false, error: webhookError.message, status: 500 };
    }

    if (!webhook) {
      return { hasPermission: false, error: "Webhook not found", status: 404 };
    }

    const { user_id, workspace_id } = webhook;

    // Check if the user owns the webhook
    if (user_id === userId) {
      return { hasPermission: true, error: null, status: 200, isOwner: true, workspace_id };
    }

    // If not the owner, check if the user is an admin in the workspace
    const { data: membership, error: membershipError } = await supabase
      .from("workspace_members")
      .select("role")
      .eq("workspace_id", workspace_id)
      .eq("user_id", userId)
      .single();

    if (membershipError) {
      return { hasPermission: false, error: membershipError.message, status: 500 };
    }

    const isAdmin = membership?.role === "admin" || membership?.role === "SuperAdmin";

    if (!membership || !isAdmin) {
      return { 
        hasPermission: false, 
        error: "You don't have permission to manage this webhook", 
        status: 403 
      };
    }

    return { hasPermission: true, error: null, status: 200, isOwner: false, workspace_id };
  } catch (error) {
    console.error("Permission check error:", error);
    return { hasPermission: false, error: "Failed to check permissions", status: 500 };
  }
}

/**
 * Checks if a user has permission to create a webhook in a workspace
 * @param userId The user ID to check
 * @param workspaceId The workspace ID to check permissions for
 * @returns Object indicating if the user has permission and any error
 */
export async function checkWorkspacePermission(userId: string, workspaceId: string) {
  try {
    // Check if the user is the owner of the workspace
    const { data: workspaceOwner, error: ownerError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();

    if (ownerError) {
      return { hasPermission: false, error: ownerError.message, status: 500 };
    }

    if (workspaceOwner?.owner_id === userId) {
      return { hasPermission: true, error: null, status: 200, isOwner: true };
    }

    // Check if the user is an admin in the workspace
    const { data: workspaceMember, error: workspaceError } = await supabase
      .from("workspace_members")
      .select("role")
      .eq("workspace_id", workspaceId)
      .eq("user_id", userId)
      .single();

    if (workspaceError) {
      return { hasPermission: false, error: workspaceError.message, status: 500 };
    }

    const isAdmin = workspaceMember?.role === "admin" || workspaceMember?.role === "SuperAdmin";

    if (!workspaceMember || !isAdmin) {
      return { 
        hasPermission: false, 
        error: "You don't have permission to create a webhook in this workspace", 
        status: 403 
      };
    }

    return { hasPermission: true, error: null, status: 200, isOwner: false };
  } catch (error) {
    console.error("Workspace permission check error:", error);
    return { hasPermission: false, error: "Failed to check workspace permissions", status: 500 };
  }
}
