import { useState, useCallback } from "react";
import { TAB_VALUES, EDITOR_TAB_VALUES } from "../constants/forms";

export const useFormState = () => {
  // Tab states
  const [activeTab, setActiveTab] = useState(TAB_VALUES.FORMS);
  const [editorTab, setEditorTab] = useState(EDITOR_TAB_VALUES.HTML);
  
  // Loading states
  const [formLoading, setFormLoading] = useState(false);
  
  // Preview state
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // Tab navigation functions
  const navigateToForms = useCallback(() => {
    setActiveTab(TAB_VALUES.FORMS);
  }, []);

  const navigateToEditor = useCallback(() => {
    setActiveTab(TAB_VALUES.EDITOR);
  }, []);

  const navigateToPreview = useCallback(() => {
    setActiveTab(TAB_VALUES.PREVIEW);
  }, []);

  // Editor tab functions
  const switchToHtmlTab = useCallback(() => {
    setEditorTab(EDITOR_TAB_VALUES.HTML);
  }, []);

  const switchToCssTab = useCallback(() => {
    setEditorTab(EDITOR_TAB_VALUES.CSS);
  }, []);

  const switchToJsTab = useCallback(() => {
    setEditorTab(EDITOR_TAB_VALUES.JS);
  }, []);

  // Preview functions
  const updatePreviewUrl = useCallback((url: string | null) => {
    setPreviewUrl(url);
  }, []);

  const clearPreviewUrl = useCallback(() => {
    setPreviewUrl(null);
  }, []);

  // Loading functions
  const startFormLoading = useCallback(() => {
    setFormLoading(true);
  }, []);

  const stopFormLoading = useCallback(() => {
    setFormLoading(false);
  }, []);

  // Reset all state
  const resetFormState = useCallback(() => {
    setActiveTab(TAB_VALUES.FORMS);
    setEditorTab(EDITOR_TAB_VALUES.HTML);
    setFormLoading(false);
    setPreviewUrl(null);
  }, []);

  return {
    // State
    activeTab,
    editorTab,
    formLoading,
    previewUrl,

    // Tab navigation
    setActiveTab,
    navigateToForms,
    navigateToEditor,
    navigateToPreview,

    // Editor tabs
    setEditorTab,
    switchToHtmlTab,
    switchToCssTab,
    switchToJsTab,

    // Preview management
    updatePreviewUrl,
    clearPreviewUrl,

    // Loading management
    startFormLoading,
    stopFormLoading,

    // Reset
    resetFormState,
  };
};
