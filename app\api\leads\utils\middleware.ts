import { NextRequest, NextResponse } from "next/server";
import Cors from "cors";

// Initialize CORS middleware
const cors = Cors({
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  origin: "*", // Allow all origins
  credentials: true,
});

/**
 * Runs middleware on a request
 * @param req The Next.js request
 * @param middleware The middleware function to run
 * @returns Promise that resolves when middleware is complete
 */
export function runMiddleware(
  req: NextRequest,
  middleware: any
): Promise<void> {
  return new Promise((resolve, reject) => {
    middleware(req, {
      end: (result: any) => {
        resolve(result);
      },
      setHeader: () => {},
      status: () => ({
        end: () => {},
        json: () => {},
      }),
    }, (result: any) => {
      if (result instanceof Error) {
        return reject(result);
      }
      return resolve(result);
    });
  });
}

/**
 * Applies CORS middleware to a request
 * @param req The Next.js request
 * @returns Promise that resolves when CORS middleware is complete
 */
export async function applyCors(req: NextRequest): Promise<void> {
  try {
    await runMiddleware(req, cors);
  } catch (error) {
    console.error("Error applying CORS middleware:", error);
  }
}
