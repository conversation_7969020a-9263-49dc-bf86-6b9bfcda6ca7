import { NextRequest } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Authenticates a user from the request's authorization header
 * @param request The Next.js request object
 * @returns Object containing the authenticated user or an error
 */
export async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  const token = authHeader.split(" ")[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  return { user, token };
}
