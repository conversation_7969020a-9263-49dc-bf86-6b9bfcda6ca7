import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, Check, Loader2, Send, MessageCircle, Phone } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { DialogMode, LeadFormData } from "../types/leads";
import { CONTACT_METHODS } from "../constants/leads";

interface LeadDialogProps {
  isOpen: boolean;
  mode: DialogMode;
  form: UseFormReturn<LeadFormData>;
  isLoading: boolean;
  onClose: () => void;
  onSubmit: (data: LeadFormData) => void;
}

export const LeadDialog: React.FC<LeadDialogProps> = ({
  isOpen,
  mode,
  form,
  isLoading,
  onClose,
  onSubmit,
}) => {
  const isCreateMode = mode === "create";
  const isEditMode = mode === "edit";

  if (!isCreateMode && !isEditMode) return null;

  const getContactMethodIcon = (method: string) => {
    switch (method) {
      case "WhatsApp":
        return <Send className="h-3.5 w-3.5 text-black dark:text-white" />;
      case "SMS":
        return <MessageCircle className="h-3.5 w-3.5 text-black dark:text-white" />;
      case "Call":
        return <Phone className="h-3.5 w-3.5 text-black dark:text-white" />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95%] max-w-lg p-0 overflow-hidden rounded-lg">
        <DialogHeader className="px-6 pt-6 pb-4 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800">
          <DialogTitle className="text-xl font-semibold text-black dark:text-white">
            {isCreateMode ? "Add New Lead" : "Edit Lead"}
          </DialogTitle>
          <p className="text-sm text-black dark:text-white mt-1">
            {isCreateMode
              ? "Enter the details to create a new lead"
              : "Update the lead information"}
          </p>
        </DialogHeader>

        <div className="px-6 py-5">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-black dark:text-white font-medium">
                      Full Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter full name"
                        {...field}
                        className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20"
                      />
                    </FormControl>
                    <FormMessage className="text-black dark:text-white" />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white font-medium">
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter email address"
                          type="email"
                          {...field}
                          className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20"
                        />
                      </FormControl>
                      <FormMessage className="text-black dark:text-white" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white font-medium">
                        Phone Number
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter phone number"
                          {...field}
                          className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20"
                        />
                      </FormControl>
                      <FormMessage className="text-black dark:text-white" />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white font-medium">
                        Company
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter company name"
                          {...field}
                          className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20"
                        />
                      </FormControl>
                      <FormMessage className="text-black dark:text-white" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white font-medium">
                        Position
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter job position"
                          {...field}
                          className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20"
                        />
                      </FormControl>
                      <FormMessage className="text-black dark:text-white" />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <FormField
                  control={form.control}
                  name="contact_method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white font-medium">
                        Contact Method
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20">
                            <SelectValue placeholder="Select contact method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="z-[1001] border-gray-200 dark:border-gray-800">
                          {CONTACT_METHODS.map((method) => (
                            <SelectItem key={method.value} value={method.value}>
                              <div className="flex items-center gap-2">
                                {getContactMethodIcon(method.value)}
                                <span>{method.label}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-black dark:text-white" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="revenue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-black dark:text-white font-medium">
                        Revenue
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter revenue amount"
                          type="number"
                          {...field}
                          value={field.value ? String(field.value) : ""}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value
                                ? parseFloat(e.target.value)
                                : undefined
                            )
                          }
                          className="border-gray-200 dark:border-gray-800 focus:ring-black/20 dark:focus:ring-white/20"
                        />
                      </FormControl>
                      <FormMessage className="text-black dark:text-white" />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter className="pt-4 border-t border-gray-200 dark:border-gray-800 mt-6">
                <DialogClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="border-gray-200 dark:border-gray-800 text-black dark:text-white"
                  >
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-black hover:bg-black/90 text-white dark:bg-white dark:hover:bg-white/90 dark:text-black"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span>Processing...</span>
                    </div>
                  ) : isCreateMode ? (
                    <div className="flex items-center gap-2">
                      <Plus className="h-4 w-4" />
                      <span>Add Lead</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Check className="h-4 w-4" />
                      <span>Update Lead</span>
                    </div>
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};
