import { ExampleWebhook, APIEndpoint, ComponentProp, IntegrationPlatform, FeatureCard, ButtonVariant, TextGradient, LoaderExample } from "../types/documentation";
import { Users, Webhook, <PERSON>tings, Laptop, Smartphone } from "lucide-react";

export const EXAMPLE_WEBHOOKS: ExampleWebhook[] = [
  {
    id: "1",
    name: "Website Contact Form",
    type: "Website",
    status: true,
    webhook_url: "https://example.com/leads?action=getLeads&sourceId=1&workspaceId=123",
    description: "Lead capture from the main website contact form"
  },
  {
    id: "2",
    name: "Facebook Campaign",
    type: "Social Media",
    status: true,
    webhook_url: "https://example.com/leads?action=getLeads&sourceId=2&workspaceId=123",
    description: "Lead generation from Facebook ad campaigns"
  }
] as const;

export const API_ENDPOINTS: APIEndpoint[] = [
  {
    endpoint: "/api/webhooks",
    method: "GET",
    description: "Retrieve all webhooks for a workspace"
  },
  {
    endpoint: "/api/webhooks",
    method: "POST",
    description: "Create a new webhook"
  },
  {
    endpoint: "/api/webhooks/{id}",
    method: "PUT",
    description: "Update an existing webhook"
  },
  {
    endpoint: "/api/webhooks/{id}",
    method: "DELETE",
    description: "Delete a webhook"
  },
  {
    endpoint: "/api/webhooks/{id}/status",
    method: "PATCH",
    description: "Toggle webhook status"
  }
] as const;

export const COMPONENT_PROPS: ComponentProp[] = [
  {
    prop: "onSourceCreated",
    type: "(source: Source) => void",
    default: "undefined",
    description: "Callback function when a new source is created"
  },
  {
    prop: "onSourceUpdated",
    type: "(source: Source) => void",
    default: "undefined",
    description: "Callback function when a source is updated"
  },
  {
    prop: "onSourceDeleted",
    type: "(id: string) => void",
    default: "undefined",
    description: "Callback function when a source is deleted"
  },
  {
    prop: "initialSources",
    type: "Source[]",
    default: "[]",
    description: "Initial sources to display"
  }
] as const;

export const INTEGRATION_PLATFORMS: IntegrationPlatform[] = [
  {
    category: "Form Builders",
    platforms: ["Typeform", "JotForm", "Google Forms", "Wufoo"]
  },
  {
    category: "CRM Systems",
    platforms: ["Salesforce", "HubSpot", "Zoho CRM", "Pipedrive"]
  },
  {
    category: "Marketing Platforms",
    platforms: ["Facebook Lead Ads", "LinkedIn Lead Gen Forms", "Twitter Lead Generation Cards", "Google Ads"]
  },
  {
    category: "Automation Tools",
    platforms: ["Zapier", "Make (Integromat)", "n8n", "IFTTT"]
  }
] as const;

export const FEATURE_CARDS: FeatureCard[] = [
  {
    title: "Lead Management",
    description: "Create, edit, and delete lead sources with custom names, types, and descriptions.",
    icon: Users
  },
  {
    title: "Webhook Integration",
    description: "Generate unique webhook URLs that can be integrated with external platforms.",
    icon: Webhook
  },
  {
    title: "Status Control",
    description: "Enable or disable sources with a toggle to control data flow.",
    icon: Settings
  },
  {
    title: "Responsive Design",
    description: "Optimized for both desktop and mobile devices with adaptive layouts.",
    icon: Laptop
  }
] as const;

export const BUTTON_VARIANTS: ButtonVariant[] = [
  { variant: "default", label: "Default" },
  { variant: "secondary", label: "Secondary" },
  { variant: "accent", label: "Accent" },
  { variant: "destructive", label: "Destructive" },
  { variant: "outline", label: "Outline" },
  { variant: "ghost", label: "Ghost" },
  { variant: "link", label: "Link" },
  { variant: "success", label: "Success" },
  { variant: "warning", label: "Warning" },
  { variant: "info", label: "Info" },
  { variant: "purple", label: "Purple" },
  { variant: "teal", label: "Teal" },
  { variant: "gradient", label: "Gradient" }
] as const;

export const GRADIENT_BUTTONS: ButtonVariant[] = [
  { variant: "", label: "Primary-Accent", className: "btn-gradient" },
  { variant: "", label: "Success", className: "btn-success" },
  { variant: "", label: "Warning", className: "btn-warning" },
  { variant: "", label: "Info", className: "btn-info" },
  { variant: "", label: "Purple", className: "btn-purple" },
  { variant: "", label: "Teal", className: "btn-teal" }
] as const;

export const TEXT_GRADIENTS: TextGradient[] = [
  {
    className: "text-gradient-primary",
    title: "Primary Gradient",
    description: "This text uses the primary color gradient."
  },
  {
    className: "text-gradient-accent",
    title: "Accent Gradient",
    description: "This text uses the accent color gradient."
  },
  {
    className: "text-gradient-primary-accent",
    title: "Primary to Accent",
    description: "This text transitions from primary to accent color."
  },
  {
    className: "text-gradient-purple",
    title: "Purple Gradient",
    description: "This text uses a purple color gradient."
  },
  {
    className: "text-gradient-teal",
    title: "Teal Gradient",
    description: "This text uses a teal color gradient."
  }
] as const;

export const LOADER_EXAMPLES: LoaderExample[] = [
  { size: "sm", variant: "primary", label: "Small Primary" },
  { size: "md", variant: "accent", label: "Medium Accent" },
  { size: "lg", variant: "secondary", label: "Large Secondary" }
] as const;

export const CODE_EXAMPLES = {
  DEPENDENCIES: `npm install @/components/ui lucide-react react-hook-form @hookform/resolvers zod uuid sonner`,
  IMPORT: `import LeadSourceManager from "@/components/LeadSourceManager";`,
  USAGE: `export default function LeadSourcesPage() {
  return (
    <div className="container mx-auto py-8">
      <LeadSourceManager />
    </div>
  );
}`,
  WEBHOOK_URL: `{BASE_URL}/leads?action=getLeads&sourceId={SOURCE_ID}&workspaceId={WORKSPACE_ID}`,
  WEBHOOK_PAYLOAD: `{
  "lead": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "company": "Acme Inc.",
    "message": "Interested in your services",
    "additionalFields": {
      "custom1": "value1",
      "custom2": "value2"
    }
  }
}`,
  TYPE_DEFINITION: `export type Source = {
  webhook?: string; // URL as a string
  created_at?: string; // ISO 8601 formatted date string
  description?: string; // String, can be empty
  id: string; // UUID
  name: string; // Name of the source
  status: boolean; // True or false, indicates the status
  type: string; // Type of the source (e.g., 'website')
  user_id?: string; // User UUID associated with the source
  webhook_url?: string; // URL as a string
  workspace_id?: string | null; // Can be a string or null
};`
} as const;

export const TAB_LABELS = {
  OVERVIEW: "Overview",
  INSTALLATION: "Installation",
  USAGE: "Usage Guide",
  INTEGRATION: "Integration",
  API: "API Reference",
  UI_COMPONENTS: "UI Components"
} as const;

export const COPY_TIMEOUT = 2000;
export const LOADING_SIMULATION_TIME = 2000;
