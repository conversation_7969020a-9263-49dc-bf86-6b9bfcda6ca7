@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Light mode - Refined Sales CRM palette */
    --background: 0 0% 100%;
    --foreground: 224 71% 4%;
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;

    /* Primary: Deep blue - professional, trustworthy */
    --primary: 224 76% 48%;
    --primary-foreground: 0 0% 100%;

    /* Secondary: Warm gray - sophisticated, balanced */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 26% 18%;

    /* Muted tones */
    --muted: 220 14% 96%;
    --muted-foreground: 220 14% 46%;

    /* Accent: Coral - energetic, attention-grabbing */
    --accent: 12 76% 61%;
    --accent-foreground: 0 0% 100%;

    /* Highlight: Mint - fresh, positive */
    --highlight: 160 84% 39%;
    --highlight-foreground: 0 0% 100%;

    /* Destructive: Refined crimson */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Borders and inputs */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 224 76% 48%;

    /* Hover states - ensuring visibility */
    --hover-light: 220 14% 96%;
    --hover-dark: 220 14% 20%;

    /* Chart colors - Refined dashboard palette */
    --chart-1: 224 76% 48%;  /* Blue */
    --chart-2: 12 76% 61%;   /* Coral */
    --chart-3: 160 84% 39%;  /* Mint */
    --chart-4: 271 91% 65%;  /* Purple */
    --chart-5: 35 91% 61%;   /* Amber */

    /* Status colors - Clear and refined */
    --success: 160 84% 39%;  /* Mint */
    --success-foreground: 0 0% 100%;
    --warning: 35 91% 61%;   /* Amber */
    --warning-foreground: 0 0% 100%;
    --info: 224 76% 48%;     /* Blue */
    --info-foreground: 0 0% 100%;
    --purple: 271 91% 65%;   /* Purple */
    --purple-foreground: 0 0% 100%;
    --coral: 12 76% 61%;     /* Coral */
    --coral-foreground: 0 0% 100%;

    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode - Refined Sales CRM palette with improved visibility */
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;
    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;
    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    /* Primary: Vibrant blue - stands out in dark mode */
    --primary: 213 94% 68%;
    --primary-foreground: 222 47% 11%;

    /* Secondary: Deep gray */
    --secondary: 217 19% 27%;
    --secondary-foreground: 210 40% 98%;

    /* Muted tones */
    --muted: 217 19% 27%;
    --muted-foreground: 215 20% 75%;

    /* Accent: Bright coral - pops against dark background */
    --accent: 12 76% 61%;
    --accent-foreground: 222 47% 11%;

    /* Highlight: Bright mint - pops against dark background */
    --highlight: 160 84% 39%;
    --highlight-foreground: 222 47% 11%;

    /* Destructive: Bright red */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    /* Borders and inputs */
    --border: 217 19% 27%;
    --input: 217 19% 27%;
    --ring: 213 94% 68%;

    /* Hover states - ensuring visibility */
    --hover-light: 220 14% 96%;
    --hover-dark: 213 30% 32%;

    /* Chart colors - Refined dark mode palette */
    --chart-1: 213 94% 68%;  /* Blue */
    --chart-2: 12 76% 61%;   /* Coral */
    --chart-3: 160 84% 49%;  /* Mint */
    --chart-4: 271 91% 65%;  /* Purple */
    --chart-5: 35 91% 61%;   /* Amber */

    /* Status colors - Bright and clear on dark */
    --success: 160 84% 49%;  /* Mint */
    --success-foreground: 222 47% 11%;
    --warning: 35 91% 61%;   /* Amber */
    --warning-foreground: 222 47% 11%;
    --info: 213 94% 68%;     /* Blue */
    --info-foreground: 222 47% 11%;
    --purple: 271 91% 65%;   /* Purple */
    --purple-foreground: 222 47% 11%;
    --coral: 12 76% 61%;     /* Coral */
    --coral-foreground: 222 47% 11%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced UI Elements for Sales CRM */
.gradient-heading {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)));
  font-weight: 700;
}

.feature-icon-container {
  background-image: linear-gradient(to bottom right, hsla(var(--primary), 0.2), hsla(var(--accent), 0.2));
  padding: 0.75rem;
  border-radius: 9999px;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.feature-icon-container:hover {
  background-image: linear-gradient(to bottom right, hsla(var(--primary), 0.3), hsla(var(--accent), 0.3));
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.feature-card {
  background-color: hsl(var(--card));
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid hsl(var(--border));
  transition: all 0.3s ease;
}

.feature-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: hsla(var(--primary), 0.2);
  transform: translateY(-2px);
}

.nav-item-active {
  background-image: linear-gradient(to right, hsla(var(--primary), 0.1), hsla(var(--accent), 0.1));
  color: hsl(var(--primary));
  border-left: 2px solid hsl(var(--primary));
  font-weight: 500;
}

/* Modern gradient buttons with improved hover states */
.btn-gradient {
  background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)));
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-gradient:hover {
  background-image: linear-gradient(to right, hsla(var(--primary), 0.9), hsla(var(--accent), 0.9));
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-success {
  background-image: linear-gradient(to right, #34D399, #10B981);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-success:hover {
  background-image: linear-gradient(to right, #10B981, #059669);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-warning {
  background-image: linear-gradient(to right, #FBBF24, #F59E0B);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-warning:hover {
  background-image: linear-gradient(to right, #F59E0B, #D97706);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-info {
  background-image: linear-gradient(to right, #60A5FA, #3B82F6);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-info:hover {
  background-image: linear-gradient(to right, #3B82F6, #2563EB);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-purple {
  background-image: linear-gradient(to right, #A78BFA, #8B5CF6);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-purple:hover {
  background-image: linear-gradient(to right, #8B5CF6, #7C3AED);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-coral {
  background-image: linear-gradient(to right, #FF8A80, #FF5252);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-coral:hover {
  background-image: linear-gradient(to right, #FF5252, #FF1744);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Sales-focused UI elements with improved visibility */
.metric-card {
  background-color: hsl(var(--card));
  padding: 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: hsla(var(--primary), 0.2);
}

.metric-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--accent)));
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: hsl(var(--foreground));
}

.metric-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
}

.metric-trend-up {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #10B981; /* Always visible green */
}

.dark .metric-trend-up {
  color: #34D399; /* Brighter green for dark mode */
}

.metric-trend-down {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  font-weight: 500;
  color: #EF4444; /* Always visible red */
}

.dark .metric-trend-down {
  color: #F87171; /* Brighter red for dark mode */
}

/* Premium Sales CRM Card Styles with improved visibility */
.stats-card {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.stats-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--accent)));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover::before {
  opacity: 1;
}

.dashboard-card {
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.dashboard-card:hover {
  border-color: hsla(var(--primary), 0.2);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
  transform: translateY(-3px);
}

.glass-card {
  background-color: hsla(var(--background), 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid hsla(var(--border), 0.5);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.premium-card {
  position: relative;
  overflow: hidden;
  border: none;
  border-radius: 0.75rem;
  padding: 1.5rem;
  background: linear-gradient(135deg,
    hsl(var(--card)) 0%,
    hsl(var(--card)) 100%
  );
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.08),
    0 0 0 1px hsla(var(--primary), 0.1);
}

.premium-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg,
    hsl(var(--primary)),
    hsl(var(--accent))
  );
}

/* Elegant card with subtle gradient */
.elegant-card {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: none;
  background: linear-gradient(
    135deg,
    hsl(var(--card)) 0%,
    hsl(var(--card)) 80%,
    hsla(var(--primary), 0.05) 100%
  );
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.06),
    0 0 0 1px hsla(var(--border), 0.5);
}

/* Premium Sales CRM Cards */
.lead-card {
  position: relative;
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.lead-card:hover {
  border-color: hsla(var(--primary), 0.3);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
  transform: translateY(-3px);
}

.opportunity-card {
  position: relative;
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-left: 4px solid hsl(var(--primary));
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.opportunity-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
  transform: translateX(3px);
}

.customer-card {
  position: relative;
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0.75rem;
  padding: 1.25rem;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
}

.customer-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.customer-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg,
    hsl(142, 72%, 29%),
    hsl(174, 84%, 32%)
  );
}

/* Premium Sales CRM Navigation and Sidebar with improved visibility */
.sidebar-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.375rem;
  padding: 0.625rem 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  color: hsl(var(--foreground));
}

.sidebar-item:hover {
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--primary));
  transform: translateX(2px);
}

.dark .sidebar-item {
  color: rgba(255, 255, 255, 0.9);
}

.dark .sidebar-item:hover {
  background-color: hsla(var(--primary), 0.2);
  color: white;
}

.sidebar-item-active {
  background-color: hsla(var(--primary), 0.15);
  color: hsl(var(--primary));
  font-weight: 500;
  position: relative;
}

.dark .sidebar-item-active {
  background-color: hsla(var(--primary), 0.25);
  color: white;
}

.sidebar-item-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, hsl(var(--primary)), hsl(var(--accent)));
  border-radius: 0 2px 2px 0;
}

/* Ensure sidebar icons are visible in dark mode */
.dark .sidebar-item svg {
  color: rgba(255, 255, 255, 0.8);
}

.dark .sidebar-item:hover svg {
  color: white;
}

.dark .sidebar-item-active svg {
  color: white;
}

/* Premium Sales CRM Form Elements */
.input-enhanced {
  background-color: hsla(var(--background), 1);
  border: 1px solid hsl(var(--input));
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.input-enhanced:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.1);
  transform: translateY(-1px);
}

.search-input {
  background-color: hsla(var(--background), 0.5);
  border: 1px solid hsl(var(--input));
  padding-left: 2.25rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  border-radius: 9999px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

.search-input:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Premium Sales CRM Button Styles */
.btn-3d {
  position: relative;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-weight: 500;
  box-shadow: 0 4px 0 0 rgba(0, 0, 0, 0.1);
  transform: translateY(0);
  transition: all 0.2s ease;
}

.btn-3d:active {
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.1);
  transform: translateY(4px);
}

.btn-sales {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-sales:hover {
  background-color: hsla(var(--primary), 0.9);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.btn-gradient-primary {
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--primary-foreground)));
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-gradient-primary:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Sales CRM Animated Elements */
.pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Premium Sales CRM Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-new {
  background-color: rgba(59, 130, 246, 0.1);
  color: rgb(37, 99, 235);
}

.dark .status-new {
  background-color: rgba(30, 64, 175, 0.3);
  color: rgb(96, 165, 250);
}

.status-active {
  background-color: rgba(16, 185, 129, 0.1);
  color: rgb(5, 150, 105);
}

.dark .status-active {
  background-color: rgba(6, 95, 70, 0.3);
  color: rgb(52, 211, 153);
}

.status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: rgb(217, 119, 6);
}

.dark .status-pending {
  background-color: rgba(146, 64, 14, 0.3);
  color: rgb(251, 191, 36);
}

.status-closed {
  background-color: rgba(100, 116, 139, 0.1);
  color: rgb(71, 85, 105);
}

.dark .status-closed {
  background-color: rgba(30, 41, 59, 0.6);
  color: rgb(148, 163, 184);
}

.status-lost {
  background-color: rgba(239, 68, 68, 0.1);
  color: rgb(220, 38, 38);
}

.dark .status-lost {
  background-color: rgba(127, 29, 29, 0.3);
  color: rgb(248, 113, 113);
}

/* Premium Sales CRM Text Gradient Utilities */
.text-gradient-primary {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, hsl(var(--primary)), hsla(var(--primary), 0.8));
  font-weight: 700;
}

.text-gradient-accent {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, hsl(var(--accent)), hsla(var(--accent), 0.8));
  font-weight: 700;
}

.text-gradient-primary-accent {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)));
  font-weight: 700;
}

.text-gradient-success {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #10B981, #059669);
  font-weight: 700;
}

.text-gradient-warning {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #F59E0B, #D97706);
  font-weight: 700;
}

.text-gradient-purple {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #8B5CF6, #7C3AED);
  font-weight: 700;
}

.text-gradient-teal {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, #14B8A6, #0D9488);
  font-weight: 700;
}

/* Premium Sales CRM Background Gradient Utilities */
.bg-gradient-primary {
  background-image: linear-gradient(to right, hsl(var(--primary)), hsla(var(--primary), 0.9));
}

.bg-gradient-accent {
  background-image: linear-gradient(to right, hsl(var(--accent)), hsla(var(--accent), 0.9));
}

.bg-gradient-primary-accent {
  background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)));
}

.bg-gradient-success {
  background-image: linear-gradient(to right, #10B981, #059669);
}

.bg-gradient-warning {
  background-image: linear-gradient(to right, #F59E0B, #D97706);
}

.bg-gradient-purple {
  background-image: linear-gradient(to right, #8B5CF6, #7C3AED);
}

.bg-gradient-teal {
  background-image: linear-gradient(to right, #14B8A6, #0D9488);
}

/* Sales CRM Special Backgrounds */
.bg-dashboard {
  background-color: hsl(var(--background));
  background-image:
    radial-gradient(at 100% 0%, hsla(var(--primary), 0.1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(var(--accent), 0.1) 0px, transparent 50%);
}

.bg-auth {
  background-color: hsl(var(--background));
  background-image:
    radial-gradient(at 0% 0%, hsla(var(--primary), 0.05) 0px, transparent 50%),
    radial-gradient(at 100% 100%, hsla(var(--accent), 0.05) 0px, transparent 50%);
}

/* Header Icon Fixes for Better Visibility */
.header-icon {
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
}

.header-icon:hover {
  color: hsl(var(--primary));
  background-color: hsla(var(--hover-light), 1);
}

.dark .header-icon {
  color: rgba(255, 255, 255, 0.9);
}

.dark .header-icon:hover {
  color: white;
  background-color: hsla(var(--hover-dark), 1);
}

/* Ensure header icons are visible in dark mode */
.dark .header-icon svg {
  color: rgba(255, 255, 255, 0.9);
}

.dark .header-icon:hover svg {
  color: white;
}

/* Notification Icon Specific Styles */
.notification-icon {
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
}

.notification-icon:hover {
  color: hsl(var(--primary));
  background-color: hsla(var(--hover-light), 1);
}

.dark .notification-icon {
  color: rgba(255, 255, 255, 0.9);
}

.dark .notification-icon:hover {
  color: white;
  background-color: hsla(var(--hover-dark), 1);
}

.dark .notification-icon svg {
  color: rgba(255, 255, 255, 0.9);
}

.dark .notification-icon:hover svg {
  color: white;
}

/* More Icon Specific Styles */
.more-icon {
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
}

.more-icon:hover {
  color: hsl(var(--primary));
  background-color: hsla(var(--hover-light), 1);
}

.dark .more-icon {
  color: rgba(255, 255, 255, 0.9);
}

.dark .more-icon:hover {
  color: white;
  background-color: hsla(var(--hover-dark), 1);
}

.dark .more-icon svg {
  color: rgba(255, 255, 255, 0.9);
}

.dark .more-icon:hover svg {
  color: white;
}
