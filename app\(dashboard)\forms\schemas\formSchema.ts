import { z } from "zod";
import { FORM_VALIDATION_RULES } from "../constants/forms";

export const formSchema = z.object({
  name: z.string().min(
    FORM_VALIDATION_RULES.NAME.MIN_LENGTH, 
    FORM_VALIDATION_RULES.NAME.MESSAGE
  ),
  description: z.string().optional(),
  html_content: z.string().min(
    FORM_VALIDATION_RULES.HTML_CONTENT.MIN_LENGTH, 
    FORM_VALIDATION_RULES.HTML_CONTENT.MESSAGE
  ),
  css_content: z.string().optional(),
  js_content: z.string().optional(),
  is_active: z.boolean().default(true),
  lead_source_id: z.string().optional(),
});

export const formSubmissionSchema = z.object({
  formId: z.string(),
  data: z.record(z.any()),
});

export const embedCodeSchema = z.object({
  formId: z.string(),
  width: z.string().optional(),
  height: z.string().optional(),
  style: z.string().optional(),
});

export type FormSchemaType = z.infer<typeof formSchema>;
export type FormSubmissionSchemaType = z.infer<typeof formSubmissionSchema>;
export type EmbedCodeSchemaType = z.infer<typeof embedCodeSchema>;
