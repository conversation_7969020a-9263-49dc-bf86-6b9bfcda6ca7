export const MEMBER_CONSTANTS = {
  // Roles
  ROLES: {
    ADMIN: 'admin' as const,
    MEMBER: 'member' as const,
    SUPER_ADMIN: 'SuperAdmin' as const,
  },
  
  // Status
  STATUS: {
    ACTIVE: 'active' as const,
    PENDING: 'pending' as const,
  },
  
  // Default values
  DEFAULTS: {
    ROLE: 'member' as const,
    PROFILE_IMAGE: '/api/placeholder/32/32',
  },
  
  // Messages
  MESSAGES: {
    INVITE_SUCCESS: 'Member invited successfully',
    INVITE_ERROR: 'Failed to invite member',
    UPDATE_SUCCESS: 'Role updated successfully',
    UPDATE_ERROR: 'Error updating member role',
    DELETE_SUCCESS: 'Member removed successfully',
    DELETE_ERROR: 'Failed to remove member',
    RESEND_SUCCESS: 'Invitation resent successfully',
    RESEND_ERROR: 'Failed to resend invitation',
    MEMBER_NOT_FOUND: 'Member not found!',
    EMAIL_REQUIRED: 'Email address is required',
    INVALID_EMAIL: 'Please enter a valid email address',
  },
  
  // UI Text
  UI_TEXT: {
    TITLE: 'Members & Invitations',
    DESCRIPTION: 'Manage workspace members and send invitations',
    INVITE_LABEL: 'Invite New Member',
    MEMBERS_LABEL: 'Current Members & Pending Invites',
    EMAIL_PLACEHOLDER: 'Email address',
    ROLE_PLACEHOLDER: 'Role',
    INVITE_BUTTON: 'Invite',
    INVITING_BUTTON: 'Inviting...',
    RESEND_BUTTON: 'Resend',
    RESENDING_BUTTON: 'Resending...',
    DELETE_BUTTON: 'Delete',
    DELETING_BUTTON: 'Deleting...',
    CANCEL_BUTTON: 'Cancel',
    EDIT_ROLE_TITLE: 'Edit User Role',
    DELETE_TITLE: 'Delete Member',
    DELETE_DESCRIPTION: 'Are you sure you want to remove {name} from the workspace? This action cannot be undone.',
    WORKSPACE_LABEL: 'Workspace:',
  },
  
  // Validation
  VALIDATION: {
    EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MAX_EMAIL_LENGTH: 254,
    MIN_EMAIL_LENGTH: 5,
  },
  
  // File upload
  FILE_UPLOAD: {
    ACCEPTED_TYPES: 'image/*',
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    SUPPORTED_FORMATS: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  },
  
  // Permissions
  PERMISSIONS: {
    CAN_EDIT_ROLES: ['admin', 'SuperAdmin'],
    CAN_DELETE_MEMBERS: ['admin', 'SuperAdmin'],
    CAN_INVITE_MEMBERS: ['admin', 'SuperAdmin'],
    CAN_RESEND_INVITES: ['admin', 'SuperAdmin'],
  },
} as const;

export const ROLE_OPTIONS = [
  { value: MEMBER_CONSTANTS.ROLES.ADMIN, label: 'Admin' },
  { value: MEMBER_CONSTANTS.ROLES.MEMBER, label: 'Member' },
] as const;

export const STATUS_COLORS = {
  [MEMBER_CONSTANTS.STATUS.ACTIVE]: 'text-green-600 dark:text-green-400',
  [MEMBER_CONSTANTS.STATUS.PENDING]: 'text-yellow-600 dark:text-yellow-400',
} as const;
