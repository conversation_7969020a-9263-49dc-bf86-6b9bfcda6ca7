import { supabase } from './supabaseClient';

// Define storage buckets
const STORAGE_BUCKETS = {
  PROFILES: 'profiles',
  ATTACHMENTS: 'attachments',
  DOCUMENTS: 'documents'
};

// Function to list files in a bucket
export const listFiles = async (bucketName: string): Promise<string[]> => {
  try {
    const { data, error } = await supabase.storage.from(bucketName).list();
    
    if (error) {
      console.error(`Error listing files in bucket ${bucketName}:`, error);
      return [];
    }
    
    return data.map(item => item.name);
  } catch (error) {
    console.error(`Error listing files in bucket ${bucketName}:`, error);
    return [];
  }
};

// Function to delete a file from storage
export const deleteFile = async (bucketName: string, filePath: string): Promise<boolean> => {
  try {
    const { error } = await supabase.storage.from(bucketName).remove([filePath]);
    
    if (error) {
      console.error(`Error deleting file ${filePath} from bucket ${bucketName}:`, error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`Error deleting file ${filePath} from bucket ${bucketName}:`, error);
    return false;
  }
};

// Function to find and delete orphaned files (files not referenced in the database)
export const cleanupOrphanedFiles = async (bucketName: string): Promise<number> => {
  try {
    // Get all files in the bucket
    const files = await listFiles(bucketName);
    
    // Get all file references from the database
    const { data: dbFiles, error } = await supabase
      .from('storage_references')
      .select('file_path')
      .eq('bucket_name', bucketName);
    
    if (error) {
      console.error(`Error fetching file references for bucket ${bucketName}:`, error);
      return 0;
    }
    
    // Create a set of referenced file paths
    const referencedFiles = new Set(dbFiles.map(item => item.file_path));
    
    // Find orphaned files (files in storage but not referenced in the database)
    const orphanedFiles = files.filter(file => !referencedFiles.has(file));
    
    // Delete orphaned files
    let deletedCount = 0;
    for (const file of orphanedFiles) {
      const success = await deleteFile(bucketName, file);
      if (success) {
        deletedCount++;
      }
    }
    
    console.log(`Cleaned up ${deletedCount} orphaned files from bucket ${bucketName}`);
    return deletedCount;
  } catch (error) {
    console.error(`Error cleaning up orphaned files in bucket ${bucketName}:`, error);
    return 0;
  }
};

// Function to clean up old temporary files (files older than a certain age)
export const cleanupOldTempFiles = async (bucketName: string, maxAgeInDays: number = 7): Promise<number> => {
  try {
    // Calculate the cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - maxAgeInDays);
    
    // Get all files in the bucket with their metadata
    const { data, error } = await supabase.storage.from(bucketName).list();
    
    if (error) {
      console.error(`Error listing files in bucket ${bucketName}:`, error);
      return 0;
    }
    
    // Filter files older than the cutoff date
    const oldFiles = data.filter(item => {
      const createdAt = new Date(item.created_at || '');
      return createdAt < cutoffDate;
    });
    
    // Delete old files
    let deletedCount = 0;
    for (const file of oldFiles) {
      const success = await deleteFile(bucketName, file.name);
      if (success) {
        deletedCount++;
      }
    }
    
    console.log(`Cleaned up ${deletedCount} old temporary files from bucket ${bucketName}`);
    return deletedCount;
  } catch (error) {
    console.error(`Error cleaning up old temporary files in bucket ${bucketName}:`, error);
    return 0;
  }
};

// Main function to run all cleanup operations
export const runStorageCleanup = async (): Promise<void> => {
  try {
    console.log('Starting storage cleanup...');
    
    // Clean up orphaned files in all buckets
    await cleanupOrphanedFiles(STORAGE_BUCKETS.PROFILES);
    await cleanupOrphanedFiles(STORAGE_BUCKETS.ATTACHMENTS);
    await cleanupOrphanedFiles(STORAGE_BUCKETS.DOCUMENTS);
    
    // Clean up old temporary files in all buckets
    await cleanupOldTempFiles(STORAGE_BUCKETS.PROFILES);
    await cleanupOldTempFiles(STORAGE_BUCKETS.ATTACHMENTS);
    await cleanupOldTempFiles(STORAGE_BUCKETS.DOCUMENTS);
    
    console.log('Storage cleanup completed');
  } catch (error) {
    console.error('Error during storage cleanup:', error);
  }
};

export { STORAGE_BUCKETS };
