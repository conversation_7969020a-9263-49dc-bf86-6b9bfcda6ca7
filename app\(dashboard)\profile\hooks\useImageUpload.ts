"use client";

import { useState } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { toast } from 'sonner';
import { STORAGE_BUCKET } from '../constants/profile';
import { generateAvatarFilePath, extractFilePathFromUrl } from '../utils/profile';

export const useImageUpload = () => {
  const [uploadingImage, setUploadingImage] = useState(false);

  const uploadImage = async (file: File): Promise<string> => {
    try {
      setUploadingImage(true);

      // Generate unique file path
      const filePath = generateAvatarFilePath(file.name);

      // Upload image to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(STORAGE_BUCKET)
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(STORAGE_BUCKET)
        .getPublicUrl(filePath);

      toast.success("Image uploaded successfully");
      return publicUrl;
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Failed to upload image");
      throw error;
    } finally {
      setUploadingImage(false);
    }
  };

  const deleteImage = async (imageUrl: string): Promise<void> => {
    if (!imageUrl) return;

    try {
      setUploadingImage(true);

      // Extract the file path from the public URL
      const filePath = extractFilePathFromUrl(imageUrl);

      // Delete image from Supabase Storage
      const { error: deleteError } = await supabase.storage
        .from(STORAGE_BUCKET)
        .remove([filePath]);

      if (deleteError) throw deleteError;

      toast.success("Image deleted successfully");
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image");
      throw error;
    } finally {
      setUploadingImage(false);
    }
  };

  return {
    uploadingImage,
    uploadImage,
    deleteImage,
  };
};
