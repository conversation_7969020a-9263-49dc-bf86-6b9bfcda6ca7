import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../../utils/auth";
import { calculateWorkspaceRevenue } from "../../utils/analytics";

/**
 * Get revenue for a workspace
 * @route GET /api/workspace/revenue/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const workspaceId = params.workspaceId;
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to view this workspace
    const permission = await checkWorkspacePermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    try {
      // Calculate revenue for the workspace
      const revenue = await calculateWorkspaceRevenue(workspaceId);
      
      return NextResponse.json(revenue, { status: 200 });
    } catch (error) {
      console.error("Error calculating workspace revenue:", error);
      return NextResponse.json({ 
        error: "Failed to calculate workspace revenue" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching workspace revenue:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
