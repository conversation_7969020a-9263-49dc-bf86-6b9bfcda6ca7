import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { useLoading } from '@/lib/hooks/useLoading';
import { initiatePaymentCheckout, PaymentGateway } from '@/lib/services/payment';
import { PlanTier, UserSubscription, SUBSCRIPTION_PLANS } from '../types/subscription';
import { createFreeSubscription, createPaidSubscription } from '../utils/subscription';
import { preparePaymentCheckout, generateSuccessUrl } from '../utils/payment';
import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

interface UsePaymentProps {
  user: any;
  updateUserSubscription: (subscription: UserSubscription) => Promise<void>;
  currentSubscription: UserSubscription | null;
}

export function usePayment({ user, updateUserSubscription, currentSubscription }: UsePaymentProps) {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const { startLoading, stopLoading } = useLoading();

  const handleSubscribe = async (
    selectedPlan: PlanTier,
    selectedGateway: PaymentGateway,
    billingCycle: 'monthly' | 'yearly'
  ) => {
    if (loading) return;

    try {
      setLoading(true);
      startLoading(SUBSCRIPTION_CONSTANTS.MESSAGES.PROCESSING);

      if (!user) {
        toast({
          title: "Authentication required",
          description: SUBSCRIPTION_CONSTANTS.MESSAGES.AUTH_REQUIRED,
          variant: "destructive",
        });
        return;
      }

      // If selecting the same plan as current subscription, redirect to management
      if (currentSubscription?.planId === selectedPlan && currentSubscription?.status === 'active') {
        router.push(SUBSCRIPTION_CONSTANTS.URLS.MANAGE_SUBSCRIPTION);
        return;
      }

      // For the free plan, just update the user metadata
      if (selectedPlan === 'starter') {
        const freeSubscription = createFreeSubscription();
        await updateUserSubscription(freeSubscription);

        toast({
          title: "Subscription updated",
          description: SUBSCRIPTION_CONSTANTS.MESSAGES.SUBSCRIPTION_UPDATED,
        });

        router.refresh();
        return;
      }

      // For paid plans, initiate payment checkout
      const checkoutConfig = preparePaymentCheckout({
        gateway: selectedGateway,
        planId: selectedPlan,
        billingCycle,
        user,
      });

      await initiatePaymentCheckout({
        ...checkoutConfig,
        onSuccess: async (data) => {
          // This is mainly for Razorpay which returns directly
          if (selectedGateway === 'razorpay') {
            const paidSubscription = createPaidSubscription(selectedPlan, billingCycle, {
              paymentMethod: 'razorpay',
              paymentId: data.paymentId,
            });

            await updateUserSubscription(paidSubscription);

            toast({
              title: "Subscription successful",
              description: `You are now subscribed to the ${SUBSCRIPTION_PLANS[selectedPlan].name} plan`,
            });

            router.refresh();
          }
        },
        onFailure: (error) => {
          toast({
            title: "Payment failed",
            description: error.message || SUBSCRIPTION_CONSTANTS.MESSAGES.PAYMENT_FAILED,
            variant: "destructive",
          });
        }
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "There was an error processing your request",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      stopLoading();
    }
  };

  return {
    loading,
    handleSubscribe,
  };
}
