"use client";

import React from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

// Hooks
import { useDashboardData } from "./hooks/useDashboardData";
import { useDashboardStats } from "./hooks/useDashboardStats";
import { useChartData } from "./hooks/useChartData";
import { useRecentSales } from "./hooks/useRecentSales";

// Components
import { DashboardSkeleton } from "./components/DashboardSkeleton";
import { StatsCard } from "./components/StatsCard";
import { SalesChart } from "./components/SalesChart";
import { RecentSalesCard } from "./components/RecentSalesCard";

// Constants
import { TOOLTIP_MESSAGES, EMPTY_STATE_MESSAGES } from "./constants/dashboard";

// Types
import { RootState } from "@/lib/store/store";

const Dashboard: React.FC = () => {
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);

  // Fetch dashboard data
  const {
    workspaceId,
    workspaceRevenue,
    ROC,
    qualifiedCount,
    workspaceCount,
    recentLeads,
    isInitialLoading,
  } = useDashboardData();

  // Process dashboard stats
  const { stats } = useDashboardStats({
    workspaceRevenue,
    qualifiedCount,
    workspaceCount,
    ROC,
  });

  // Process chart data
  const { chartData, hasChartData } = useChartData({ ROC });

  // Process recent sales data
  const { recentSales, teamPerformance, hasRecentSales, hasTeamPerformance } = useRecentSales({
    recentLeads,
  });

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return <DashboardSkeleton isCollapsed={isCollapsed} />;
  }

  // Show no workspace message
  if (!workspaceId) {
    return (
      <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="max-w-lg w-full p-8 text-center">
            <CardHeader>
              <CardTitle className="text-2xl font-semibold text-gray-800 dark:text-white">
                {EMPTY_STATE_MESSAGES.NO_WORKSPACE.title}
              </CardTitle>
              <CardDescription className="text-lg text-gray-600 dark:text-gray-400">
                {EMPTY_STATE_MESSAGES.NO_WORKSPACE.description}
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <div className="flex flex-col space-y-6 w-full">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 w-full">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white">
              Dashboard
            </h1>
          </div>
        </div>

        {/* Tabs */}
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full max-w-[400px] grid-cols-1">
            <TabsTrigger value="overview" className="text-sm font-medium">
              Overview
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
              {stats.map((stat, index) => {
                const tooltipMessages = [
                  TOOLTIP_MESSAGES.TOTAL_REVENUE,
                  TOOLTIP_MESSAGES.QUALIFIED_LEADS,
                  TOOLTIP_MESSAGES.TOTAL_LEADS,
                  TOOLTIP_MESSAGES.CONVERSION_RATE,
                ];

                return (
                  <StatsCard
                    key={stat.title}
                    stat={stat}
                    tooltipMessage={tooltipMessages[index]}
                  />
                );
              })}
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
              {/* Sales Chart */}
              <SalesChart data={chartData} hasData={hasChartData} />

              {/* Recent Sales */}
              <RecentSalesCard
                recentSales={recentSales}
                teamPerformance={teamPerformance}
                hasRecentSales={hasRecentSales}
                hasTeamPerformance={hasTeamPerformance}
              />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;