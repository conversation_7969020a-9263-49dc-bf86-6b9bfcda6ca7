import { webhook<PERSON><PERSON> } from "../base/webhooks";

interface WebhookRequest {
  status: boolean;
  type: string;
  name: string;
  webhook_url?: string;
  workspaceId?: string;
}

interface WebhookResponse {
  [key: string]: any;
}

export const webhookApis = webhookApi.injectEndpoints({
  endpoints: (builder) => ({
    // Create a new webhook
    webhook: builder.mutation<WebhookRequest, WebhookResponse & { workspaceId: string }>({
      query: (credentials) => ({
        url: "/create",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Webhook', id: 'LIST' },
        { type: 'WebhookByWorkspace', id: arg.workspaceId }
      ],
    }),

    // Update an existing webhook
    updateWebhook: builder.mutation<WebhookRequest, { data: any; id: string; workspaceId: string }>({
      query: ({ data, id }) => ({
        url: `/${id}`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Webhook', id: arg.id },
        { type: 'Webhook', id: 'LIST' },
        { type: 'WebhookByWorkspace', id: arg.workspaceId }
      ],
    }),

    // Delete an existing webhook
    deleteWebhook: builder.mutation<{ id: string }, { id: string; workspaceId: string }>({
      query: ({ id }) => ({
        url: `/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Webhook', id: arg.id },
        { type: 'Webhook', id: 'LIST' },
        { type: 'WebhookByWorkspace', id: arg.workspaceId }
      ],
    }),

    // Fetch webhooks
    getWebhooks: builder.query<any, { id: string }>({
      query: ({ id }) => ({
        url: `/workspace/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, arg) => [
        { type: 'WebhookByWorkspace', id: arg.id },
        { type: 'Webhook', id: 'LIST' }
      ],
    }),

    getWebhooksBySourceId: builder.query<
      any,
      { id: string; workspaceId: string }
    >({
      query: ({ id, workspaceId }) => ({
        url: `/source/${id}?workspaceId=${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, arg) => [
        { type: 'WebhookByWorkspace', id: arg.id },
        { type: 'Webhook', id: 'LIST' }
      ],
    }),

    changeWebhookStatus: builder.mutation<
      { id: string; status: boolean; workspaceId: string },
      { id: string; status: boolean; workspaceId: string }
    >({
      query: ({ id, status }) => ({
        url: `/${id}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Webhook', id: arg.id },
        { type: 'Webhook', id: 'LIST' },
        { type: 'WebhookByWorkspace', id: arg.workspaceId }
      ],
    }),
  }),
});

// Export hooks for the mutations and queries
export const {
  useWebhookMutation,
  useUpdateWebhookMutation,
  useDeleteWebhookMutation,
  useGetWebhooksQuery,
  useChangeWebhookStatusMutation,
  useGetWebhooksBySourceIdQuery,
} = webhookApis;
