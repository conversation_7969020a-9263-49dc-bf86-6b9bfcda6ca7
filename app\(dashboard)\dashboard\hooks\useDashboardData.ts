import { useState, useEffect, useCallback } from "react";
import {
  useGetActiveWorkspaceQuery,
  useGetCountByWorkspaceQuery,
  useGetQualifiedCountQuery,
  useGetRevenueByWorkspaceQuery,
  useGetROCByWorkspaceQuery,
} from "@/lib/store/services/workspace";
import { useGetWebhooksBySourceIdQuery } from "@/lib/store/services/webhooks";
import { getWorkspaceId } from "../utils/dashboardUtils";
import { RecentLead, DashboardLoadingStates } from "../types/dashboard";
import { DUMMY_LEADS_DATA } from "../constants/dashboard";

export const useDashboardData = () => {
  // Get active workspace
  const { data: activeWorkspace, isLoading: isWorkspaceLoading } = useGetActiveWorkspaceQuery();
  const workspaceId = getWorkspaceId(activeWorkspace);

  // Fetch dashboard data
  const { data: workspaceRevenue, isLoading: isRevenueLoading } = useGetRevenueByWorkspaceQuery(
    workspaceId || "", { skip: !workspaceId }
  );

  const { data: ROC, isLoading: isRocLoading } = useGetROCByWorkspaceQuery(
    workspaceId || "", { skip: !workspaceId }
  );

  const { data: qualifiedCount, isLoading: isQualifiedCountLoading } = useGetQualifiedCountQuery(
    workspaceId || "", { skip: !workspaceId }
  );

  const { data: workspaceCount, isLoading: isCountLoading } = useGetCountByWorkspaceQuery(
    workspaceId || "", { skip: !workspaceId }
  );

  const { data: webhooks, isLoading: isWebhooksLoading } = useGetWebhooksBySourceIdQuery(
    {
      workspaceId: workspaceId || "",
      id: ROC?.top_source_id,
    },
    { skip: !workspaceId || !ROC?.top_source_id }
  );

  // Recent leads state
  const [recentLeads, setRecentLeads] = useState<RecentLead[]>([]);
  const [isRecentLeadsLoading, setIsRecentLeadsLoading] = useState<boolean>(true);

  // Fetch recent leads
  const fetchRecentLeads = useCallback(async () => {
    if (!workspaceId) {
      setIsRecentLeadsLoading(false);
      return;
    }

    setIsRecentLeadsLoading(true);

    try {
      const response = await fetch(`/api/leads/recent/${workspaceId}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch recent leads: ${response.status}`);
      }

      const data = await response.json();

      if (data && data.data) {
        setRecentLeads(data.data);
      } else {
        setRecentLeads([]);
      }
    } catch (error) {
      console.error('Error fetching recent leads:', error);

      // Use dummy data in development
      if (process.env.NODE_ENV === 'development') {
        setRecentLeads(DUMMY_LEADS_DATA as any);
      } else {
        setRecentLeads([]);
      }
    } finally {
      setIsRecentLeadsLoading(false);
    }
  }, [workspaceId]);

  // Fetch data on mount and workspace change
  useEffect(() => {
    fetchRecentLeads();
  }, [fetchRecentLeads]);

  // Loading states
  const loadingStates: DashboardLoadingStates = {
    isWorkspaceLoading,
    isRevenueLoading,
    isRocLoading,
    isQualifiedCountLoading,
    isCountLoading,
    isWebhooksLoading,
    isRecentLeadsLoading,
  };

  // Check if any critical data is loading
  const isInitialLoading = isWorkspaceLoading || isRevenueLoading || isRocLoading ||
                          isQualifiedCountLoading || isCountLoading;

  return {
    // Data
    workspaceId,
    activeWorkspace,
    workspaceRevenue,
    ROC,
    qualifiedCount,
    workspaceCount,
    webhooks,
    recentLeads,

    // Loading states
    loadingStates,
    isInitialLoading,

    // Actions
    refetchRecentLeads: fetchRecentLeads,
  };
};
