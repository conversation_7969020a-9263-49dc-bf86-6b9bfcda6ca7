"use client";

import { Provider } from 'react-redux';
import { store } from '@/lib/store/store';
import { ThemeProvider } from 'next-themes';
import { TooltipProvider } from '@/components/ui/tooltip';
import { LoadingProvider } from '@/lib/providers/LoadingProvider';
import { GlobalLoadingIndicator } from '@/components/ui/global-loader';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <Provider store={store}>
      <TooltipProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <LoadingProvider>
            <GlobalLoadingIndicator />
            {children}
          </LoadingProvider>
        </ThemeProvider>
      </TooltipProvider>
    </Provider>
  );
}