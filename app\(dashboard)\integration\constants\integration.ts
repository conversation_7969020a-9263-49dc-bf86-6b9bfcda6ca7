import { PlatformIntegration, IntegrationStatus, FilterType } from "../types/integration";

export const INTEGRATION_MESSAGES = {
  LOADING: "Loading integrations...",
  NO_RESULTS: "No integrations found matching your criteria.",
  SEARCH_PLACEHOLDER: "Search platforms...",
  DOCUMENTATION_COMING_SOON: "Coming soon"
} as const;

export const INTEGRATION_LABELS = {
  TITLE: "Supported Integrations",
  PLATFORM: "Platform",
  CATEGORY: "Category", 
  DESCRIPTION: "Description",
  STATUS: "Status",
  DOCUMENTATION: "Documentation",
  VIEW_DOCS: "View Docs",
  DOCUMENTATION_BUTTON: "Documentation"
} as const;

export const FILTER_LABELS = {
  ALL: "All",
  ACTIVE: "Active", 
  BETA: "Beta",
  COMING_SOON: "Coming Soon"
} as const;

export const STATUS_COLORS = {
  active: "bg-green-500 hover:bg-green-600",
  beta: "bg-yellow-500 hover:bg-yellow-600",
  "coming-soon": "bg-gray-500 hover:bg-gray-600"
} as const;

export const STATUS_DISPLAY_NAMES = {
  active: "Active",
  beta: "Beta", 
  "coming-soon": "Coming Soon"
} as const;

export const FILTER_OPTIONS: FilterType[] = ["all", "active", "beta", "coming-soon"];

export const MOCK_PLATFORMS: PlatformIntegration[] = [
  {
    id: "1",
    name: "Salesforce",
    category: "CRM",
    description: "Integrate with Salesforce to synchronize leads and contacts.",
    status: "active",
    documentationUrl: "/docs/integrations/salesforce",
  },
  {
    id: "2",
    name: "HubSpot",
    category: "Marketing",
    description: "Connect with HubSpot to manage your marketing campaigns and leads.",
    status: "active",
    documentationUrl: "/docs/integrations/hubspot",
  },
  {
    id: "3",
    name: "Zapier",
    category: "Automation",
    description: "Use Zapier to connect with thousands of apps and automate your workflows.",
    status: "active",
    documentationUrl: "/docs/integrations/zapier",
  },
  {
    id: "4",
    name: "Mailchimp",
    category: "Email Marketing",
    description: "Sync your email lists and campaigns with Mailchimp.",
    status: "active",
    documentationUrl: "/docs/integrations/mailchimp",
  },
  {
    id: "5",
    name: "Slack",
    category: "Communication",
    description: "Get real-time notifications and updates in your Slack channels.",
    status: "active",
    documentationUrl: "/docs/integrations/slack",
  },
  {
    id: "6",
    name: "Google Analytics",
    category: "Analytics",
    description: "Track lead sources and conversion rates with Google Analytics.",
    status: "beta",
    documentationUrl: "/docs/integrations/google-analytics",
  },
  {
    id: "7",
    name: "Microsoft Dynamics",
    category: "CRM",
    description: "Synchronize lead data with Microsoft Dynamics CRM.",
    status: "coming-soon",
  },
  {
    id: "8",
    name: "Zoho CRM",
    category: "CRM",
    description: "Connect your leads and contacts with Zoho CRM.",
    status: "beta",
    documentationUrl: "/docs/integrations/zoho",
  }
];

export const TABLE_CONFIG = {
  MOBILE_BREAKPOINT: "md",
  ANIMATION_DURATION: "duration-200",
  SKELETON_ROWS: 8
} as const;

export const SEARCH_CONFIG = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 0
} as const;
