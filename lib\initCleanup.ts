import { initCleanupSchedule, cleanupBeforeShutdown } from './cleanup';

// Initialize cleanup schedule
let cleanupInterval: NodeJS.Timeout | null = null;

// Start the cleanup schedule
export const startCleanupSchedule = () => {
  if (!cleanupInterval) {
    cleanupInterval = initCleanupSchedule();
    console.log('Cleanup schedule started');
    
    // Register shutdown handler
    process.on('SIGTERM', async () => {
      console.log('SIGTERM received, cleaning up...');
      await cleanupBeforeShutdown();
      process.exit(0);
    });
    
    process.on('SIGINT', async () => {
      console.log('SIGINT received, cleaning up...');
      await cleanupBeforeShutdown();
      process.exit(0);
    });
  }
};

// Stop the cleanup schedule
export const stopCleanupSchedule = () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('Cleanup schedule stopped');
  }
};

// Auto-start in production
if (process.env.NODE_ENV === 'production') {
  startCleanupSchedule();
}
