export interface FormData {
  id: string;
  name: string;
  description?: string;
  html_content: string;
  css_content?: string;
  js_content?: string;
  is_active: boolean;
  lead_source_id?: string | null;
  workspace_id: number;
  created_at: string;
  updated_at: string;
}

export interface FormValues {
  name: string;
  description?: string;
  html_content: string;
  css_content?: string;
  js_content?: string;
  is_active: boolean;
  lead_source_id?: string;
}

export interface LeadSource {
  id: string;
  name: string;
  type: string;
  status: boolean;
  webhook_url?: string;
  description?: string;
  workspace_id?: string | null;
  created_at?: string;
  user_id?: string;
}

export interface FormsState {
  activeTab: string;
  editorTab: string;
  previewUrl: string | null;
  loading: boolean;
  formLoading: boolean;
  leadSources: LeadSource[];
}

export interface FormSubmissionData {
  formId: string;
  data: Record<string, any>;
}

export interface EmbedCodeDialogProps {
  formId: string | null;
  formName: string;
  children: React.ReactNode;
}

export interface FormCardProps {
  form: FormData;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onGetEmbedCode: (id: string, name: string) => void;
  onPreview: (id: string) => void;
}

export interface FormEditorProps {
  form: any;
  formLoading: boolean;
  editorTab: string;
  setEditorTab: (tab: string) => void;
  onSubmit: (values: FormValues) => void;
  onCancel: () => void;
  onPreview: () => void;
  leadSources: LeadSource[];
  isEditing: boolean;
}

export interface FormPreviewProps {
  previewUrl: string | null;
  formName: string;
  formId: string | null;
  onGeneratePreview: () => void;
}

export interface FormListProps {
  forms: FormData[];
  isLoading: boolean;
  onCreateNew: () => void;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onGetEmbedCode: (id: string, name: string) => void;
  onPreview: (id: string) => void;
}

export interface DefaultTemplates {
  html: string;
  css: string;
  js: string;
}

export interface FormBuilderState {
  forms: FormData[];
  currentForm: FormData | null;
  isLoading: boolean;
  error: string | null;
}

export interface FormSubmissionResponse {
  success: boolean;
  message: string;
  data?: any;
}

export interface WebhookData {
  webhooks: LeadSource[];
  data?: LeadSource[];
}

export interface WorkspaceData {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface FormBuilderProps {
  isCollapsed: boolean;
  activeWorkspace: WorkspaceData | null;
  searchParams: ReadonlyURLSearchParams | null;
}
