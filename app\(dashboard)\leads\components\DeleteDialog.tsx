import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { DialogMode } from "../types/leads";

interface DeleteDialogProps {
  isOpen: boolean;
  selectedLeadsCount: number;
  onClose: () => void;
  onConfirm: () => void;
}

export const DeleteDialog: React.FC<DeleteDialogProps> = ({
  isOpen,
  selectedLeadsCount,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95%] max-w-md p-0 overflow-hidden rounded-lg">
        <DialogHeader className="px-6 pt-6 pb-4 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800">
          <div className="flex items-center gap-3">
            <div className="bg-black/10 dark:bg-white/10 p-2 rounded-full">
              <Trash2 className="h-5 w-5 text-black dark:text-white" />
            </div>
            <DialogTitle className="text-xl font-semibold text-black dark:text-white">
              Delete Selected Leads
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="px-6 py-5">
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-4 border border-gray-200 dark:border-gray-800">
            <p className="text-black dark:text-white">
              Are you sure you want to delete{" "}
              <span className="font-bold">{selectedLeadsCount}</span> lead
              {selectedLeadsCount !== 1 ? "s" : ""}?
            </p>
            <p className="text-sm text-black dark:text-white mt-2">
              This action cannot be undone. The leads will be permanently
              removed from the system.
            </p>
          </div>

          <DialogFooter className="flex justify-end space-x-3 mt-4">
            <DialogClose asChild>
              <Button
                variant="outline"
                className="border-gray-200 dark:border-gray-800 text-black dark:text-white"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              variant="destructive"
              onClick={onConfirm}
              className="bg-black hover:bg-black/90 text-white dark:bg-white dark:hover:bg-white/90 dark:text-black"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete {selectedLeadsCount} Lead{selectedLeadsCount !== 1 ? "s" : ""}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};
