"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Briefcase } from "lucide-react";
import { ProfileDetails } from '../../types/profile';

interface ProfessionalDetailsProps {
  profileData: ProfileDetails;
}

export function ProfessionalDetails({ profileData }: ProfessionalDetailsProps) {
  const { professionalInfo } = profileData;

  return (
    <Card className="md:col-span-2 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px] shadow-sm">
      <CardHeader className="p-3 sm:p-4 md:p-6 pb-2 sm:pb-3 md:pb-4">
        <CardTitle className="flex items-center gap-2 text-base sm:text-lg md:text-xl lg:text-2xl font-bold">
          <Briefcase className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-primary" />
          Professional Details
        </CardTitle>
      </CardHeader>
      <CardContent className="p-3 sm:p-4 md:p-6 pt-0 space-y-3 sm:space-y-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Job Title</p>
            <p className="text-sm sm:text-base font-medium text-foreground break-words">
              {professionalInfo.title}
            </p>
          </div>
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Department</p>
            <p className="text-sm sm:text-base font-medium text-foreground break-words">
              {professionalInfo.department}
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Company</p>
            <p className="text-sm sm:text-base font-medium text-foreground break-words">
              {professionalInfo.company}
            </p>
          </div>
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Start Date</p>
            <p className="text-sm sm:text-base font-medium text-foreground">
              {professionalInfo.startDate}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
