import React from "react";
import { Button } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import { DocumentationButtonProps } from "../types/integration";
import { INTEGRATION_MESSAGES, INTEGRATION_LABELS } from "../constants/integration";
import { openDocumentation } from "../utils/integrationUtils";

export const DocumentationButton: React.FC<DocumentationButtonProps> = ({
  url,
  size = "sm",
  variant = "outline"
}) => {
  if (!url) {
    return (
      <span className="text-gray-500 dark:text-gray-400 text-sm">
        {INTEGRATION_MESSAGES.DOCUMENTATION_COMING_SOON}
      </span>
    );
  }

  return (
    <Button
      variant={variant}
      size={size}
      className="flex items-center gap-1 border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50"
      onClick={() => openDocumentation(url)}
    >
      <span>{INTEGRATION_LABELS.VIEW_DOCS}</span>
      <ExternalLink className="h-3 w-3" />
    </Button>
  );
};
