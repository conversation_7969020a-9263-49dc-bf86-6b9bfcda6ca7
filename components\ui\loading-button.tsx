"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { ButtonLoader } from "@/components/ui/global-loader";
import { cn } from "@/lib/utils";
import { ButtonProps } from "@/components/ui/button";

export interface LoadingButtonProps extends ButtonProps {
  isLoading?: boolean;
  loadingText?: string;
  icon?: React.ReactNode;
}

const LoadingButton = React.forwardRef<HTMLButtonElement, LoadingButtonProps>(
  ({ className, children, isLoading, loadingText, icon, ...props }, ref) => {
    // Determine what to display based on loading state
    const displayText = isLoading && loadingText ? loadingText : children;
    
    return (
      <Button
        className={cn(className)}
        disabled={isLoading || props.disabled}
        ref={ref}
        {...props}
      >
        {isLoading && <ButtonLoader />}
        {!isLoading && icon}
        {displayText}
      </Button>
    );
  }
);

LoadingButton.displayName = "LoadingButton";

export { LoadingButton };
