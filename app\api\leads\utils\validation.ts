import axios from "axios";

/**
 * Validates an email address using an external API
 * @param email The email address to validate
 * @param emailApiKey Optional API key for the validation service
 * @returns Promise<boolean> indicating if the email is valid
 */
export async function validateEmail(email: string, emailApiKey?: string): Promise<boolean> {
  try {
    // Use provided API key or fallback to a default if not available
    const apiKey = emailApiKey || process.env.DEFAULT_EMAIL_VERIFICATION_KEY;
    
    if (!apiKey) {
      console.warn("No email verification API key available");
      return true; // Skip validation if no key is available
    }

    const response = await axios.get(
      `https://emailverifier.reoon.com/api/v1/verify?email=${email}&key=${apiKey}&mode=quick`
    );

    return (
      response.data.is_valid_syntax &&
      response.data.mx_accepts_mail &&
      response.data.status === "valid"
    );
  } catch (error) {
    console.error("Email validation error:", error);
    return false;
  }
}

/**
 * Validates a phone number using an external API
 * @param phoneNumber The phone number to validate
 * @param phoneApiKey Optional API key for the validation service
 * @returns Promise<boolean> indicating if the phone number is valid
 */
export async function validatePhoneNumber(
  phoneNumber: string,
  phoneApiKey?: string
): Promise<boolean> {
  try {
    // Use provided API key or fallback to a default if not available
    const apiKey = phoneApiKey || process.env.DEFAULT_PHONE_VERIFICATION_KEY;
    
    if (!apiKey) {
      console.warn("No phone verification API key available");
      return true; // Skip validation if no key is available
    }

    const response = await axios.get(
      `https://api-bdc.net/data/phone-number-validate?number=${phoneNumber}&countryCode=IN&localityLanguage=en&key=${apiKey}`
    );

    // Check if phone number is valid based on API response
    return response.data.isValid;
  } catch (error) {
    console.error("Phone number validation error:", error);
    return false;
  }
}
