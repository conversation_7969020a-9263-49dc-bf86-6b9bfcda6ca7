export const CHART_CONFIG = {
  BAR_GAP: 30,
  BAR_SIZE: 80,
  MAX_BAR_SIZE: 100,
  ANIMATION_DURATION: 1500,
  CHART_HEIGHT: 280,
  CHART_MARGINS: { top: 20, right: 20, bottom: 0, left: 20 },
} as const;

export const DUMMY_MONTHS = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'] as const;

export const TOOLTIP_MESSAGES = {
  TOTAL_REVENUE: "Total revenue generated from all leads",
  QUALIFIED_LEADS: "Leads that have reached a qualified status",
  TOTAL_LEADS: "Total number of leads in the system",
  CONVERSION_RATE: "Percentage of leads that have been converted",
  TEAM_PERFORMANCE: "Performance of team members based on recent sales",
  RECENT_TRANSACTIONS: "Most recent sales with revenue generated",
} as const;

export const EMPTY_STATE_MESSAGES = {
  NO_WORKSPACE: {
    title: "No Active Workspace",
    description: "Please select or create a workspace to view analytics.",
  },
  NO_SALES: {
    title: "No sales data available",
    description: "Sales will appear here as leads generate revenue. Assign leads to team members and update their status to track sales.",
    tip: "Tip: Add revenue to leads to see them appear in this section.",
  },
  NO_CHART_DATA: {
    title: "No data available",
    description: "Analytics will appear as leads are processed",
  },
  NO_CONVERSIONS: {
    message: "No converted leads yet. Conversion data will appear when leads are converted.",
  },
} as const;

export const DUMMY_LEADS_DATA = [
  {
    id: 1,
    name: "Olivia Martin",
    email: "<EMAIL>",
    revenue: 1999,
    created_at: new Date().toISOString(),
    users: {
      id: "user-1",
      email: "<EMAIL>",
      user_metadata: {
        name: "John Doe"
      }
    }
  },
  {
    id: 2,
    name: "Jackson Lee",
    email: "<EMAIL>",
    revenue: 39,
    created_at: new Date().toISOString(),
    users: {
      id: "user-2",
      email: "<EMAIL>",
      user_metadata: {
        name: "Jane Smith"
      }
    }
  },
  {
    id: 3,
    name: "Isabella Nguyen",
    email: "<EMAIL>",
    revenue: 299,
    created_at: new Date().toISOString(),
    users: {
      id: "user-3",
      email: "<EMAIL>",
      user_metadata: {
        name: "Michael Johnson"
      }
    }
  }
] as const;
