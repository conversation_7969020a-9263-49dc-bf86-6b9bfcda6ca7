"use client";

import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ProfileFormData } from '../../types/profile';

interface SocialLinksSectionProps {
  formData: ProfileFormData;
  updateFormData: (field: keyof ProfileFormData, value: string) => void;
}

export function SocialLinksSection({
  formData,
  updateFormData,
}: SocialLinksSectionProps) {
  return (
    <>
      {/* LinkedIn */}
      <div className="space-y-2">
        <Label htmlFor="linkedin">LinkedIn URL</Label>
        <Input
          id="linkedin"
          value={formData.linkedin}
          onChange={(e) => updateFormData('linkedin', e.target.value)}
          placeholder="https://linkedin.com/in/username"
        />
      </div>

      {/* Twitter */}
      <div className="space-y-2">
        <Label htmlFor="twitter">Twitter URL</Label>
        <Input
          id="twitter"
          value={formData.twitter}
          onChange={(e) => updateFormData('twitter', e.target.value)}
          placeholder="https://twitter.com/username"
        />
      </div>

      {/* GitHub */}
      <div className="space-y-2">
        <Label htmlFor="github">GitHub URL</Label>
        <Input
          id="github"
          value={formData.github}
          onChange={(e) => updateFormData('github', e.target.value)}
          placeholder="https://github.com/username"
        />
      </div>
    </>
  );
}
