import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useUpdateWorkspaceMutation } from '@/lib/store/services/workspace';
import { WorkspaceSettings } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';
import { 
  validateWorkspaceSettings, 
  formatWorkspaceDataForAPI, 
  parseWorkspaceDataFromAPI,
  getStoredActiveTab,
  storeActiveTab,
  hasSettingsChanged
} from '../utils/workspace';

interface UseWorkspaceSettingsProps {
  workspaceId: string;
  workspaceData?: any;
}

export function useWorkspaceSettings({ workspaceId, workspaceData }: UseWorkspaceSettingsProps) {
  const [updateWorkspace] = useUpdateWorkspaceMutation();
  
  // Tab management
  const [activeTab, setActiveTab] = useState(() => getStoredActiveTab());
  
  // Settings state
  const [settings, setSettings] = useState<WorkspaceSettings>(WORKSPACE_CONSTANTS.DEFAULTS.WORKSPACE_SETTINGS);
  const [tempSettings, setTempSettings] = useState<WorkspaceSettings | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Initialize settings from workspace data
  useEffect(() => {
    if (workspaceData?.data) {
      const parsedSettings = parseWorkspaceDataFromAPI(workspaceData.data);
      setSettings(parsedSettings);
    }
  }, [workspaceData]);

  // Handle tab change
  const handleTabClick = (id: string) => {
    setActiveTab(id);
    storeActiveTab(id);
  };

  // Handle edit mode
  const handleEditClick = () => {
    setIsEditMode(true);
    setTempSettings({ ...settings });
  };

  const handleCancelEdit = () => {
    setIsEditMode(false);
    if (tempSettings) {
      setSettings(tempSettings);
    }
  };

  // Handle save settings
  const handleSave = async () => {
    // Validate settings
    const validation = validateWorkspaceSettings(settings);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      setIsSaving(true);
      
      const formattedData = formatWorkspaceDataForAPI(settings);
      await updateWorkspace({ 
        id: workspaceId, 
        data: formattedData 
      }).unwrap();
      
      setIsEditMode(false);
      setTempSettings(settings);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.SETTINGS_SAVED);
    } catch (error: any) {
      console.error('Error saving settings:', error);
      toast.error(WORKSPACE_CONSTANTS.MESSAGES.SETTINGS_SAVE_ERROR);
    } finally {
      setIsSaving(false);
    }
  };

  // Check if settings have been modified
  const hasUnsavedChanges = () => {
    if (!tempSettings) return false;
    return hasSettingsChanged(settings, tempSettings);
  };

  return {
    // Tab state
    activeTab,
    handleTabClick,
    
    // Settings state
    settings,
    setSettings,
    tempSettings,
    isEditMode,
    isSaving,
    
    // Actions
    handleEditClick,
    handleCancelEdit,
    handleSave,
    hasUnsavedChanges,
  };
}
