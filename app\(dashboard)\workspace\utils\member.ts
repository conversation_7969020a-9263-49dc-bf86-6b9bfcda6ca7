import { WorkspaceMember, MemberRole } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';

/**
 * Validate email address format
 * @param email - Email to validate
 * @returns Boolean indicating if email is valid
 */
export function validateEmail(email: string): boolean {
  if (!email || email.length < MEMBER_CONSTANTS.VALIDATION.MIN_EMAIL_LENGTH) {
    return false;
  }

  if (email.length > MEMBER_CONSTANTS.VALIDATION.MAX_EMAIL_LENGTH) {
    return false;
  }

  return MEMBER_CONSTANTS.VALIDATION.EMAIL_REGEX.test(email);
}

/**
 * Check if user has permission to perform action
 * @param userRole - Current user's role
 * @param action - Action to check permission for
 * @returns Boolean indicating if user has permission
 */
export function hasPermission(userRole: MemberRole, action: keyof typeof MEMBER_CONSTANTS.PERMISSIONS): boolean {
  const allowedRoles = MEMBER_CONSTANTS.PERMISSIONS[action];
  return allowedRoles.includes(userRole as any);
}

/**
 * Get display name for member
 * @param member - Member object
 * @returns Display name (name or email)
 */
export function getMemberDisplayName(member: WorkspaceMember): string {
  return member.name || member.email;
}

/**
 * Get member initials for avatar
 * @param member - Member object
 * @returns Initials string
 */
export function getMemberInitials(member: WorkspaceMember): string {
  const name = getMemberDisplayName(member);
  const parts = name.split(' ');

  if (parts.length >= 2) {
    return `${parts[0][0]}${parts[1][0]}`.toUpperCase();
  }

  return name.slice(0, 2).toUpperCase();
}

/**
 * Format member role for display
 * @param role - Member role
 * @returns Formatted role string
 */
export function formatRole(role: string): string {
  return role.charAt(0).toUpperCase() + role.slice(1).toLowerCase();
}

/**
 * Format member status for display
 * @param status - Member status
 * @returns Formatted status string
 */
export function formatStatus(status: string): string {
  return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
}

/**
 * Check if member can be edited
 * @param member - Member to check
 * @param currentUserRole - Current user's role
 * @returns Boolean indicating if member can be edited
 */
export function canEditMember(member: WorkspaceMember, currentUserRole: MemberRole): boolean {
  // Can't edit super admins
  if (member.role === MEMBER_CONSTANTS.ROLES.SUPER_ADMIN) {
    return false;
  }

  // Only admins and super admins can edit members
  return hasPermission(currentUserRole, 'CAN_EDIT_ROLES');
}

/**
 * Check if member can be deleted
 * @param member - Member to check
 * @param currentUserRole - Current user's role
 * @returns Boolean indicating if member can be deleted
 */
export function canDeleteMember(member: WorkspaceMember, currentUserRole: MemberRole): boolean {
  // Can't delete super admins
  if (member.role === MEMBER_CONSTANTS.ROLES.SUPER_ADMIN) {
    return false;
  }

  // Only admins and super admins can delete members
  return hasPermission(currentUserRole, 'CAN_DELETE_MEMBERS');
}

/**
 * Check if invite can be resent
 * @param member - Member to check
 * @param currentUserRole - Current user's role
 * @returns Boolean indicating if invite can be resent
 */
export function canResendInvite(member: WorkspaceMember, currentUserRole: MemberRole): boolean {
  // Can only resend for pending members
  if (member.status !== MEMBER_CONSTANTS.STATUS.PENDING) {
    return false;
  }

  // Only admins and super admins can resend invites
  return hasPermission(currentUserRole, 'CAN_RESEND_INVITES');
}

/**
 * Create new member object for invitation
 * @param email - Member email
 * @param role - Member role
 * @returns New member object
 */
export function createNewMember(email: string, role: string): WorkspaceMember {
  return {
    email: email.toLowerCase().trim(),
    role,
    status: MEMBER_CONSTANTS.STATUS.PENDING,
  };
}

/**
 * Validate file for profile image upload
 * @param file - File to validate
 * @returns Object with isValid boolean and error message
 */
export function validateImageFile(file: File): { isValid: boolean; error?: string } {
  // Check file size
  if (file.size > MEMBER_CONSTANTS.FILE_UPLOAD.MAX_SIZE) {
    return {
      isValid: false,
      error: 'File size must be less than 5MB'
    };
  }

  // Check file type
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  if (!fileExtension || !MEMBER_CONSTANTS.FILE_UPLOAD.SUPPORTED_FORMATS.includes(fileExtension as any)) {
    return {
      isValid: false,
      error: 'Please upload a valid image file (JPG, PNG, GIF, WebP)'
    };
  }

  return { isValid: true };
}

/**
 * Generate delete confirmation message
 * @param member - Member to delete
 * @returns Formatted confirmation message
 */
export function getDeleteConfirmationMessage(member: WorkspaceMember): string {
  const name = getMemberDisplayName(member);
  return MEMBER_CONSTANTS.UI_TEXT.DELETE_DESCRIPTION.replace('{name}', name);
}
