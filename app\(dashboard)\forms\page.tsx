"use client";

import React, { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON>bsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Button
} from "@/components/ui/button";
import {
  Input
} from "@/components/ui/input";
import {
  Textarea
} from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Switch
} from "@/components/ui/switch";
import {
  Loader
} from "@/components/ui/loader";
import { Copy, Save, Share, Trash, Plus, Code, Eye, Link } from "lucide-react";

// Hooks
import { useFormsData } from "./hooks/useFormsData";
import { useFormState } from "./hooks/useFormState";
import { useFormActions } from "./hooks/useFormActions";
import { useRealtimeUpdates } from "./hooks/useRealtimeUpdates";

// Components
import { FormsSkeleton } from "./components/FormsSkeleton";
import { FormsList } from "./components/FormsList";
import EmbedCodeDialog from "./components/EmbedCodeDialog";

// Constants
import {
  TAB_VALUES,
  EDITOR_TAB_VALUES,
  FORM_LABELS,
  FORM_PLACEHOLDERS,
  FORM_DESCRIPTIONS,
  BUTTON_LABELS,
  EMPTY_STATE_MESSAGES,
} from "./constants/forms";

// Utils
import {
  getDefaultFormValues,
  getFormIdForEmbed,
} from "./utils/formUtils";

// Schemas
import { formSchema, FormSchemaType } from "./schemas/formSchema";

// Types
import { FormValues } from "./types/forms";

// Create a wrapper component that uses useSearchParams
function FormsPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Custom hooks
  const {
    activeWorkspace,
    workspaceId,
    isCollapsed,
    formsList,
    isFormsLoading,
    leadSources,
    isInitialLoading,
  } = useFormsData();

  const {
    activeTab,
    editorTab,
    formLoading,
    previewUrl,
    setActiveTab,
    setEditorTab,
    startFormLoading,
    stopFormLoading,
    updatePreviewUrl,
    navigateToEditor,
  } = useFormState();

  // Form hook
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultFormValues(),
  });

  // Form actions hook
  const {
    onSubmit,
    createNewForm,
    editForm,
    deleteForm,
    fetchFormDetails,
    generatePreview,
    previewForm,
  } = useFormActions({
    workspaceId: workspaceId || '',
    searchParams,
    startFormLoading,
    stopFormLoading,
    updatePreviewUrl,
    navigateToEditor,
    form,
  });

  // Realtime updates hook
  useRealtimeUpdates({
    workspaceId,
    enabled: true,
  });

  // Fetch form details if editing an existing form
  useEffect(() => {
    const formId = searchParams ? searchParams.get("id") : null;
    if (formId && workspaceId) {
      fetchFormDetails(formId);
    }
  }, [searchParams, workspaceId, fetchFormDetails]);

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return <FormsSkeleton isCollapsed={isCollapsed} />;
  }

  if (!activeWorkspace) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-gray-600 dark:text-gray-400">{EMPTY_STATE_MESSAGES.NO_WORKSPACE}</p>
      </div>
    );
  }

  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        <CardHeader className="flex flex-row justify-between items-center bg-gray-100 dark:bg-gray-800 md:bg-white md:dark:bg-gray-900">
          <CardTitle className="text-sm md:text-xl lg:text-2xl gradient-heading text-gray-900 dark:text-white">
            Form Builder
          </CardTitle>
          <div className="flex gap-2">
            <Button variant="accent" onClick={createNewForm}>
              <Plus className="mr-2 h-4 w-4" />
              New Form
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-4 md:p-6">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
            <TabsList className="w-full justify-start mb-6 overflow-x-auto">
              <TabsTrigger value="forms">My Forms</TabsTrigger>
              <TabsTrigger value="editor">Form Editor</TabsTrigger>
              {searchParams && searchParams.get("id") && <TabsTrigger value="preview">Preview</TabsTrigger>}
            </TabsList>

            {/* Forms List Tab */}
            <TabsContent value="forms" className="space-y-6">
              <FormsList
                forms={formsList}
                isLoading={isFormsLoading}
                onCreateNew={createNewForm}
                onEdit={editForm}
                onDelete={deleteForm}
                onGetEmbedCode={(id, name) => {}}
                onPreview={previewForm}
              />
            </TabsContent>

            {/* Form Editor Tab */}
            <TabsContent value="editor" className="space-y-6">
              {formLoading ? (
                <div className="flex justify-center py-10">
                  <Loader size="lg" variant="primary" />
                </div>
              ) : (
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-6">
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{FORM_LABELS.NAME}</FormLabel>
                              <FormControl>
                                <Input placeholder={FORM_PLACEHOLDERS.NAME} {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{FORM_LABELS.DESCRIPTION}</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder={FORM_PLACEHOLDERS.DESCRIPTION}
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="lead_source_id"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{FORM_LABELS.LEAD_SOURCE}</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder={FORM_PLACEHOLDERS.LEAD_SOURCE} />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="none">None</SelectItem>
                                  {leadSources?.map((source: any) => (
                                    <SelectItem key={source.id} value={source.id}>
                                      {source.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormDescription>
                                {FORM_DESCRIPTIONS.LEAD_SOURCE}
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="is_active"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">
                                  {FORM_LABELS.ACTIVE}
                                </FormLabel>
                                <FormDescription>
                                  {FORM_DESCRIPTIONS.ACTIVE}
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="space-y-6">
                        <div className="border rounded-md overflow-hidden">
                          <Tabs value={editorTab} onValueChange={(value) => setEditorTab(value as any)} className="w-full">
                            <div className="bg-muted p-2 border-b">
                              <TabsList className="w-full">
                                <TabsTrigger value="html" className="flex items-center">
                                  <Code className="mr-2 h-4 w-4" />
                                  {FORM_LABELS.HTML}
                                </TabsTrigger>
                                <TabsTrigger value="css" className="flex items-center">
                                  <Code className="mr-2 h-4 w-4" />
                                  {FORM_LABELS.CSS}
                                </TabsTrigger>
                                <TabsTrigger value="js" className="flex items-center">
                                  <Code className="mr-2 h-4 w-4" />
                                  {FORM_LABELS.JAVASCRIPT}
                                </TabsTrigger>
                              </TabsList>
                            </div>

                            <TabsContent value="html" className="mt-0 border-0 p-0">
                              <FormField
                                control={form.control}
                                name="html_content"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Textarea
                                        className="font-mono min-h-[400px] rounded-none border-0 resize-none focus-visible:ring-0"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </TabsContent>

                            <TabsContent value="css" className="mt-0 border-0 p-0">
                              <FormField
                                control={form.control}
                                name="css_content"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Textarea
                                        className="font-mono min-h-[400px] rounded-none border-0 resize-none focus-visible:ring-0"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </TabsContent>

                            <TabsContent value="js" className="mt-0 border-0 p-0">
                              <FormField
                                control={form.control}
                                name="js_content"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormControl>
                                      <Textarea
                                        className="font-mono min-h-[400px] rounded-none border-0 resize-none focus-visible:ring-0"
                                        {...field}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </TabsContent>
                          </Tabs>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button type="button" variant="outline" onClick={() => setActiveTab("forms" as any)}>
                        {BUTTON_LABELS.CANCEL}
                      </Button>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="secondary"
                          onClick={generatePreview}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          {BUTTON_LABELS.PREVIEW}
                        </Button>
                        <Button type="submit" variant="accent">
                          <Save className="mr-2 h-4 w-4" />
                          {BUTTON_LABELS.SAVE}
                        </Button>
                      </div>
                    </div>
                  </form>
                </Form>
              )}
            </TabsContent>

            {/* Preview Tab */}
            {searchParams && searchParams.get("id") && (
              <TabsContent value="preview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Form Preview</CardTitle>
                    <CardDescription>
                      This is how your form will appear to users
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-center mb-4">
                      <Button variant="outline" onClick={generatePreview}>
                        <Eye className="mr-2 h-4 w-4" />
                        Open Preview in New Tab
                      </Button>
                    </div>

                    <div className="border rounded-lg overflow-hidden">
                      {previewUrl ? (
                        <iframe
                          src={previewUrl}
                          className="w-full h-[600px]"
                          title="Form Preview"
                        />
                      ) : (
                        <div className="flex justify-center items-center h-[600px] bg-muted">
                          <Button variant="outline" onClick={generatePreview}>
                            Generate Preview
                          </Button>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Embed Code</CardTitle>
                    <CardDescription>
                      Use this code to embed your form on any website
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-sm text-muted-foreground">
                        Choose from multiple embedding options to integrate this form into your website.
                      </p>

                      <EmbedCodeDialog
                        formId={getFormIdForEmbed(searchParams)}
                        formName={form.getValues().name}
                      >
                        <Button className="w-full">
                          <Code className="mr-2 h-4 w-4" />
                          Get Embed Code
                        </Button>
                      </EmbedCodeDialog>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

// Export the main component with Suspense
export default function FormsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FormsPageContent />
    </Suspense>
  );
}

// Add dynamic export to prevent static generation
export const dynamic = 'force-dynamic';
