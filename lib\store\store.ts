import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";
import { api } from "./base/authapi";
import authReducer from "./slices/authSlice";
import leadsReducer from "./slices/leadsSlice";
import { webhookApi } from "./base/webhooks";
import { workspaceApi } from "./base/workspace";
import { leadsApi } from "./base/leadsapi";
import { statusApi } from "./base/status";
import { TagsApi } from "./base/tags";
import { membersApi } from "./base/members";
import { formsApi } from "./base/forms";
import sidebarReducer from "./slices/sideBar";
import loadingReducer from "./slices/loadingSlice";

// Configure store with optimized settings
export const store = configureStore({
  reducer: {
    [api.reducerPath]: api.reducer,
    [webhookApi.reducerPath]: webhookApi.reducer,
    [workspaceApi.reducerPath]: workspaceApi.reducer,
    [leadsApi.reducerPath]: leadsApi.reducer,
    [statusApi.reducerPath]: statusApi.reducer,
    [TagsApi.reducerPath]: TagsApi.reducer,
    [membersApi.reducerPath]: membersApi.reducer,
    [formsApi.reducerPath]: formsApi.reducer,
    auth: authReducer,
    sidebar: sidebarReducer,
    loading: loadingReducer,
    // leads: leadsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // Optimize serializable check to reduce overhead
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
        // Ignore these field paths in all actions
        ignoredActionPaths: ['meta.arg', 'payload.timestamp'],
        // Ignore these paths in the state
        ignoredPaths: ['items.dates'],
      },
      // Reduce middleware overhead
      immutableCheck: { warnAfter: 300 },
    })
      .concat(api.middleware)
      .concat(webhookApi.middleware)
      .concat(workspaceApi.middleware)
      .concat(leadsApi.middleware)
      .concat(statusApi.middleware)
      .concat(TagsApi.middleware)
      .concat(membersApi.middleware)
      .concat(formsApi.middleware),
});

// Setup listeners with optimized configuration
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
