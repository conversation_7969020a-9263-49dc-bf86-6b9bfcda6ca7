"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";
import { PaymentGateway } from "@/lib/services/payment";

// Types and utilities
import { PlanTier } from "./types/subscription";
import { SUBSCRIPTION_CONSTANTS } from "./constants/subscription";

// Custom hooks
import { useSubscription } from "./hooks/useSubscription";
import { usePayment } from "./hooks/usePayment";
import { useBillingCycle } from "./hooks/useBillingCycle";

// Components
import { SubscriptionSkeleton } from "./components/SubscriptionSkeleton";
import { CurrentSubscription } from "./components/CurrentSubscription";
import { BillingCycleToggle } from "./components/BillingCycleToggle";
import { PaymentMethodSelector } from "./components/PaymentMethodSelector";
import { SubscribeButton } from "./components/SubscribeButton";
import { FeatureComparison } from "./components/FeatureComparison";

// Utils
import { getSubscribeButtonText, isSubscribeButtonDisabled } from "./utils/payment";

// Dynamically import the FAQ section with client-side only rendering
const FAQSection = dynamic(() => import('@/components/subscription/FAQSection'), {
  ssr: false,
  loading: () => <SubscriptionSkeleton />
});

// Dynamically import the Pricing section with client-side only rendering
const PricingSection = dynamic(() => import('@/components/subscription/PricingSection'), {
  ssr: false,
  loading: () => <SubscriptionSkeleton />
});

export default function SubscriptionPage() {
  const router = useRouter();
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);

  // State management
  const [selectedPlan, setSelectedPlan] = useState<PlanTier>('professional');
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway>('stripe');

  // Custom hooks
  const { currentSubscription, user, isDataLoading, daysRemaining, updateUserSubscription } = useSubscription();
  const { loading, handleSubscribe } = usePayment({ user, updateUserSubscription, currentSubscription });
  const { billingCycle, setBillingCycle, getPrice } = useBillingCycle();

  // Handle subscription action
  const onSubscribe = () => {
    handleSubscribe(selectedPlan, selectedGateway, billingCycle);
  };

  // Handle manage subscription navigation
  const onManageSubscription = () => {
    router.push(SUBSCRIPTION_CONSTANTS.URLS.MANAGE_SUBSCRIPTION);
  };

  // Get button text and disabled state
  const buttonText = getSubscribeButtonText(
    selectedPlan,
    currentSubscription?.planId || null,
    currentSubscription?.status === 'active',
    loading
  );

  const isButtonDisabled = isSubscribeButtonDisabled(
    selectedPlan,
    currentSubscription?.planId || null,
    currentSubscription?.status === 'active',
    loading
  );

  return (
    <div
      className={`transition-all duration-500 ease-in-out p-2 sm:p-4 md:p-6 lg:p-8 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden bg-gradient-to-b from-background to-muted/20`}
    >
      {/* Header */}
      <div className="mb-8 md:mb-12 text-center max-w-3xl mx-auto">
        <div className="inline-block mb-4 bg-primary/10 px-4 py-1.5 rounded-full">
          <span className="text-sm font-medium text-primary flex items-center gap-1.5">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="animate-pulse">
              <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="currentColor" />
            </svg>
            Subscription Plans
          </span>
        </div>
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent animate-in slide-in-from-bottom-4 duration-700">
          Choose Your Perfect Plan
        </h1>
        <p className="text-sm sm:text-base md:text-lg text-muted-foreground max-w-2xl mx-auto animate-in slide-in-from-bottom-5 duration-700 delay-100">
          Select the subscription that best fits your business needs and unlock powerful CRM features to supercharge your growth
        </p>
      </div>

      {/* Current Subscription Status */}
      <CurrentSubscription
        subscription={currentSubscription}
        daysRemaining={daysRemaining}
        isDataLoading={isDataLoading}
        onManage={onManageSubscription}
      />

      {/* Billing Cycle Toggle */}
      <BillingCycleToggle
        billingCycle={billingCycle}
        onToggle={setBillingCycle}
      />

      {/* Pricing Cards - Client-side only rendering */}
      <PricingSection
        selectedPlan={selectedPlan}
        setSelectedPlan={setSelectedPlan}
        currentSubscription={currentSubscription}
        billingCycle={billingCycle}
        getPrice={getPrice}
      />

      {/* Payment Method Selection */}
      <PaymentMethodSelector
        selectedGateway={selectedGateway}
        onSelect={setSelectedGateway}
        selectedPlan={selectedPlan}
      />

      {/* Subscribe Button */}
      <SubscribeButton
        selectedPlan={selectedPlan}
        loading={loading}
        onSubscribe={onSubscribe}
        isDisabled={isButtonDisabled}
        buttonText={buttonText}
      />

      {/* Feature Comparison Table */}
      <FeatureComparison
        billingCycle={billingCycle}
        getPrice={getPrice}
      />

      {/* FAQ Section - Client-side only rendering */}
      <FAQSection />
    </div>
  );
}