import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkStatusPermission } from "../utils/auth";

/**
 * Get a status by ID
 * @route GET /api/status/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Status ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to view this status
    const permission = await checkStatusPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Fetch the status
    const { data, error } = await supabase
      .from("status")
      .select("*")
      .eq("id", id)
      .single();
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching status:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Update a status
 * @route PUT /api/status/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Status ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    const { updatedStatus } = body;
    
    if (!updatedStatus) {
      return NextResponse.json({ error: "Updated status data is required" }, { status: 400 });
    }
    
    const { name, color, count_statistics } = updatedStatus;
    
    // Check if user has permission to update this status
    const permission = await checkStatusPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Update the status
    const { data, error } = await supabase
      .from("status")
      .update({
        name: name,
        color: color,
        count_statistics: count_statistics,
      })
      .eq("id", id)
      .select();
    
    if (error) {
      console.error("Update error:", error);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Status updated successfully",
      data
    }, { status: 200 });
  } catch (error) {
    console.error("Error updating status:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Delete a status
 * @route DELETE /api/status/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Status ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to delete this status
    const permission = await checkStatusPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Delete the status
    const { error: deleteError } = await supabase
      .from("status")
      .delete()
      .eq("id", id);
    
    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Status deleted successfully"
    }, { status: 200 });
  } catch (error) {
    console.error("Error deleting status:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
