"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";
import { useProfile } from "./hooks/useProfile";
import { EditProfileDialog } from "./components/EditProfileDialog";
import { ProfileHeader } from "./components/display/ProfileHeader";
import { ProfessionalDetails } from "./components/display/ProfessionalDetails";
import { ApiKeysDisplay } from "./components/display/ApiKeysDisplay";
import { SocialLinksDisplay } from "./components/display/SocialLinksDisplay";
import { ProfileLoading } from "./components/ProfileLoading";

export default function ProfileDetailsPage() {
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const { loading, profileData, updateProfile } = useProfile();

  if (loading) {
    return <ProfileLoading />;
  }

  return (
    <div
      className={`transition-all duration-500 ease-in-out p-2 sm:p-4 md:p-6 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden max-w-8xl mx-auto`}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4 mb-4 md:mb-6">
        <h1 className="text-xl sm:text-2xl md:text-3xl font-bold">Profile Details</h1>
        <div className="flex items-center gap-2 sm:gap-4 w-full sm:w-auto">
          <Button
            variant="outline"
            className="flex-1 sm:flex-none h-9 text-sm"
            onClick={() => setIsEditDialogOpen(true)}
          >
            Edit Profile
          </Button>
        </div>
      </div>

      {/* Edit Profile Dialog */}
      <EditProfileDialog
        profileData={profileData}
        onProfileUpdate={updateProfile}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />

      {/* Profile Content */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
        <ProfileHeader profileData={profileData} />
        <ProfessionalDetails profileData={profileData} />
        <ApiKeysDisplay profileData={profileData} />
      </div>

      {/* Social Links */}
      <SocialLinksDisplay profileData={profileData} />
    </div>
  );
}