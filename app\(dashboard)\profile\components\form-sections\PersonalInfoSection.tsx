"use client";

import React, { useState } from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload } from "lucide-react";
import { ProfileFormData } from '../../types/profile';
import { useImageUpload } from '../../hooks/useImageUpload';
import { getUserInitials } from '../../utils/profile';

interface PersonalInfoSectionProps {
  formData: ProfileFormData;
  updateFormData: (field: keyof ProfileFormData, value: string) => void;
  uploadingImage: boolean;
}

export function PersonalInfoSection({
  formData,
  updateFormData,
  uploadingImage,
}: PersonalInfoSectionProps) {
  const { uploadImage, deleteImage } = useImageUpload();
  const [previewImage, setPreviewImage] = useState(formData.avatar);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const publicUrl = await uploadImage(file);
      setPreviewImage(publicUrl);
      updateFormData('avatar', publicUrl);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const handleImageDelete = async () => {
    if (!formData.avatar) return;

    try {
      await deleteImage(formData.avatar);
      setPreviewImage("");
      updateFormData('avatar', "");
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const userInitials = getUserInitials(formData.firstName, formData.lastName);

  return (
    <>
      {/* Profile Picture */}
      <div className="space-y-3">
        <Label className="text-sm font-medium">Profile Picture</Label>
        <div className="flex flex-col sm:flex-row items-center gap-4">
          <Avatar className="h-16 w-16 sm:h-20 sm:w-20 ring-2 ring-gray-100 dark:ring-gray-800">
            <AvatarImage src={previewImage} alt="Preview" className="object-cover" />
            <AvatarFallback className="text-sm font-semibold bg-gradient-to-br from-primary/10 to-accent/10">
              {userInitials}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <Label htmlFor="avatar" className="cursor-pointer">
              <div className="flex items-center justify-center gap-2 border rounded-md p-2 sm:p-3 hover:bg-accent transition-colors text-sm">
                <Upload className="h-4 w-4" />
                <span>
                  {uploadingImage ? "Uploading..." : "Upload"}
                </span>
              </div>
              <Input
                id="avatar"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
                disabled={uploadingImage}
              />
            </Label>
            {previewImage && (
              <button
                type="button"
                onClick={handleImageDelete}
                className="px-3 py-2 text-sm text-red-600 border border-red-200 rounded-md hover:bg-red-50 dark:hover:bg-red-950 transition-colors"
                disabled={uploadingImage}
              >
                Remove
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Name Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName" className="text-sm font-medium">First Name</Label>
          <Input
            id="firstName"
            value={formData.firstName}
            onChange={(e) => updateFormData('firstName', e.target.value)}
            className="h-9 text-sm"
            placeholder="Enter first name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="lastName" className="text-sm font-medium">Last Name</Label>
          <Input
            id="lastName"
            value={formData.lastName}
            onChange={(e) => updateFormData('lastName', e.target.value)}
            className="h-9 text-sm"
            placeholder="Enter last name"
          />
        </div>
      </div>

      {/* Phone */}
      <div className="space-y-2">
        <Label htmlFor="phone" className="text-sm font-medium">Phone</Label>
        <Input
          id="phone"
          type="tel"
          value={formData.phone}
          onChange={(e) => updateFormData('phone', e.target.value)}
          className="h-9 text-sm"
          placeholder="Enter phone number"
        />
      </div>

      {/* Date of Birth */}
      <div className="space-y-2">
        <Label htmlFor="dateOfBirth" className="text-sm font-medium">Date of Birth</Label>
        <Input
          id="dateOfBirth"
          type="date"
          value={formData.dateOfBirth}
          onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
          className="h-9 text-sm"
        />
      </div>
    </>
  );
}
