import { useState } from "react";
import { toast } from "sonner";
import {
  useWebhookMutation,
  useUpdateWebhookMutation,
  useDeleteWebhookMutation,
  useChangeWebhookStatusMutation,
} from "@/lib/store/services/webhooks";
import { LeadSource, LeadSourceFormData, WorkspaceData } from "../types/leadSources";
import {
  formatLeadSourceForCreation,
  formatLeadSourceForUpdate,
  copyToClipboard,
  getErrorMessage,
} from "../utils/leadSourceUtils";
import {
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
} from "../constants/leadSources";

interface UseLeadSourceActionsProps {
  sources: LeadSource[];
  setSources: React.Dispatch<React.SetStateAction<LeadSource[]>>;
  workspaceData: WorkspaceData | undefined;
}

export const useLeadSourceActions = ({
  sources,
  setSources,
  workspaceData,
}: UseLeadSourceActionsProps) => {
  // API mutations
  const [createWebhook, { isLoading: isCreating }] = useWebhookMutation();
  const [updateWebhook, { isLoading: isUpdating }] = useUpdateWebhookMutation();
  const [deleteWebhook, { isLoading: isDeleting }] = useDeleteWebhookMutation();
  const [changeWebhookStatus, { isLoading: isStatusChanging }] = useChangeWebhookStatusMutation();

  // Local loading states
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    statusChanging: false,
  });

  /**
   * Create a new lead source
   */
  const createLeadSource = async (data: LeadSourceFormData): Promise<boolean> => {
    if (!workspaceData?.data?.id) {
      toast.error(ERROR_MESSAGES.NO_WORKSPACE);
      return false;
    }

    try {
      setLoadingStates(prev => ({ ...prev, creating: true }));

      const newSource = formatLeadSourceForCreation(data, workspaceData.data.id);

      await createWebhook({
        status: true,
        type: data.type,
        name: data.name,
        webhook_url: newSource.webhook_url,
        workspace_id: workspaceData.data.id,
        workspaceId: workspaceData.data.id,
      }).unwrap();

      // Update local state
      setSources(prevSources => [...prevSources, newSource as LeadSource]);

      toast.success(SUCCESS_MESSAGES.CREATED);
      return true;
    } catch (error: any) {
      const errorMessage = getErrorMessage(error, ERROR_MESSAGES.CREATE_FAILED);

      if (error.status === 409) {
        toast.error(ERROR_MESSAGES.DUPLICATE_NAME);
      } else if (error.status === 403) {
        toast.error(ERROR_MESSAGES.NO_PERMISSION);
      } else {
        toast.error(errorMessage);
      }

      return false;
    } finally {
      setLoadingStates(prev => ({ ...prev, creating: false }));
    }
  };

  /**
   * Update an existing lead source
   */
  const updateLeadSource = async (
    sourceId: string,
    data: LeadSourceFormData
  ): Promise<boolean> => {
    try {
      setLoadingStates(prev => ({ ...prev, updating: true }));

      await updateWebhook({
        data,
        id: sourceId,
        workspaceId: workspaceData?.data?.id || ""
      }).unwrap();

      // Update local state
      setSources(prevSources =>
        prevSources.map(source =>
          source.id === sourceId
            ? { ...source, ...data, description: data.description || "" }
            : source
        )
      );

      toast.success(SUCCESS_MESSAGES.UPDATED);
      return true;
    } catch (error: any) {
      const errorMessage = getErrorMessage(error, ERROR_MESSAGES.UPDATE_FAILED);
      toast.error(errorMessage);
      return false;
    } finally {
      setLoadingStates(prev => ({ ...prev, updating: false }));
    }
  };

  /**
   * Delete a lead source
   */
  const deleteLeadSource = async (sourceId: string): Promise<boolean> => {
    try {
      setLoadingStates(prev => ({ ...prev, deleting: true }));

      await deleteWebhook({
        id: sourceId,
        workspaceId: workspaceData?.data?.id || ""
      }).unwrap();

      // Update local state
      setSources(prevSources => prevSources.filter(source => source.id !== sourceId));

      toast.success(SUCCESS_MESSAGES.DELETED);
      return true;
    } catch (error: any) {
      const errorMessage = getErrorMessage(error, ERROR_MESSAGES.DELETE_FAILED);
      toast.error(errorMessage);
      return false;
    } finally {
      setLoadingStates(prev => ({ ...prev, deleting: false }));
    }
  };

  /**
   * Toggle webhook status
   */
  const toggleWebhookStatus = async (sourceId: string): Promise<boolean> => {
    try {
      setLoadingStates(prev => ({ ...prev, statusChanging: true }));

      const currentSource = sources.find(source => source.id === sourceId);
      if (!currentSource) return false;

      const newStatus = !currentSource.status;

      await changeWebhookStatus({
        id: sourceId,
        status: newStatus,
        workspaceId: workspaceData?.data?.id || "",
      }).unwrap();

      // Update local state
      setSources(prevSources =>
        prevSources.map(source =>
          source.id === sourceId ? { ...source, status: newStatus } : source
        )
      );

      toast.success(SUCCESS_MESSAGES.STATUS_UPDATED);
      return true;
    } catch (error: any) {
      const errorMessage = getErrorMessage(error, ERROR_MESSAGES.STATUS_UPDATE_FAILED);
      toast.error(errorMessage);
      return false;
    } finally {
      setLoadingStates(prev => ({ ...prev, statusChanging: false }));
    }
  };

  /**
   * Copy webhook URL to clipboard
   */
  const copyWebhookUrl = async (webhookUrl: string): Promise<void> => {
    const success = await copyToClipboard(webhookUrl);
    if (success) {
      toast.success(SUCCESS_MESSAGES.WEBHOOK_COPIED);
    } else {
      toast.error("Failed to copy webhook URL");
    }
  };

  return {
    // Actions
    createLeadSource,
    updateLeadSource,
    deleteLeadSource,
    toggleWebhookStatus,
    copyWebhookUrl,

    // Loading states
    isCreating: isCreating || loadingStates.creating,
    isUpdating: isUpdating || loadingStates.updating,
    isDeleting: isDeleting || loadingStates.deleting,
    isStatusChanging: isStatusChanging || loadingStates.statusChanging,
  };
};
