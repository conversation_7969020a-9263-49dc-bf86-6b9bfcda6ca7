"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check } from "lucide-react";
import { SUBSCRIPTION_PLANS, PlanTier, UserSubscription } from "@/lib/types/subscription";

interface PricingSectionProps {
  selectedPlan: PlanTier;
  setSelectedPlan: (plan: PlanTier) => void;
  currentSubscription: UserSubscription | null;
  billingCycle: 'monthly' | 'yearly';
  getPrice: (basePrice: number) => string;
}

const PricingSection: React.FC<PricingSectionProps> = ({
  selectedPlan,
  setSelectedPlan,
  currentSubscription,
  billingCycle,
  getPrice
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 w-full">
      {Object.values(SUBSCRIPTION_PLANS).map((plan) => (
        <Card
          key={plan.id}
          className={`flex flex-col h-full transition-all duration-300 hover:shadow-lg
            ${selectedPlan === plan.id
              ? 'border-primary shadow-md ring-1 ring-primary/20 relative z-10 scale-[1.02]'
              : 'border-border hover:border-primary/30'}`}
        >
          {/* Always reserve space for the badge to prevent layout shifts */}
          <div className="h-4 relative">
            {currentSubscription?.planId === plan.id && (
              <div className="absolute -top-4 left-0 right-0 flex justify-center">
                <Badge className="bg-green-600 text-white px-3 py-1 rounded-full shadow-sm">
                  Current Plan
                </Badge>
              </div>
            )}
          </div>

          <CardHeader className="pb-4 pt-6">
            <div className="text-center">
              <CardTitle className="text-xl md:text-2xl">{plan.name}</CardTitle>
              <CardDescription className="mt-2">
                {plan.id === 'starter' ? (
                  <span className="text-lg font-medium">Free forever</span>
                ) : (
                  <div className="flex items-center justify-center">
                    <span className="text-3xl font-bold">${getPrice(plan.price)}</span>
                    <span className="text-sm text-muted-foreground ml-1">
                      {billingCycle === 'monthly' ? '/month' : '/year'}
                    </span>
                  </div>
                )}
              </CardDescription>
            </div>
          </CardHeader>

          <CardContent className="flex-grow">
            <div className="h-px w-full bg-border mb-6"></div>
            <ul className="space-y-4 text-sm">
              {plan.features.map((feature, index) => (
                <li key={index} className="flex items-start">
                  <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5 flex-shrink-0">
                    <Check className="h-3 w-3 text-primary" />
                  </div>
                  <span>{feature}</span>
                </li>
              ))}
            </ul>
          </CardContent>

          <CardFooter className="pt-2 pb-6">
            <Button
              className="w-full py-6"
              variant={selectedPlan === plan.id ? "default" : "outline"}
              onClick={() => setSelectedPlan(plan.id)}
              size="lg"
            >
              <span>
                {selectedPlan === plan.id
                  ? currentSubscription?.planId === plan.id
                    ? "Current Plan"
                    : "Selected"
                  : "Select Plan"}
              </span>
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>
  );
};

export default PricingSection;
