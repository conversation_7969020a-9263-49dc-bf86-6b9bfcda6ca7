import '../styles/globals.css';
import type { AppProps } from 'next/app';
import { Provider } from 'react-redux';
import { store } from '../lib/store/store';
import { startCleanupSchedule } from '../lib/initCleanup';

// Start cleanup schedule in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  startCleanupSchedule();
}

export default function App({ Component, pageProps }: AppProps) {
  return (
    <Provider store={store}>
      <Component {...pageProps} />
    </Provider>
  );
}
