import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface FormsSkeletonProps {
  isCollapsed?: boolean;
}

export const FormsSkeleton: React.FC<FormsSkeletonProps> = ({ isCollapsed = false }) => {
  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        {/* Header Skeleton */}
        <CardHeader className="flex flex-row justify-between items-center bg-gray-100 dark:bg-gray-800 md:bg-white md:dark:bg-gray-900">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-28 rounded-md" />
        </CardHeader>

        <CardContent className="p-4 md:p-6">
          {/* Tabs Skeleton */}
          <div className="flex space-x-1 mb-6">
            <Skeleton className="h-10 w-24 rounded-md" />
            <Skeleton className="h-10 w-28 rounded-md" />
            <Skeleton className="h-10 w-20 rounded-md" />
          </div>

          {/* Content Skeleton */}
          <div className="space-y-6">
            {/* Forms Grid Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="overflow-hidden border border-gray-200 dark:border-gray-800">
                  <CardHeader className="pb-2">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-full" />
                  </CardHeader>
                  <CardContent className="pb-2">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-4 w-12" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                      <Skeleton className="h-4 w-20" />
                    </div>
                  </CardContent>
                  <div className="flex justify-between p-4 pt-0">
                    <Skeleton className="h-8 w-16 rounded-md" />
                    <div className="flex gap-2">
                      <Skeleton className="h-8 w-8 rounded-md" />
                      <Skeleton className="h-8 w-8 rounded-md" />
                      <Skeleton className="h-8 w-8 rounded-md" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export const FormEditorSkeleton: React.FC<FormsSkeletonProps> = ({ isCollapsed = false }) => {
  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        {/* Header Skeleton */}
        <CardHeader className="flex flex-row justify-between items-center bg-gray-100 dark:bg-gray-800 md:bg-white md:dark:bg-gray-900">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-28 rounded-md" />
        </CardHeader>

        <CardContent className="p-4 md:p-6">
          {/* Tabs Skeleton */}
          <div className="flex space-x-1 mb-6">
            <Skeleton className="h-10 w-24 rounded-md" />
            <Skeleton className="h-10 w-28 rounded-md" />
            <Skeleton className="h-10 w-20 rounded-md" />
          </div>

          {/* Editor Content Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Form Fields */}
            <div className="space-y-6">
              {/* Form Name */}
              <div>
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-10 w-full rounded-md" />
              </div>

              {/* Description */}
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-20 w-full rounded-md" />
              </div>

              {/* Lead Source */}
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-10 w-full rounded-md" />
                <Skeleton className="h-3 w-3/4 mt-1" />
              </div>

              {/* Active Switch */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <Skeleton className="h-4 w-16 mb-1" />
                  <Skeleton className="h-3 w-48" />
                </div>
                <Skeleton className="h-6 w-11 rounded-full" />
              </div>
            </div>

            {/* Right Column - Code Editor */}
            <div className="space-y-6">
              <div className="border rounded-md overflow-hidden">
                {/* Editor Tabs */}
                <div className="bg-muted p-2 border-b">
                  <div className="flex space-x-1">
                    <Skeleton className="h-8 w-16 rounded-md" />
                    <Skeleton className="h-8 w-16 rounded-md" />
                    <Skeleton className="h-8 w-20 rounded-md" />
                  </div>
                </div>

                {/* Editor Content */}
                <div className="p-4">
                  <div className="space-y-2">
                    {Array.from({ length: 15 }).map((_, index) => (
                      <Skeleton 
                        key={index} 
                        className="h-4 w-full" 
                        style={{ width: `${Math.random() * 40 + 60}%` }} 
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-6">
            <Skeleton className="h-10 w-20 rounded-md" />
            <div className="flex gap-2">
              <Skeleton className="h-10 w-24 rounded-md" />
              <Skeleton className="h-10 w-28 rounded-md" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export const FormPreviewSkeleton: React.FC<FormsSkeletonProps> = ({ isCollapsed = false }) => {
  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        {/* Header Skeleton */}
        <CardHeader className="flex flex-row justify-between items-center bg-gray-100 dark:bg-gray-800 md:bg-white md:dark:bg-gray-900">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-10 w-28 rounded-md" />
        </CardHeader>

        <CardContent className="p-4 md:p-6">
          {/* Tabs Skeleton */}
          <div className="flex space-x-1 mb-6">
            <Skeleton className="h-10 w-24 rounded-md" />
            <Skeleton className="h-10 w-28 rounded-md" />
            <Skeleton className="h-10 w-20 rounded-md" />
          </div>

          {/* Preview Content */}
          <div className="space-y-6">
            {/* Form Preview Card */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32 mb-2" />
                <Skeleton className="h-4 w-64" />
              </CardHeader>
              <CardContent>
                <div className="flex justify-center mb-4">
                  <Skeleton className="h-10 w-48 rounded-md" />
                </div>
                <div className="border rounded-lg overflow-hidden">
                  <Skeleton className="h-[600px] w-full" />
                </div>
              </CardContent>
            </Card>

            {/* Embed Code Card */}
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-28 mb-2" />
                <Skeleton className="h-4 w-72" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-10 w-full rounded-md" />
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Mobile Forms Skeleton
export const FormsMobileSkeleton: React.FC = () => {
  return (
    <div className="p-4 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-6 w-32" />
        <Skeleton className="h-8 w-24 rounded-md" />
      </div>

      {/* Tabs */}
      <div className="flex space-x-2 overflow-x-auto">
        {Array.from({ length: 3 }).map((_, index) => (
          <Skeleton key={index} className="h-8 w-20 rounded-md flex-shrink-0" />
        ))}
      </div>

      {/* Content */}
      <div className="space-y-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index} className="p-4">
            <div className="space-y-3">
              <Skeleton className="h-5 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <div className="flex justify-between items-center">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
              </div>
              <div className="flex justify-between">
                <Skeleton className="h-8 w-16 rounded-md" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-8 rounded-md" />
                  <Skeleton className="h-8 w-8 rounded-md" />
                  <Skeleton className="h-8 w-8 rounded-md" />
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
