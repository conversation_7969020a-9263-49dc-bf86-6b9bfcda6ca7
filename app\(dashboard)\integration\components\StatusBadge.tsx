import React from "react";
import { Badge } from "@/components/ui/badge";
import { StatusBadgeProps } from "../types/integration";
import { getStatusBadgeColor, getStatusDisplayName } from "../utils/integrationUtils";

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  return (
    <Badge className={getStatusBadgeColor(status)}>
      {getStatusDisplayName(status)}
    </Badge>
  );
};
