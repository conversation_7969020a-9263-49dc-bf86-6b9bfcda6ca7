"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowLeft, HelpCircle } from "lucide-react";

export default function SubscriptionCancelPage() {
  const router = useRouter();

  return (
    <div className="container max-w-2xl py-12">
      <Card className="text-center overflow-hidden">
        <div className="bg-gradient-to-r from-red-500/20 to-red-600/20 h-16"></div>
        <CardHeader className="relative pt-12 pb-6">
          <div className="absolute -top-10 left-1/2 transform -translate-x-1/2">
            <div className="h-20 w-20 rounded-full bg-red-100 flex items-center justify-center border-4 border-background">
              <XCircle className="h-10 w-10 text-red-500" />
            </div>
          </div>
          <CardTitle className="text-2xl md:text-3xl">Subscription Cancelled</CardTitle>
          <CardDescription className="text-base mt-2">
            Your subscription process was not completed
          </CardDescription>
        </CardHeader>

        <CardContent>
          <div className="bg-muted/30 rounded-lg p-6 mb-6">
            <p className="mb-4">
              You have cancelled the subscription process. No charges have been made to your account.
            </p>

            <div className="flex items-start gap-3 mt-6 text-left bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <HelpCircle className="h-5 w-5 text-yellow-500 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-yellow-700">Need help?</p>
                <p className="text-sm text-yellow-600 mt-1">
                  If you encountered any issues during the subscription process, please contact our support team for assistance.
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-3">
            <Button
              className="w-full py-6 gap-2"
              onClick={() => router.push("/dashboard/subscription")}
              size="lg"
            >
              Try Again
            </Button>

            <Button
              variant="outline"
              className="w-full gap-2"
              onClick={() => router.push("/dashboard")}
            >
              <ArrowLeft className="h-4 w-4" />
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
