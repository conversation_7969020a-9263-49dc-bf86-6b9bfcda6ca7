import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../../../utils/auth";

/**
 * Get total leads count and conversion metrics for a workspace
 * @route GET /api/workspace/leads/total/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const workspaceId = params.workspaceId;

    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }

    // Check if user has permission to view this workspace
    const permission = await checkWorkspacePermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }

    try {
      // Convert workspaceId to number for the database function
      const workspaceIdNumber = parseInt(workspaceId);

      if (isNaN(workspaceIdNumber)) {
        return NextResponse.json({
          error: "Invalid workspace ID format"
        }, { status: 400 });
      }

      console.log("Calculating conversion metrics for workspace ID:", workspaceId, "as number:", workspaceIdNumber);

      const { data, error } = await supabase.rpc(
        "calculate_conversion_metrics_with_monthly",
        {
          workspace_id: workspaceIdNumber,
        }
      );

      // Log the result for debugging
      console.log("Conversion metrics result:", { data, error });

      if (error) {
        console.error("Error calculating conversion metrics:", error);
        return NextResponse.json({ error: error.message }, { status: 400 });
      }

      // Check if data exists and has at least one element
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.error("No conversion metrics data returned");
        return NextResponse.json({
          conversion_rate: 0,
          total_leads: 0,
          converted_leads: 0,
          monthly_stats: []
        }, { status: 200 });
      }

      // Format the conversion rate to ensure it's a number
      const result = data[0];

      // Log the result for debugging
      console.log("Conversion metrics result data[0]:", result);

      if (result && typeof result.conversion_rate !== 'undefined') {
        // Ensure conversion_rate is a number
        result.conversion_rate = parseFloat(String(result.conversion_rate));
      }

      // Ensure monthly_stats is properly formatted
      if (result.monthly_stats && Array.isArray(result.monthly_stats)) {
        console.log("Monthly stats before processing:", result.monthly_stats);

        // Process each monthly stat to ensure proper formatting
        result.monthly_stats = result.monthly_stats.map((stat: any) => {
          return {
            month: stat.month || 'Unknown',
            totalLeads: typeof stat.totalLeads === 'number' ? stat.totalLeads : parseInt(stat.totalLeads || '0'),
            convertedLeads: typeof stat.convertedLeads === 'number' ? stat.convertedLeads : parseInt(stat.convertedLeads || '0'),
            conversionRate: stat.conversionRate || '0%'
          };
        });

        console.log("Monthly stats after processing:", result.monthly_stats);
      } else {
        result.monthly_stats = [];
      }

      return NextResponse.json(result, { status: 200 });
    } catch (error) {
      console.error("Error calculating conversion metrics:", error);
      return NextResponse.json({
        error: "Failed to calculate conversion metrics"
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching total leads count:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
