import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader } from "@/components/ui/loader";
import { Plus, Code, Link, Trash } from "lucide-react";
import EmbedCodeDialog from "./EmbedCodeDialog";
import { FormListProps } from "../types/forms";
import { 
  BUTTON_LABELS, 
  EMPTY_STATE_MESSAGES 
} from "../constants/forms";
import { 
  formatDate, 
  getStatusColor, 
  getStatusText, 
  getFormIdForEmbed 
} from "../utils/formUtils";

export const FormsList: React.FC<FormListProps> = ({
  forms,
  isLoading,
  onCreateNew,
  onEdit,
  onDelete,
  onGetEmbedCode,
  onPreview,
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center py-10">
        <Loader size="lg" variant="primary" />
      </div>
    );
  }

  if (!forms || forms.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-gray-500 mb-4">{EMPTY_STATE_MESSAGES.NO_FORMS}</p>
        <p className="text-sm text-muted-foreground mb-6">
          {EMPTY_STATE_MESSAGES.NO_FORMS_DESCRIPTION}
        </p>
        <Button variant="accent" onClick={onCreateNew}>
          <Plus className="mr-2 h-4 w-4" />
          {BUTTON_LABELS.CREATE_FIRST}
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {forms.map((form) => (
        <Card key={form.id} className="overflow-hidden border border-gray-200 dark:border-gray-800 hover:shadow-lg transition-shadow duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg text-gray-900 dark:text-white">
              {form.name}
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              {form.description || "No description"}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="pb-2">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <span className="text-sm mr-2 text-gray-600 dark:text-gray-400">
                  Status:
                </span>
                <span className={`text-sm font-medium ${getStatusColor(form.is_active)}`}>
                  {getStatusText(form.is_active)}
                </span>
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(form.created_at)}
              </div>
            </div>
          </CardContent>
          
          <div className="flex justify-between p-4 pt-0">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => onEdit(form.id)}
              className="hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              {BUTTON_LABELS.EDIT}
            </Button>
            
            <div className="flex gap-2">
              <EmbedCodeDialog 
                formId={getFormIdForEmbed(null, form.id)} 
                formName={form.name}
              >
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="hover:bg-gray-50 dark:hover:bg-gray-800"
                  title="Get embed code"
                >
                  <Code className="h-4 w-4" />
                </Button>
              </EmbedCodeDialog>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPreview(form.id)}
                className="hover:bg-gray-50 dark:hover:bg-gray-800"
                title="Preview form"
              >
                <Link className="h-4 w-4" />
              </Button>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => onDelete(form.id)}
                className="hover:bg-red-50 dark:hover:bg-red-900/20"
                title="Delete form"
              >
                <Trash className="h-4 w-4 text-destructive" />
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
