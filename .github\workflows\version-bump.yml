name: Version Bump

on:
  workflow_dispatch:
    inputs:
      version_type:
        description: 'Type of version bump (major, minor, patch)'
        required: true
        default: 'patch'
        type: choice
        options:
          - major
          - minor
          - patch

jobs:
  bump-version:
    name: Bump Version
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Install dependencies
        run: npm ci

      - name: Get current version
        id: current_version
        run: echo "VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT

      - name: Bump version
        id: bump_version
        run: |
          if [ "${{ github.event.inputs.version_type }}" == "major" ]; then
            npm version major --no-git-tag-version
          elif [ "${{ github.event.inputs.version_type }}" == "minor" ]; then
            npm version minor --no-git-tag-version
          else
            npm version patch --no-git-tag-version
          fi
          echo "NEW_VERSION=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT

      - name: Update CHANGELOG.md
        run: |
          DATE=$(date +%Y-%m-%d)
          VERSION=${{ steps.bump_version.outputs.NEW_VERSION }}
          
          # Create a temporary file
          touch temp_changelog.md
          
          # Add header and new version section
          echo "# Changelog" > temp_changelog.md
          echo "" >> temp_changelog.md
          echo "All notable changes to the INFILABS CRM project will be documented in this file." >> temp_changelog.md
          echo "" >> temp_changelog.md
          echo "The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)," >> temp_changelog.md
          echo "and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html)." >> temp_changelog.md
          echo "" >> temp_changelog.md
          echo "## [$VERSION] - $DATE" >> temp_changelog.md
          echo "" >> temp_changelog.md
          echo "### Added" >> temp_changelog.md
          echo "- " >> temp_changelog.md
          echo "" >> temp_changelog.md
          echo "### Changed" >> temp_changelog.md
          echo "- " >> temp_changelog.md
          echo "" >> temp_changelog.md
          echo "### Fixed" >> temp_changelog.md
          echo "- " >> temp_changelog.md
          echo "" >> temp_changelog.md
          
          # Append existing changelog content (skipping the header)
          tail -n +7 CHANGELOG.md >> temp_changelog.md
          
          # Replace the original file
          mv temp_changelog.md CHANGELOG.md

      - name: Update version in health check API
        run: |
          VERSION=${{ steps.bump_version.outputs.NEW_VERSION }}
          if [ -f "app/api/health/route.ts" ]; then
            sed -i "s/version: process.env.NEXT_PUBLIC_APP_VERSION || \"[0-9]*\.[0-9]*\.[0-9]*\"/version: process.env.NEXT_PUBLIC_APP_VERSION || \"$VERSION\"/" app/api/health/route.ts
          fi

      - name: Commit changes
        run: |
          git add package.json package-lock.json CHANGELOG.md app/api/health/route.ts
          git commit -m "Bump version to ${{ steps.bump_version.outputs.NEW_VERSION }}"
          git push origin HEAD:${{ github.ref }}

      - name: Create Tag
        run: |
          git tag -a v${{ steps.bump_version.outputs.NEW_VERSION }} -m "Version ${{ steps.bump_version.outputs.NEW_VERSION }}"
          git push origin v${{ steps.bump_version.outputs.NEW_VERSION }}
