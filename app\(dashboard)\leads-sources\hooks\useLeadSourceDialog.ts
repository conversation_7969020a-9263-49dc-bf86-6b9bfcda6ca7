import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { leadSourceSchema, LeadSourceFormData } from "../schemas/leadSourceSchema";
import { LeadSource } from "../types/leadSources";
import { FORM_DEFAULT_VALUES, ERROR_MESSAGES } from "../constants/leadSources";

type DialogMode = "create" | "edit" | "delete" | null;

interface UseLeadSourceDialogProps {
  workspaceData: any;
}

export const useLeadSourceDialog = ({ workspaceData }: UseLeadSourceDialogProps) => {
  // Dialog state
  const [dialogMode, setDialogMode] = useState<DialogMode>(null);
  const [selectedSource, setSelectedSource] = useState<LeadSource | null>(null);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  // Form management
  const form = useForm<LeadSourceFormData>({
    resolver: zodResolver(leadSourceSchema),
    defaultValues: FORM_DEFAULT_VALUES,
  });

  /**
   * Reset dialog and form state
   */
  const resetDialog = () => {
    form.reset(FORM_DEFAULT_VALUES);
    setSelectedSource(null);
    setDialogMode(null);
  };

  /**
   * Open create dialog
   */
  const openCreateDialog = () => {
    if (!workspaceData?.data?.id) {
      toast.error(ERROR_MESSAGES.NO_WORKSPACE);
      return;
    }
    resetDialog();
    setDialogMode("create");
  };

  /**
   * Open edit dialog
   */
  const openEditDialog = (source: LeadSource) => {
    form.reset({
      name: source.name,
      type: source.type,
      description: source.description || "",
    });
    setSelectedSource(source);
    setDialogMode("edit");
  };

  /**
   * Open delete dialog
   */
  const openDeleteDialog = (source: LeadSource) => {
    setSelectedSource(source);
    setDialogMode("delete");
  };

  /**
   * Toggle expanded row for mobile view
   */
  const toggleRow = (sourceId: string) => {
    setExpandedRow(expandedRow === sourceId ? null : sourceId);
  };

  /**
   * Check if dialog is open
   */
  const isDialogOpen = (mode: DialogMode) => {
    return dialogMode === mode;
  };

  return {
    // State
    dialogMode,
    selectedSource,
    expandedRow,
    form,

    // Actions
    resetDialog,
    openCreateDialog,
    openEditDialog,
    openDeleteDialog,
    toggleRow,
    isDialogOpen,
  };
};
