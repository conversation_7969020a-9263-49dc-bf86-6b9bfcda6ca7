import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../utils/auth";

/**
 * Get a workspace by ID
 * @route GET /api/workspace/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    // Fetch the workspace
    const { data: workspace, error: workspaceError } = await supabase
      .from("workspaces")
      .select("*")
      .eq("id", id)
      .single();
    
    if (workspaceError) {
      return NextResponse.json({ error: workspaceError.message }, { status: 400 });
    }
    
    if (!workspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }
    
    // Check if the user is the owner
    if (workspace.owner_id === user.id) {
      return NextResponse.json({ 
        message: "Workspace fetched", 
        data: workspace 
      }, { status: 200 });
    }
    
    // Check if the user is a member
    const { data: membership, error: membershipError } = await supabase
      .from("workspace_members")
      .select("*")
      .eq("workspace_id", id)
      .eq("user_id", user.id)
      .single();
    
    if (membershipError) {
      return NextResponse.json({ error: membershipError.message }, { status: 500 });
    }
    
    if (!membership) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }
    
    // Return workspace data if the user is a member
    return NextResponse.json({ 
      message: "Workspace fetched", 
      data: workspace 
    }, { status: 200 });
  } catch (error) {
    console.error("Error fetching workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Update a workspace
 * @route PUT /api/workspace/[id]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    // Parse request body
    const body = await request.json();
    const { data } = body;
    
    if (!data || typeof data !== "object") {
      return NextResponse.json({ 
        error: "Valid data object is required" 
      }, { status: 400 });
    }
    
    // Ensure the workspace belongs to the user
    const { data: workspace, error: workspaceError } = await supabase
      .from("workspaces")
      .select("id")
      .eq("id", id)
      .eq("owner_id", user.id)
      .single();
    
    if (workspaceError || !workspace) {
      return NextResponse.json({ 
        error: "Workspace not found or access denied" 
      }, { status: 404 });
    }
    
    // Update the workspace
    const { data: updatedWorkspace, error: updateError } = await supabase
      .from("workspaces")
      .update(data)
      .eq("id", id)
      .eq("owner_id", user.id)
      .select();
    
    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Workspace updated successfully",
      data: updatedWorkspace
    }, { status: 200 });
  } catch (error) {
    console.error("Error updating workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Delete a workspace
 * @route DELETE /api/workspace/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    // Check if user is the workspace owner
    const { data: workspace, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", id)
      .single();
    
    if (workspaceError) {
      return NextResponse.json({ error: workspaceError.message }, { status: 400 });
    }
    
    if (!workspace) {
      return NextResponse.json({ error: "Workspace not found" }, { status: 404 });
    }
    
    if (workspace.owner_id !== user.id) {
      return NextResponse.json({ 
        error: "Only the workspace owner can delete it" 
      }, { status: 403 });
    }
    
    // Delete the workspace
    const { error: deleteError } = await supabase
      .from("workspaces")
      .delete()
      .eq("id", id);
    
    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Workspace deleted successfully"
    }, { status: 200 });
  } catch (error) {
    console.error("Error deleting workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
