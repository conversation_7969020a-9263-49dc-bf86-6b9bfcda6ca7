
"use client";

import React from "react";
import { useParams } from "next/navigation";
import {
  <PERSON>,
  CardHeader,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Users } from "lucide-react";
import { useGetActiveWorkspaceQuery } from "@/lib/store/services/workspace";

// Types and constants
import { MemberManagementProps } from "./types/member";
import { MEMBER_CONSTANTS } from "./constants/member";

// Custom hooks
import { useMemberManagement } from "./hooks/useMemberManagement";
import { useRoleManagement } from "./hooks/useRoleManagement";

// Components
import { InviteForm } from "./components/InviteForm";
import { MembersList } from "./components/MembersList";
import { EditRoleDialog } from "./components/EditRoleDialog";
import { DeleteMemberDialog } from "./components/DeleteMemberDialog";

export default function MemberManagement({
  members,
  onMemberAdd,
  onMemberDelete,
  onMemberUpdate,
  onInviteResend,
  isAdding,
  isDeleting,
  isResending,
  isLoading,
}: MemberManagementProps) {
  const searchParams = useParams();
  const { id: workspaceId }: any = searchParams;

  // Get active workspace data
  const { data: activeWorkspace } = useGetActiveWorkspaceQuery();

  // Custom hooks for member management
  const {
    newInviteEmail,
    setNewInviteEmail,
    newInviteRole,
    setNewInviteRole,
    resendingMembers,
    memberToDelete,
    handleInviteMember,
    handleDeleteMember,
    confirmDeleteMember,
    cancelDeleteMember,
    handleResendInvite,
    isInviteFormValid,
  } = useMemberManagement({
    onMemberAdd,
    onMemberDelete,
    onMemberUpdate,
    onInviteResend,
    isAdding,
    isDeleting,
  });

  // Custom hooks for role management
  const {
    selectedMember,
    isEditDialogOpen,
    openEditDialog,
    closeEditDialog,
    handleRoleUpdate,
  } = useRoleManagement({
    workspaceId,
    members,
    onMemberUpdate,
  });

  // Handle role change with proper typing
  const handleRoleChange = (role: string) => {
    setNewInviteRole(role);
  };

  return (
    <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
      <CardHeader className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg md:text-xl font-semibold">
              {MEMBER_CONSTANTS.UI_TEXT.TITLE}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground mt-1">
              {MEMBER_CONSTANTS.UI_TEXT.DESCRIPTION}
            </CardDescription>
          </div>
          <Users className="w-6 h-6 md:w-8 md:h-8 text-muted-foreground" />
        </div>
      </CardHeader>

      <CardContent className="p-4 md:p-6 pt-0 space-y-6">
        {/* Invite Form */}
        <InviteForm
          email={newInviteEmail}
          role={newInviteRole}
          onEmailChange={setNewInviteEmail}
          onRoleChange={handleRoleChange}
          onInvite={handleInviteMember}
          isLoading={isAdding}
          isValid={isInviteFormValid()}
        />

        {/* Members List */}
        <MembersList
          members={members}
          isLoading={isLoading}
          onMemberUpdate={onMemberUpdate}
          onMemberDelete={handleDeleteMember}
          onInviteResend={handleResendInvite}
          onEditRole={openEditDialog}
          isDeleting={isDeleting}
          isResending={isResending}
          resendingMembers={resendingMembers}
        />

        {/* Edit Role Dialog */}
        <EditRoleDialog
          member={selectedMember}
          isOpen={isEditDialogOpen}
          onClose={closeEditDialog}
          onSubmit={handleRoleUpdate}
          workspaceName={activeWorkspace?.data?.name}
        />

        {/* Delete Member Dialog */}
        <DeleteMemberDialog
          member={memberToDelete}
          isOpen={!!memberToDelete}
          onClose={cancelDeleteMember}
          onConfirm={confirmDeleteMember}
          isDeleting={isDeleting}
        />
      </CardContent>
    </Card>
  );
}
