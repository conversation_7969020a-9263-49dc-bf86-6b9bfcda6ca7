import React from 'react';
import { Loader2 } from 'lucide-react';
import { CONTACT_CONSTANTS } from '../constants/contact';

interface ContactLoadingProps {
  message?: string;
}

export function ContactLoading({ 
  message = CONTACT_CONSTANTS.MESSAGES.LOADING 
}: ContactLoadingProps) {
  return (
    <div className="flex items-center justify-center min-h-screen overflow-hidden">
      <div className="flex flex-col items-center gap-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-muted-foreground text-sm">{message}</p>
      </div>
    </div>
  );
}
