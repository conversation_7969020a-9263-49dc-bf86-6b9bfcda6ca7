-- Migration for table: payment_history (generated from API spec only)
CREATE TABLE IF NOT EXISTS "payment_history" (
  "id" uuid PRIMARY KEY NOT NULL,
  "user_id" uuid NOT NULL,
  "subscription_id" uuid,
  "payment_provider" text NOT NULL,
  "payment_id" text,
  "order_id" text,
  "amount" numeric NOT NULL,
  "currency" text NOT NULL,
  "status" text NOT NULL,
  "metadata" text,
  "created_at" text,
  "updated_at" text
);

-- Enable Row Level Security
ALTER TABLE "payment_history" ENABLE ROW LEVEL SECURITY;

