import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspaceAdminPermission } from "../utils/auth";
import { sendInvitationEmail } from "../utils/email";
import { createInvitationToken, validateEmail } from "../utils/invitation";
import { createAuditLog } from "../utils/audit";

/**
 * Send secure workspace invitation
 * @route POST /api/members/invite
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get parameters
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ 
        error: "Workspace ID is required" 
      }, { status: 400 });
    }
    
    // Check admin permission
    const permission = await checkWorkspaceAdminPermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Parse request body
    const { email, role = 'member' } = await request.json();
    
    // Validate input
    if (!email || !validateEmail(email)) {
      return NextResponse.json({ 
        error: "Valid email is required" 
      }, { status: 400 });
    }
    
    if (!['admin', 'member', 'viewer'].includes(role)) {
      return NextResponse.json({ 
        error: "Invalid role specified" 
      }, { status: 400 });
    }
    
    // Check if user is already a member
    const { data: existingMember } = await supabase
      .from("workspace_members")
      .select("id, status")
      .eq("workspace_id", workspaceId)
      .eq("email", email)
      .single();
    
    if (existingMember) {
      if (existingMember.status === 'accepted') {
        return NextResponse.json({
          error: "User is already a member of this workspace"
        }, { status: 400 });
      } else if (existingMember.status === 'pending') {
        return NextResponse.json({
          error: "User already has a pending invitation"
        }, { status: 400 });
      }
    }
    
    // Check for existing pending invitation
    const { data: existingInvitation } = await supabase
      .from("workspace_invitations")
      .select("id, expires_at")
      .eq("workspace_id", workspaceId)
      .eq("email", email)
      .is("accepted_at", null)
      .gt("expires_at", new Date().toISOString())
      .single();
    
    if (existingInvitation) {
      return NextResponse.json({
        error: "User already has a pending invitation"
      }, { status: 400 });
    }
    
    // Rate limiting check (max 10 invitations per hour per workspace)
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const { count: recentInvitations } = await supabase
      .from("workspace_invitations")
      .select("id", { count: 'exact' })
      .eq("workspace_id", workspaceId)
      .eq("invited_by", user.id)
      .gte("created_at", oneHourAgo);
    
    if (recentInvitations && recentInvitations >= 10) {
      return NextResponse.json({
        error: "Rate limit exceeded. Maximum 10 invitations per hour."
      }, { status: 429 });
    }
    
    // Generate secure invitation token
    const token = createInvitationToken();
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
    
    // Create invitation record
    const { data: invitation, error: invitationError } = await supabase
      .from("workspace_invitations")
      .insert({
        workspace_id: parseInt(workspaceId),
        email: email.toLowerCase(),
        role,
        token,
        invited_by: user.id,
        expires_at: expiresAt.toISOString()
      })
      .select()
      .single();
    
    if (invitationError) {
      console.error("Error creating invitation:", invitationError);
      return NextResponse.json({ 
        error: "Failed to create invitation" 
      }, { status: 500 });
    }
    
    // Get workspace details for email
    const { data: workspace } = await supabase
      .from("workspaces")
      .select("name")
      .eq("id", workspaceId)
      .single();
    
    // Send invitation email
    const emailSent = await sendInvitationEmail({
      email,
      token,
      workspaceName: workspace?.name || "Workspace",
      inviterName: user.user_metadata?.full_name || user.email,
      expiresAt
    });
    
    if (!emailSent) {
      // Delete the invitation if email failed
      await supabase
        .from("workspace_invitations")
        .delete()
        .eq("id", invitation.id);
      
      return NextResponse.json({
        error: "Failed to send invitation email"
      }, { status: 500 });
    }
    
    // Create audit log
    await createAuditLog({
      workspaceId: parseInt(workspaceId),
      action: 'invited',
      email,
      newRole: role,
      performedBy: user.id,
      metadata: {
        invitationId: invitation.id,
        expiresAt: expiresAt.toISOString()
      }
    });
    
    return NextResponse.json({
      message: "Invitation sent successfully",
      data: {
        id: invitation.id,
        email,
        role,
        expiresAt: expiresAt.toISOString()
      }
    }, { status: 201 });
    
  } catch (error) {
    console.error("Error sending invitation:", error);
    return NextResponse.json({ 
      error: "Internal server error" 
    }, { status: 500 });
  }
}
