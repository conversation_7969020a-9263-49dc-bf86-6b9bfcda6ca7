export const SUBSCRIPTION_CONSTANTS = {
  // Pricing
  YEARLY_DISCOUNT: 0.8, // 20% discount for yearly billing
  
  // Billing cycles
  BILLING_CYCLES: {
    MONTHLY: 'monthly' as const,
    YEARLY: 'yearly' as const,
  },
  
  // Plan durations in days
  PLAN_DURATIONS: {
    MONTHLY: 30,
    YEARLY: 365,
    FREE_PLAN: 365, // Free plan lasts 1 year
  },
  
  // Payment gateways
  PAYMENT_GATEWAYS: {
    STRIPE: 'stripe' as const,
    PAYPAL: 'paypal' as const,
    RAZORPAY: 'razorpay' as const,
  },
  
  // URLs
  URLS: {
    MANAGE_SUBSCRIPTION: '/dashboard/subscription/manage',
    SUCCESS_URL_TEMPLATE: (plan: string, cycle: string) => 
      `/dashboard/subscription/success?plan=${plan}&cycle=${cycle}`,
    CANCEL_URL: '/dashboard/subscription/cancel',
  },
  
  // Messages
  MESSAGES: {
    AUTH_REQUIRED: 'Please log in to subscribe',
    SUBSCRIPTION_UPDATED: 'Subscription updated successfully',
    PAYMENT_FAILED: 'There was an error processing your payment',
    LOAD_ERROR: 'Failed to load subscription data',
    PROCESSING: 'Processing subscription...',
    NO_CHARGE_UNTIL_CONFIRM: "You won't be charged until you confirm your subscription",
    SECURE_PAYMENT: 'All payment information is securely processed and encrypted',
  },
  
  // Button texts
  BUTTON_TEXTS: {
    PROCESSING: 'Processing...',
    SWITCH_TO_FREE: 'Switch to Free Plan',
    CURRENT_PLAN: 'Current Plan',
    SUBSCRIBE_NOW: 'Subscribe Now',
    MANAGE_SUBSCRIPTION: 'Manage Subscription',
  },
  
  // Feature icons mapping
  FEATURE_ICONS: {
    WORKSPACE_LIMIT: 'Users',
    LEAD_MANAGEMENT: 'CheckCircle',
    AI_INTEGRATION: 'Zap',
    EMAIL_MARKETING: 'Mail',
    SMS_MARKETING: 'MessageSquare',
    PRIORITY_SUPPORT: 'Shield',
  },
} as const;

export const PAYMENT_GATEWAY_CONFIG = {
  stripe: {
    name: 'Stripe',
    description: 'Credit/Debit Card',
    color: '#6772E5',
    icon: 'stripe',
  },
  paypal: {
    name: 'PayPal',
    description: 'PayPal Account',
    color: '#0070BA',
    icon: 'paypal',
  },
  razorpay: {
    name: 'Razorpay',
    description: 'Multiple Options',
    color: '#3395FF',
    icon: 'razorpay',
  },
} as const;
