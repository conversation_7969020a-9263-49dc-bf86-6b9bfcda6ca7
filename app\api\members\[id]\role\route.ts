import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspaceAdminPermission } from "../../utils/auth";

/**
 * Update a member's role
 * @route PUT /api/members/[id]/role
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    const { role } = body;
    
    if (!role) {
      return NextResponse.json({ error: "Role is required" }, { status: 400 });
    }
    
    // Check if user has permission to update member roles in this workspace
    const permission = await checkWorkspaceAdminPermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Update the member's role
    const { data, error } = await supabase
      .from("workspace_members")
      .update({ role })
      .eq("id", id)
      .eq("workspace_id", workspaceId)
      .select();
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: "Member role updated successfully",
      data 
    }, { status: 200 });
  } catch (error) {
    console.error("Error updating member role:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
