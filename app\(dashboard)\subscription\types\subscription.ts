export interface SubscriptionPageState {
  loading: boolean;
  selectedPlan: PlanTier;
  selectedGateway: PaymentGateway;
  currentSubscription: UserSubscription | null;
  user: any | null;
  billingCycle: 'monthly' | 'yearly';
  isDataLoading: boolean;
  daysRemaining: number | null;
}

export interface SubscriptionPageProps {
  isCollapsed: boolean;
}

export interface PaymentMethodCardProps {
  gateway: PaymentGateway;
  selectedGateway: PaymentGateway;
  onSelect: (gateway: PaymentGateway) => void;
}

export interface BillingCycleToggleProps {
  billingCycle: 'monthly' | 'yearly';
  onToggle: (cycle: 'monthly' | 'yearly') => void;
}

export interface CurrentSubscriptionProps {
  subscription: UserSubscription;
  daysRemaining: number | null;
  isDataLoading: boolean;
  onManage: () => void;
}

export interface SubscribeButtonProps {
  selectedPlan: PlanTier;
  loading: boolean;
  onSubscribe: () => void;
  isDisabled: boolean;
  buttonText: string;
}

export interface FeatureComparisonProps {
  billingCycle: 'monthly' | 'yearly';
  getPrice: (basePrice: number) => string;
}

// Re-export from main types
export type { PlanTier, UserSubscription, SubscriptionPlan } from '@/lib/types/subscription';
export type { PaymentGateway } from '@/lib/services/payment';
export { SUBSCRIPTION_PLANS } from '@/lib/types/subscription';
