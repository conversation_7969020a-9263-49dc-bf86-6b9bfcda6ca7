"use client";

import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Loader2 } from "lucide-react";
import { DeleteMemberDialogProps } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';
import { getDeleteConfirmationMessage } from '../utils/member';

export function DeleteMemberDialog({
  member,
  isOpen,
  onClose,
  onConfirm,
  isDeleting,
}: DeleteMemberDialogProps) {
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent className="sm:max-w-[425px]">
        <AlertDialogHeader>
          <AlertDialogTitle>{MEMBER_CONSTANTS.UI_TEXT.DELETE_TITLE}</AlertDialogTitle>
          <AlertDialogDescription>
            {member ? getDeleteConfirmationMessage(member) : ''}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="sm:flex-row sm:justify-end gap-2">
          <AlertDialogCancel className="sm:w-auto" disabled={isDeleting}>
            {MEMBER_CONSTANTS.UI_TEXT.CANCEL_BUTTON}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90 sm:w-auto"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {MEMBER_CONSTANTS.UI_TEXT.DELETING_BUTTON}
              </>
            ) : (
              MEMBER_CONSTANTS.UI_TEXT.DELETE_BUTTON
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
