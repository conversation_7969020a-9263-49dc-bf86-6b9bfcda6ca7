import { workspace<PERSON><PERSON> } from "../base/workspace";

interface WorkspaceRequest {
  id?: string; // Optional for create/update, required for delete
  name: string;
  description?: string;
  status: boolean;
}

interface WorkspaceResponse {
  [key: string]: any;
}

export const workspaceApis = workspaceApi.injectEndpoints({
  endpoints: (builder) => ({
    // Create a new workspace
    createWorkspace: builder.mutation<WorkspaceRequest, WorkspaceResponse>({
      query: (data) => ({
        url: "/create",
        method: "POST",
        body: data,
      }),
      invalidatesTags: [
        { type: 'Workspace' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'LIST' }
      ],
    }),

    // Update an existing workspace
    updateWorkspace: builder.mutation<WorkspaceRequest, WorkspaceResponse>({
      query: (data) => ({
        url: `/${data.id}`,
        method: "PUT",
        body: { data },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Workspace' as const, id: arg.id ? arg.id : 'LIST' },
        { type: 'Workspace' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'LIST' },
        ...(arg.status !== undefined ? [{ type: 'ActiveWorkspace' as const, id: 'CURRENT' }] : [])
      ],
    }),

    // Delete an existing workspace
    deleteWorkspace: builder.mutation<{ id: string }, { id: string }>({
      query: ({ id }) => ({
        url: `/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Workspace' as const, id: arg.id },
        { type: 'Workspace' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'LIST' }
      ],
    }),

    // Fetch all workspaces
    getWorkspaces: builder.query<any, void>({
      query: () => ({
        url: "/list",
        method: "GET",
      }),
      providesTags: () => [
        { type: 'Workspace' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'LIST' }
      ],
    }),

    // Fetch workspace by ID
    getWorkspacesById: builder.query<any, string>({
      query: (id) => ({
        url: `/${id}`,
        method: "GET",
      }),
      providesTags: (result, error, id) => [
        { type: 'Workspace' as const, id }
      ],
    }),

    // Fetch workspaces by owner ID
    getWorkspacesByOwnerId: builder.query<any, { ownerId: string }>({
      query: ({ ownerId }) => ({
        url: `/list?ownerId=${ownerId}`,
        method: "GET",
      }),
      providesTags: () => [
        { type: 'Workspace' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'LIST' }
      ],
    }),

    // Get active workspace
    getActiveWorkspace: builder.query<any, void>({
      query: () => ({
        url: `/active`,
        method: "GET",
      }),
      providesTags: () => [
        { type: 'ActiveWorkspace' as const, id: 'CURRENT' }
      ],
    }),

    // Get revenue by workspace
    getRevenueByWorkspace: builder.query<any, string | undefined>({
      query: (id) => id ? ({
        url: `/revenue/${id}`,
        method: "GET",
      }) : undefined,
      providesTags: (result, error, id) => [
        { type: 'Workspace' as const, id: id || 'NONE' }
      ],
      // Add default response for empty data
      transformResponse: (response: any) => {
        console.log("Revenue API Response:", response);

        if (!response) {
          return { totalRevenue: 0, change: "+0%" };
        }

        // Ensure totalRevenue is a number
        if (response.totalRevenue !== undefined) {
          const totalRevenue = typeof response.totalRevenue === 'number' ?
            response.totalRevenue :
            parseFloat(response.totalRevenue || '0');

          return {
            ...response,
            totalRevenue
          };
        }

        return response;
      },
      // Add error handling
      transformErrorResponse: (response: { status: number; data: any }) => {
        console.error("Error fetching workspace revenue:", response);
        return { totalRevenue: 0, change: "+0%" };
      },
      // Skip if no ID provided
      skip: (id) => !id,
    }),

    // Get qualified leads count
    getQualifiedCount: builder.query<any, string | undefined>({
      query: (id) => id ? ({
        url: `/leads/qualified/${id}`,
        method: "GET",
      }) : undefined,
      providesTags: (result, error, id) => [
        { type: 'Workspace' as const, id: id || 'NONE' }
      ],
      // Add default response for empty data
      transformResponse: (response: any) => {
        if (!response) {
          return { qualifiedLeadsCount: 0, qualifiedStatuses: [] };
        }
        return response;
      },
      // Add error handling
      transformErrorResponse: (response: { status: number; data: any }) => {
        console.error("Error fetching qualified count:", response);
        return { qualifiedLeadsCount: 0, qualifiedStatuses: [] };
      },
      // Skip if no ID provided
      skip: (id) => !id,
    }),

    // Get arrived leads count by workspace
    getCountByWorkspace: builder.query<any, string | undefined>({
      query: (id) => id ? ({
        url: `/leads/arrived/${id}`,
        method: "GET",
      }) : undefined,
      providesTags: (result, error, id) => [
        { type: 'Workspace' as const, id: id || 'NONE' }
      ],
      // Add default response for empty data
      transformResponse: (response: any) => {
        if (!response) {
          return { arrivedLeadsCount: 0 };
        }
        return response;
      },
      // Add error handling
      transformErrorResponse: (response: { status: number; data: any }) => {
        console.error("Error fetching leads count:", response);
        return { arrivedLeadsCount: 0 };
      },
      // Skip if no ID provided
      skip: (id) => !id,
    }),

    // Get ROC (Rate of Conversion) by workspace
    getROCByWorkspace: builder.query<any, string | undefined>({
      query: (id) => id ? ({
        url: `/leads/total/${id}`,
        method: "GET",
      }) : undefined,
      providesTags: (result, error, id) => [
        { type: 'Workspace' as const, id: id || 'NONE' }
      ],
      // Add default response for empty data
      transformResponse: (response: any) => {
        console.log("ROC API Response:", response);

        if (!response) {
          return {
            conversion_rate: 0,
            total_leads: 0,
            converted_leads: 0,
            monthly_stats: []
          };
        }

        // Create a new response object with default values
        const processedResponse = {
          conversion_rate: 0,
          total_leads: 0,
          converted_leads: 0,
          monthly_stats: [],
          ...response
        };

        // Ensure monthly_stats is always an array
        if (!processedResponse.monthly_stats || !Array.isArray(processedResponse.monthly_stats)) {
          processedResponse.monthly_stats = [];
        }

        // Ensure conversion_rate is a number
        if (processedResponse.conversion_rate !== undefined) {
          processedResponse.conversion_rate = typeof processedResponse.conversion_rate === 'number' ?
            processedResponse.conversion_rate :
            parseFloat(String(processedResponse.conversion_rate) || '0');
        }

        // Ensure total_leads is a number
        if (processedResponse.total_leads !== undefined) {
          processedResponse.total_leads = typeof processedResponse.total_leads === 'number' ?
            processedResponse.total_leads :
            parseInt(String(processedResponse.total_leads) || '0');
        }

        // Ensure converted_leads is a number
        if (processedResponse.converted_leads !== undefined) {
          processedResponse.converted_leads = typeof processedResponse.converted_leads === 'number' ?
            processedResponse.converted_leads :
            parseInt(String(processedResponse.converted_leads) || '0');
        }

        console.log("Processed ROC Response:", processedResponse);
        return processedResponse;
      },
      // Add error handling
      transformErrorResponse: (response: { status: number; data: any }) => {
        console.error("Error fetching ROC data:", response);
        return {
          conversion_rate: 0,
          total_leads: 0,
          converted_leads: 0,
          monthly_stats: []
        };
      },
      // Skip if no ID provided
      skip: (id) => !id,
    }),

    // Get workspace members
    getWorkspaceMembers: builder.query<any, string>({
      query: (workspaceId) => ({
        url: `/members/${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, workspaceId) => [
        { type: 'Workspace' as const, id: workspaceId }
      ],
    }),

    // Get qualified leads count
    getQualifiedCount: builder.query<any, string>({
      query: (workspaceId) => ({
        url: `/leads/qualified/${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, workspaceId) => [
        { type: 'Workspace' as const, id: workspaceId }
      ],
    }),

    // Get workspace analytics
    getWorkspaceDetailsAnalytics: builder.query<any, string>({
      query: (workspaceId) => ({
        url: `/analytics/${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, workspaceId) => [
        { type: 'Workspace' as const, id: workspaceId }
      ],
    }),

    // Update workspace status
    updateWorkspaceStatus: builder.mutation<
      { id: string; status: boolean },
      { id: string; status: boolean }
    >({
      query: ({ id, status }) => ({
        url: `/${id}/status`,
        method: "PUT",
        body: { status },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Workspace' as const, id: arg.id },
        { type: 'Workspace' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'LIST' },
        { type: 'ActiveWorkspace' as const, id: 'CURRENT' }
      ],
    }),

    // New optimized workspace switching
    switchWorkspace: builder.mutation<
      { message: string; data: { id: string; name: string; role: string; isOwner: boolean } },
      { workspaceId: string }
    >({
      query: ({ workspaceId }) => ({
        url: "/switch",
        method: "POST",
        body: { workspaceId },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'ActiveWorkspace' as const, id: 'CURRENT' },
        { type: 'Workspace' as const, id: arg.workspaceId },
        { type: 'WorkspaceList' as const, id: 'LIST' },
        { type: 'WorkspaceList' as const, id: 'USER_WORKSPACES' },
        'MemberByWorkspace',
        'MemberList'
      ],
    }),

    // Get user's workspace memberships
    getUserWorkspaces: builder.query<
      { workspaces: any[]; activeWorkspace: any; totalCount: number },
      void
    >({
      query: () => ({
        url: "/switch",
        method: "GET",
      }),
      providesTags: () => [
        { type: 'WorkspaceList' as const, id: 'USER_WORKSPACES' }
      ],
    }),
  }),
});

// Export hooks for the workspace mutations and queries
export const {
  useGetActiveWorkspaceQuery,
  useCreateWorkspaceMutation,
  useUpdateWorkspaceMutation,
  useDeleteWorkspaceMutation,
  useGetWorkspacesQuery,
  useGetWorkspacesByOwnerIdQuery,
  useUpdateWorkspaceStatusMutation,
  useGetWorkspacesByIdQuery,
  useGetRevenueByWorkspaceQuery,
  useGetCountByWorkspaceQuery,
  useGetROCByWorkspaceQuery,
  useGetWorkspaceMembersQuery,
  useGetQualifiedCountQuery,
  useGetWorkspaceDetailsAnalyticsQuery,
  useSwitchWorkspaceMutation,
  useGetUserWorkspacesQuery,
} = workspaceApis;
