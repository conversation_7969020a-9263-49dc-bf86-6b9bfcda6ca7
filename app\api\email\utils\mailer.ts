import nodemailer from "nodemailer";

/**
 * Create a nodemailer transporter with environment variables
 */
export const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || "587", 10),
    secure: process.env.SMTP_SECURE === "true",
    auth: {
      user: process.env.SMTP_USERNAME || process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

/**
 * Email options interface
 */
export interface EmailOptions {
  requestReadReceipt?: boolean;
  deliveryNotification?: boolean;
}

/**
 * Send an email using nodemailer
 * @param to Recipient email address
 * @param subject Email subject
 * @param html Email HTML content
 * @param options Additional email options
 * @returns Promise with the email info
 */
export const sendEmail = async (
  to: string, 
  subject: string, 
  html: string, 
  options: EmailOptions = {}
) => {
  try {
    const transporter = createTransporter();
    
    const info = await transporter.sendMail({
      from: `"PRE CRM" <${process.env.SMTP_USER}>`,
      to,
      subject,
      html,
      headers: {
        ...(options.requestReadReceipt ? {
          'Disposition-Notification-To': process.env.SMTP_USER || '',
          'X-Confirm-Reading-To': process.env.SMTP_USER || '',
          'Return-Receipt-To': process.env.SMTP_USER || '',
          'Read-Receipt-To': process.env.SMTP_USER || '',
        } : {}),
        ...(options.deliveryNotification ? {
          'X-DSN-Notify': 'SUCCESS,FAILURE,DELAY',
          'Delivery-Status-Notification-Options': 'SUCCESS,FAILURE,DELAY',
        } : {})
      },
    });
    
    console.log("Message sent: %s", info.messageId);
    return info;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
};
