"use client";
import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { toast } from "sonner";

// This tells Next.js to render this page dynamically at request time
export const dynamic = 'force-dynamic';

// Create a loading component
function InvitePageLoading() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Processing Invitation</CardTitle>
          <CardDescription>Please wait while we process your invitation...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    </div>
  );
}

// Main component wrapped in Suspense
function AcceptInviteContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [invitationData, setInvitationData] = useState<any>(null);

  // Extract parameters from URL
  const email = searchParams?.get("email") || "";
  const workspaceId = searchParams?.get("workspaceId") || "";
  const status = searchParams?.get("status") || "";

  useEffect(() => {
    const loadInvitationData = async () => {
      try {
        const token = searchParams?.get("token");
        const email = searchParams?.get("email");
        const workspaceId = searchParams?.get("workspaceId");

        if (token) {
          // New secure token-based invitation
          const response = await fetch(`/api/members/accept-invitation?token=${token}`);

          if (!response.ok) {
            const data = await response.json();
            throw new Error(data.error || "Invalid invitation");
          }

          const result = await response.json();
          setInvitationData(result.data);
        } else if (email && workspaceId) {
          // Legacy invitation format - create mock data for display
          setInvitationData({
            email,
            workspaceId,
            workspaceName: "Workspace",
            role: "member"
          });
        } else {
          throw new Error("Invalid invitation link. Missing required parameters.");
        }

        setLoading(false);
      } catch (err: any) {
        setError(err.message || "Failed to load invitation details");
        setLoading(false);
      }
    };

    loadInvitationData();
  }, [searchParams]);

  const handleAcceptInvite = async () => {
    setLoading(true);
    try {
      const token = searchParams?.get("token");
      const email = searchParams?.get("email");
      const workspaceId = searchParams?.get("workspaceId");
      const status = searchParams?.get("status");

      if (token) {
        // New secure token-based invitation
        const response = await fetch("/api/members/accept-invitation", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ token }),
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || "Failed to accept invitation");
        }

        const result = await response.json();
        setSuccess(true);
        toast.success(`Welcome to ${result.data.workspaceName}!`);
      } else if (email && workspaceId && status) {
        // Legacy invitation format
        const response = await fetch(`/api/auth?action=acceptInvite&email=${email}&workspaceId=${workspaceId}&status=${status}`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || "Failed to accept invitation");
        }

        setSuccess(true);
        toast.success("Invitation accepted successfully!");
      } else {
        throw new Error("Invalid invitation link");
      }

      // Redirect to dashboard after a short delay
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    } catch (err: any) {
      setError(err.message || "An error occurred while accepting the invitation.");
      toast.error("Failed to accept invitation");
    } finally {
      setLoading(false);
    }
  };

  const handleDeclineInvite = () => {
    // Just redirect to the login page
    router.push("/login");
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>{success ? "Welcome!" : "Workspace Invitation"}</CardTitle>
          <CardDescription>
            {success
              ? `You have successfully joined ${invitationData?.workspaceName || "the workspace"}.`
              : invitationData
              ? `You've been invited to join ${invitationData.workspaceName} as a ${invitationData.role}.`
              : "Loading invitation details..."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="flex flex-col items-center gap-2 py-4 text-center">
              <XCircle className="h-8 w-8 text-destructive" />
              <p className="text-sm text-muted-foreground">{error}</p>
            </div>
          ) : success ? (
            <div className="flex flex-col items-center gap-2 py-4 text-center">
              <CheckCircle className="h-8 w-8 text-primary" />
              <p className="text-sm text-muted-foreground">Redirecting you to the dashboard...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm">
                <strong>Email:</strong> {email}
              </p>
              {/* Don't show workspace ID to user, it's internal */}
            </div>
          )}
        </CardContent>
        {!loading && !error && !success && (
          <CardFooter className="flex justify-between">
            <Button variant="outline" onClick={handleDeclineInvite}>
              Decline
            </Button>
            <Button onClick={handleAcceptInvite}>Accept Invitation</Button>
          </CardFooter>
        )}
      </Card>
    </div>
  );
}

// Export the page component with Suspense
export default function AcceptInvitePage() {
  return (
    <Suspense fallback={<InvitePageLoading />}>
      <AcceptInviteContent />
    </Suspense>
  );
}
