import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Users, Plus } from "lucide-react";
import { EMPTY_STATE_MESSAGES } from "../constants/leadSources";

interface EmptyStateProps {
  onCreateSource: () => void;
  hasWorkspace: boolean;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  onCreateSource,
  hasWorkspace,
}) => {
  if (!hasWorkspace) {
    return (
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        <CardContent className="flex flex-col items-center justify-center py-16 px-6">
          <div className="text-gray-400 dark:text-gray-600 mb-6">
            <Users className="h-16 w-16 mx-auto" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 text-center">
            {EMPTY_STATE_MESSAGES.NO_WORKSPACE.title}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
            {EMPTY_STATE_MESSAGES.NO_WORKSPACE.description}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
      <CardContent className="flex flex-col items-center justify-center py-16 px-6">
        <div className="text-gray-400 dark:text-gray-600 mb-6">
          <Users className="h-16 w-16 mx-auto" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 text-center">
          {EMPTY_STATE_MESSAGES.NO_SOURCES.title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-6">
          {EMPTY_STATE_MESSAGES.NO_SOURCES.description}
        </p>
        <Button
          onClick={onCreateSource}
          className="bg-black hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-200 text-white dark:text-black"
        >
          <Plus className="mr-2 h-4 w-4" />
          {EMPTY_STATE_MESSAGES.NO_SOURCES.action}
        </Button>
      </CardContent>
    </Card>
  );
};
