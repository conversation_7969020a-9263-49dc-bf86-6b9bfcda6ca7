import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search } from 'lucide-react';
import { CONTACT_CONSTANTS } from '../constants/contact';

interface ContactFiltersProps {
  search: string;
  statusFilter: string;
  contactStatuses: Set<string>;
  onSearchChange: (search: string) => void;
  onStatusFilterChange: (status: string) => void;
}

export function ContactFilters({
  search,
  statusFilter,
  contactStatuses,
  onSearchChange,
  onStatusFilterChange,
}: ContactFiltersProps) {
  return (
    <div className="flex flex-col md:flex-row gap-4 items-center mb-6">
      {/* Search Input */}
      <div className="md:flex-1 relative w-full">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder={CONTACT_CONSTANTS.MESSAGES.SEARCH_PLACEHOLDER}
          value={search}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-9 bg-background border-border"
        />
      </div>

      {/* Status Filter Dropdown */}
      <Select value={statusFilter} onValueChange={onStatusFilterChange}>
        <SelectTrigger className="md:w-[180px] bg-background border-border">
          <SelectValue placeholder={CONTACT_CONSTANTS.MESSAGES.FILTER_PLACEHOLDER} />
        </SelectTrigger>
        <SelectContent>
          {/* Default "All Status" option */}
          <SelectItem value={CONTACT_CONSTANTS.STATUS_OPTIONS.ALL}>
            All Status
          </SelectItem>

          {/* Convert Set to Array and map over it */}
          {Array.from(contactStatuses).map((statusName) => (
            <SelectItem
              key={statusName as string}
              value={statusName as string}
            >
              {statusName as string}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
