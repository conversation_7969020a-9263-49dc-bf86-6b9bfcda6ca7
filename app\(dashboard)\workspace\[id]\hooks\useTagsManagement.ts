import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  useAddTagsMutation,
  useDeleteTagsMutation,
  useUpdateTagsMutation,
} from '@/lib/store/services/tags';
import { Tags, NewTags } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';
import { validateTags, formatTagsForAPI, handleAPIError } from '../utils/workspace';

interface UseTagsManagementProps {
  workspaceId: string;
  tagsData?: any;
}

export function useTagsManagement({ workspaceId, tagsData }: UseTagsManagementProps) {
  const [addTags, { isLoading: isAddingTag }] = useAddTagsMutation();
  const [updateTags, { isLoading: isUpdatingTags }] = useUpdateTagsMutation();
  const [deleteTags, { isLoading: isDeletingTags }] = useDeleteTagsMutation();

  // State management
  const [tags, setTags] = useState<Tags[]>([]);
  const [newTags, setNewTags] = useState<NewTags>(WORKSPACE_CONSTANTS.DEFAULTS.NEW_TAGS);
  const [isAddingTags, setIsAddingTags] = useState(false);
  const [tagsToEdit, setTagsToEdit] = useState<Tags | null>(null);
  const [tagsToDelete, setTagsToDelete] = useState<Tags | null>(null);

  // Initialize tags from API data
  useEffect(() => {
    if (tagsData?.data) {
      setTags(tagsData.data);
    }
  }, [tagsData]);

  // Add new tags
  const handleAddTags = async () => {
    // Validate tags
    const validation = validateTags(newTags);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      const formattedTags = formatTagsForAPI(newTags);
      const result = await addTags({
        tagsData: formattedTags,
        workspaceId,
      }).unwrap();

      // Update local state
      setTags((prevTags) => [
        ...prevTags,
        {
          id: result.id || '',
          name: newTags.name,
          color: newTags.color,
        },
      ]);

      // Reset form
      setNewTags(WORKSPACE_CONSTANTS.DEFAULTS.NEW_TAGS);
      setIsAddingTags(false);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.TAGS_ADDED);

      // Refresh page to ensure data consistency
      window.location.reload();
    } catch (error: any) {
      const errorMessage = handleAPIError(error, WORKSPACE_CONSTANTS.MESSAGES.TAGS_ADD_ERROR);
      toast.error(errorMessage);
    }
  };

  // Edit tags
  const handleEditTags = (tags: Tags) => {
    setTagsToEdit({ ...tags });
  };

  // Update tags
  const handleUpdateTags = async () => {
    if (!tagsToEdit) return;

    // Validate tags
    const validation = validateTags(tagsToEdit);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      await updateTags({
        id: tagsToEdit.id,
        updatedTags: tagsToEdit,
      }).unwrap();

      // Update local state
      setTags((prevTags) =>
        prevTags.map((tag) =>
          tag.id === tagsToEdit.id ? tagsToEdit : tag
        )
      );

      setTagsToEdit(null);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.TAGS_UPDATED);
    } catch (error: any) {
      const errorMessage = handleAPIError(error, WORKSPACE_CONSTANTS.MESSAGES.TAGS_UPDATE_ERROR);
      toast.error(errorMessage);
    }
  };

  // Delete tags
  const handleDeleteTags = (tags: Tags) => {
    setTagsToDelete(tags);
  };

  const confirmDeleteTags = async () => {
    if (!tagsToDelete) return;

    try {
      await deleteTags({
        id: tagsToDelete.id,
        workspace_id: workspaceId,
      }).unwrap();

      // Update local state
      setTags((prevTags) =>
        prevTags.filter((tag) => tag.id !== tagsToDelete.id)
      );

      setTagsToDelete(null);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.TAGS_DELETED);
    } catch (error: any) {
      const errorMessage = handleAPIError(error, WORKSPACE_CONSTANTS.MESSAGES.TAGS_DELETE_ERROR);
      toast.error(errorMessage);
    }
  };

  const cancelDeleteTags = () => {
    setTagsToDelete(null);
  };

  return {
    // State
    tags,
    newTags,
    setNewTags,
    isAddingTags,
    setIsAddingTags,
    tagsToEdit,
    setTagsToEdit,
    tagsToDelete,
    setTagsToDelete,

    // Loading states
    isAddingTag,
    isUpdatingTags,
    isDeletingTags,

    // Actions
    handleAddTags,
    handleEditTags,
    handleUpdateTags,
    handleDeleteTags,
    confirmDeleteTags,
    cancelDeleteTags,
  };
}
