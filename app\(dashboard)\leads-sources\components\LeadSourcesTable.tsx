import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pencil,
  Trash2,
  Copy,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { LeadSource } from "../types/leadSources";
import { TABLE_HEADERS } from "../constants/leadSources";
import { truncateText, getSourceTypeLabel } from "../utils/leadSourceUtils";

interface LeadSourcesTableProps {
  sources: LeadSource[];
  expandedRow: string | null;
  onToggleRow: (sourceId: string) => void;
  onEditSource: (source: LeadSource) => void;
  onDeleteSource: (source: LeadSource) => void;
  onToggleStatus: (sourceId: string) => void;
  onCopyWebhook: (webhookUrl: string) => void;
  isStatusChanging: boolean;
}

export const LeadSourcesTable: React.FC<LeadSourcesTableProps> = ({
  sources,
  expandedRow,
  onToggleRow,
  onEditSource,
  onDeleteSource,
  onToggleStatus,
  onCopyWebhook,
  isStatusChanging,
}) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        {/* Desktop Table Header */}
        <TableHeader>
          <TableRow className="bg-white dark:bg-black">
            {TABLE_HEADERS.DESKTOP.map((header) => (
              <TableHead
                key={header}
                className="hidden md:table-cell text-black dark:text-white"
              >
                {header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>

        <TableBody className="w-auto">
          {sources.map((source) => (
            <React.Fragment key={source.id}>
              {/* Mobile View */}
              <TableRow className="flex md:hidden lg:hidden items-center justify-between border-b border-gray-300 dark:border-gray-700 p-2 last:border-none">
                <div className="flex flex-col gap-0">
                  <div className="text-[1rem] font-medium text-gray-900 dark:text-white">
                    {source.name}
                  </div>
                  <div className="text-gray-500 dark:text-gray-400">
                    {getSourceTypeLabel(source.type)}
                  </div>
                </div>
                <TableCell>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => onToggleRow(source.id)}
                    className="h-8 w-8 border-none bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700"
                  >
                    {expandedRow === source.id ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </TableCell>
              </TableRow>

              {/* Expanded Mobile Details */}
              {expandedRow === source.id && (
                <TableRow className="md:hidden lg:hidden">
                  <TableCell colSpan={3}>
                    <div className="space-y-3 p-2">
                      {/* Description */}
                      {source.description && (
                        <div className="flex items-start gap-4">
                          <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                            Description:
                          </span>
                          <span className="text-gray-900 dark:text-white">
                            {source.description}
                          </span>
                        </div>
                      )}

                      {/* Count */}
                      <div className="flex items-center gap-4">
                        <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                          Count:
                        </span>
                        <span className="text-gray-900 dark:text-white">0</span>
                      </div>

                      {/* Processing Rate */}
                      <div className="flex items-center gap-4">
                        <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                          Processing:
                        </span>
                        <span className="text-gray-900 dark:text-white">N/A</span>
                      </div>

                      {/* Qualification Rate */}
                      <div className="flex items-center gap-4">
                        <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                          Qualification:
                        </span>
                        <span className="text-gray-900 dark:text-white">N/A</span>
                      </div>

                      {/* Webhook */}
                      <div className="flex items-center gap-4">
                        <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                          Webhook:
                        </span>
                        <div className="flex items-center space-x-2 flex-1">
                          <span className="truncate max-w-[150px] text-gray-900 dark:text-white text-sm">
                            {truncateText(source.webhook_url || "", 30)}
                          </span>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => onCopyWebhook(source.webhook_url || "")}
                            className="h-8 w-8 hover:bg-gray-100 dark:hover:bg-gray-800"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Status */}
                      <div className="flex items-center gap-4">
                        <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                          Status:
                        </span>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={source.status}
                            onCheckedChange={() => onToggleStatus(source.id)}
                            disabled={isStatusChanging}
                          />
                          <span
                            className={`text-sm ${
                              source.status
                                ? "text-green-600 dark:text-green-400"
                                : "text-red-600 dark:text-red-400"
                            }`}
                          >
                            {source.status ? "Enabled" : "Disabled"}
                          </span>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-4 pt-2">
                        <span className="text-gray-500 dark:text-gray-400 min-w-[80px]">
                          Actions:
                        </span>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => onEditSource(source)}
                            className="h-8 w-8 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="icon"
                            onClick={() => onDeleteSource(source)}
                            className="h-8 w-8"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}

              {/* Desktop View */}
              <TableRow className="hidden md:table-row hover:bg-gray-50 dark:hover:bg-gray-900/50">
                <TableCell className="font-medium text-gray-900 dark:text-white">
                  {source.name}
                </TableCell>
                <TableCell className="text-gray-700 dark:text-gray-300">
                  {getSourceTypeLabel(source.type)}
                </TableCell>
                <TableCell className="text-gray-700 dark:text-gray-300">
                  {source.description || "-"}
                </TableCell>
                <TableCell className="text-gray-700 dark:text-gray-300">0</TableCell>
                <TableCell className="text-gray-700 dark:text-gray-300">N/A</TableCell>
                <TableCell className="text-gray-700 dark:text-gray-300">N/A</TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <span className="truncate max-w-xs text-gray-700 dark:text-gray-300 text-sm">
                      {truncateText(source.webhook_url || "", 40)}
                    </span>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onCopyWebhook(source.webhook_url || "")}
                      className="h-8 w-8 hover:bg-gray-100 dark:hover:bg-gray-800"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={source.status}
                      onCheckedChange={() => onToggleStatus(source.id)}
                      disabled={isStatusChanging}
                    />
                    <span
                      className={`text-sm ${
                        source.status
                          ? "text-green-600 dark:text-green-400"
                          : "text-red-600 dark:text-red-400"
                      }`}
                    >
                      {source.status ? "Enabled" : "Disabled"}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => onEditSource(source)}
                      className="h-8 w-8 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="icon"
                      onClick={() => onDeleteSource(source)}
                      className="h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
