-- Migration for table: subscriptions (generated from API spec only)
CREATE TABLE IF NOT EXISTS "subscriptions" (
  "id" uuid PRIMARY KEY NOT NULL,
  "user_id" uuid NOT NULL,
  "plan_id" text NOT NULL,
  "status" text NOT NULL,
  "current_period_start" text,
  "current_period_end" text,
  "cancel_at_period_end" boolean,
  "payment_method" text,
  "payment_id" text,
  "created_at" text,
  "updated_at" text
);

-- Enable Row Level Security
ALTER TABLE "subscriptions" ENABLE ROW LEVEL SECURITY;

