import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Legend, Tooltip } from "recharts";
import { SalesData } from "../types/dashboard";
import { CHART_CONFIG, EMPTY_STATE_MESSAGES } from "../constants/dashboard";

interface SalesChartProps {
  data: SalesData[];
  hasData: boolean;
}

export const SalesChart: React.FC<SalesChartProps> = ({ data, hasData }) => {
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg">
          <p className="text-sm font-medium text-gray-900 dark:text-white">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'sales' ? 'Sales' : 'Leads'}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  if (!hasData) {
    return (
      <Card className="lg:col-span-2 overflow-hidden border border-gray-200 dark:border-gray-800 rounded-lg">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
            Sales Overview
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            Monthly sales and leads performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-[280px] text-center">
            <div className="text-gray-400 dark:text-gray-600 mb-4">
              <BarChart className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {EMPTY_STATE_MESSAGES.NO_CHART_DATA.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-sm">
              {EMPTY_STATE_MESSAGES.NO_CHART_DATA.description}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="lg:col-span-2 overflow-hidden border border-gray-200 dark:border-gray-800 rounded-lg">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
              Sales Overview
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-400">
              Monthly sales and leads performance
            </CardDescription>
          </div>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span className="text-gray-600 dark:text-gray-400">Sales</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-600 dark:text-gray-400">Converted</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="w-full h-[280px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={data}
              margin={CHART_CONFIG.CHART_MARGINS}
              barGap={CHART_CONFIG.BAR_GAP}
            >
              <CartesianGrid 
                strokeDasharray="3 3" 
                className="stroke-gray-200 dark:stroke-gray-700" 
              />
              <XAxis 
                dataKey="month" 
                className="text-xs fill-gray-600 dark:fill-gray-400"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs fill-gray-600 dark:fill-gray-400"
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                wrapperStyle={{ 
                  fontSize: '12px',
                  color: 'rgb(107 114 128)'
                }}
              />
              <Bar 
                dataKey="leads" 
                fill="#3b82f6" 
                name="Total Leads"
                radius={[2, 2, 0, 0]}
                maxBarSize={CHART_CONFIG.MAX_BAR_SIZE}
              />
              <Bar 
                dataKey="sales" 
                fill="#10b981" 
                name="Converted Leads"
                radius={[2, 2, 0, 0]}
                maxBarSize={CHART_CONFIG.MAX_BAR_SIZE}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
