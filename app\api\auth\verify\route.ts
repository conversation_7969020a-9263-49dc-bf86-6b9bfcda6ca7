import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Verify a user's email
 * @route POST /api/auth/verify
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { token } = body;
    
    if (!token) {
      return NextResponse.json({ 
        error: "Verification token is required" 
      }, { status: 400 });
    }
    
    // Verify the token with Supabase
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'email',
    });
    
    if (error) {
      return NextResponse.json({ 
        error: error.message || AUTH_MESSAGES.VERIFICATION_FAILED 
      }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Email verification successful",
      user: data.user,
      session: data.session,
    }, { status: 200 });
  } catch (error) {
    console.error("Error verifying email:", error);
    return NextResponse.json({ 
      error: AUTH_MESSAGES.VERIFICATION_FAILED 
    }, { status: 500 });
  }
}
