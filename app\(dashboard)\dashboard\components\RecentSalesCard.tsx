import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCircle, Users, DollarSign } from "lucide-react";
import { RecentSale, TeamPerformance } from "../types/dashboard";
import { EMPTY_STATE_MESSAGES, TOOLTIP_MESSAGES } from "../constants/dashboard";
import { generateInitials, formatNumber } from "../utils/dashboardUtils";

interface RecentSalesCardProps {
  recentSales: RecentSale[];
  teamPerformance: TeamPerformance[];
  hasRecentSales: boolean;
  hasTeamPerformance: boolean;
}

export const RecentSalesCard: React.FC<RecentSalesCardProps> = ({
  recentSales,
  teamPerformance,
  hasRecentSales,
  hasTeamPerformance,
}) => {
  if (!hasRecentSales) {
    return (
      <Card className="border border-gray-200 dark:border-gray-800 rounded-lg h-[500px] lg:h-[calc(100vh-13rem)] max-h-[700px] flex flex-col">
        <CardHeader className="pb-4">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Sales
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {recentSales.length}
            </Badge>
          </div>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            Recent transactions and team performance
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-sm">
            <div className="text-gray-400 dark:text-gray-600 mb-4">
              <DollarSign className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {EMPTY_STATE_MESSAGES.NO_SALES.title}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {EMPTY_STATE_MESSAGES.NO_SALES.description}
            </p>
            <p className="text-sm text-blue-600 dark:text-blue-400 font-medium">
              {EMPTY_STATE_MESSAGES.NO_SALES.tip}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border border-gray-200 dark:border-gray-800 rounded-lg h-[500px] lg:h-[calc(100vh-13rem)] max-h-[700px] flex flex-col">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Sales
          </CardTitle>
          <Badge variant="secondary" className="text-xs">
            {recentSales.length}
          </Badge>
        </div>
        <CardDescription className="text-gray-600 dark:text-gray-400">
          Recent transactions and team performance
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden">
        {/* Team Performance Section */}
        {hasTeamPerformance && (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center space-x-2">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Team Performance
                </h4>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-3 w-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs max-w-xs">{TOOLTIP_MESSAGES.TEAM_PERFORMANCE}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="space-y-2">
              {teamPerformance.slice(0, 3).map((member, index) => (
                <div key={member.name} className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <div className="flex items-center">
                    <Avatar className="h-7 w-7 mr-2">
                      <AvatarFallback className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400">
                        {generateInitials(member.name)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {member.name}
                    </span>
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <span className="text-sm font-semibold text-green-600 dark:text-green-400">
                      ${formatNumber(member.totalRevenue)}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {member.totalSales} sale{member.totalSales !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>
              ))}
            </div>
            <hr className="my-4 border-gray-200 dark:border-gray-700" />
          </div>
        )}

        {/* Recent Transactions Section */}
        <div className="flex items-center space-x-2 mb-3">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            Recent Transactions
          </h4>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-3 w-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs max-w-xs">{TOOLTIP_MESSAGES.RECENT_TRANSACTIONS}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <div className="space-y-3 overflow-y-auto flex-1">
          {recentSales.map((sale) => (
            <div key={`${sale.id}-${sale.email}`} className="flex items-center p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
              <Avatar className="h-9 w-9 mr-3">
                <AvatarFallback className="bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 text-sm font-medium">
                  {sale.id}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex justify-between items-baseline">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {sale.name}
                    </p>
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <span className="truncate">{sale.email}</span>
                      <span className="mx-1.5">•</span>
                      <span>{sale.date}</span>
                    </div>
                  </div>
                  <div className="flex flex-col items-end ml-2">
                    <p className="text-sm font-semibold text-green-600 dark:text-green-400">
                      {sale.amount}
                    </p>
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <Users className="h-3 w-3 mr-1" />
                      <span className="truncate max-w-[80px]">{sale.assignedTo}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
