import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ChevronUp, ChevronDown, Send, Phone, Mail } from 'lucide-react';
import { Contact } from '../types/contact';
import { CONTACT_CONSTANTS } from '../constants/contact';
import WebhookStatus from '@/components/ui/WebhookStatus';

interface ContactTableRowProps {
  contact: Contact;
  selectedHeaders: string[];
  workspaceId: string;
  editState: any;
  nameInfo: string;
  emailInfo: string;
  phoneInfo: string;
  businessInfo: string;
  emailValidation: boolean;
  expandedRow: number | null;
  onEnterEditMode: (mode: string, contactId: number, currentValue?: any) => void;
  onUpdate: (contactId: number, updateData: any) => void;
  onKeyDown: (e: React.KeyboardEvent, mode: string, contactId: number, updateData: any) => void;
  onToggleRow: (contactId: number) => void;
  onSetNameInfo: (value: string) => void;
  onSetEmailInfo: (value: string) => void;
  onSetPhoneInfo: (value: string) => void;
  onSetBusinessInfo: (value: string) => void;
  onSetEmailValidation: (value: boolean) => void;
  onSendEmail: (contact: Contact) => void;
  onOpenInGmail: (contact: Contact) => void;
  onInitiateContact: (contact: Contact, method: string) => void;
}

export function ContactTableRow({
  contact,
  selectedHeaders,
  workspaceId,
  editState,
  nameInfo,
  emailInfo,
  phoneInfo,
  businessInfo,
  emailValidation,
  expandedRow,
  onEnterEditMode,
  onUpdate,
  onKeyDown,
  onToggleRow,
  onSetNameInfo,
  onSetEmailInfo,
  onSetPhoneInfo,
  onSetBusinessInfo,
  onSetEmailValidation,
  onSendEmail,
  onOpenInGmail,
  onInitiateContact,
}: ContactTableRowProps) {
  
  const renderEditableField = (
    mode: string,
    currentValue: string,
    placeholder: string,
    value: string,
    onChange: (value: string) => void,
    type: string = 'text'
  ) => {
    const isEditing = editState[`edit${mode}Id`] === contact.id;
    
    if (isEditing) {
      return (
        <input
          type={type}
          placeholder={placeholder}
          className="px-2 py-1 border rounded-md w-full bg-background text-foreground"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              const updateKey = mode.toLowerCase();
              onUpdate(contact.id, { [updateKey]: value });
              onEnterEditMode(`edit${mode}Id`, null);
            } else if (e.key === 'Escape') {
              onEnterEditMode(`edit${mode}Id`, null);
              onChange(currentValue);
            }
          }}
          autoFocus
        />
      );
    }

    return (
      <span
        className="cursor-pointer hover:underline text-foreground"
        onDoubleClick={() => {
          onEnterEditMode(`edit${mode}Id`, contact.id);
          onChange(currentValue);
        }}
      >
        {currentValue || (
          <span className="text-muted-foreground italic">
            {placeholder}
          </span>
        )}
      </span>
    );
  };

  const renderEmailValidation = () => {
    const isEditing = editState.editEmailValidationId === contact.id;
    
    if (isEditing) {
      return (
        <select
          className="px-2 py-1 border rounded-md bg-background text-foreground"
          value={emailValidation ? 'true' : 'false'}
          onChange={async (e) => {
            const newValue = e.target.value === 'true';
            onSetEmailValidation(newValue);
            await onUpdate(contact.id, { is_email_valid: newValue });
            onEnterEditMode('editEmailValidationId', null);
          }}
          autoFocus
        >
          <option value="true">True</option>
          <option value="false">False</option>
        </select>
      );
    }

    return (
      <span
        onDoubleClick={() => {
          onEnterEditMode('editEmailValidationId', contact.id);
          onSetEmailValidation(contact.is_email_valid);
        }}
        className={`px-2 py-1 text-sm font-semibold rounded cursor-pointer ${
          contact.is_email_valid
            ? 'bg-green-200 text-green-800 dark:bg-green-900 dark:text-green-100'
            : 'bg-red-200 text-red-800 dark:bg-red-900 dark:text-red-100'
        }`}
      >
        {contact.is_email_valid ? 'True' : 'False'}
      </span>
    );
  };

  const renderContactField = (value: string, type: 'email' | 'phone') => {
    if (!value) return null;

    const actions = type === 'email' 
      ? [
          { label: 'Send Email', icon: Send, action: () => onSendEmail(contact), color: 'text-blue-500' },
          { label: 'Open in Gmail', icon: Mail, action: () => onOpenInGmail(contact), color: 'text-red-500' }
        ]
      : [
          { label: 'WhatsApp', icon: Send, action: () => onInitiateContact(contact, 'WhatsApp'), color: 'text-green-500' },
          { label: 'Call', icon: Phone, action: () => onInitiateContact(contact, 'Call'), color: 'text-blue-500' }
        ];

    return (
      <div className="relative inline-block group">
        <span className="cursor-pointer group-hover:underline text-foreground">
          {value}
        </span>
        
        <div
          className="absolute left-1/2 -translate-x-1/2 hidden group-hover:flex flex-col bg-popover shadow-md rounded-md p-2 w-[140px] border border-border z-50"
          style={{
            bottom: 'calc(100% + 2px)',
            transform: 'translateX(-50%)',
            pointerEvents: 'auto',
          }}
        >
          {actions.map((action, index) => (
            <button
              key={action.label}
              onClick={action.action}
              className={`flex items-center gap-2 text-sm text-foreground hover:${action.color.replace('text-', 'text-')} w-full text-left ${index > 0 ? 'mt-1' : ''}`}
            >
              <action.icon className={`h-4 w-4 ${action.color}`} />
              {action.label}
            </button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Mobile View */}
      <TableRow className="md:hidden flex items-center p-4 justify-between gap-4 border-b hover:bg-muted/50 transition-colors">
        <div className="flex flex-col gap-2 flex-1">
          {selectedHeaders.includes('Name') && (
            <div className="font-medium text-foreground">
              {renderEditableField(
                'Name',
                contact.Name || '',
                CONTACT_CONSTANTS.MESSAGES.ADD_NAME_PLACEHOLDER,
                nameInfo,
                onSetNameInfo
              )}
            </div>
          )}
          
          {selectedHeaders.includes('Email') && (
            <div className="text-sm text-muted-foreground">
              {renderEditableField(
                'Email',
                contact.email || '',
                CONTACT_CONSTANTS.MESSAGES.ADD_EMAIL_PLACEHOLDER,
                emailInfo,
                onSetEmailInfo,
                'email'
              )}
            </div>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onToggleRow(contact.id)}
          className="h-8 w-8 rounded-full"
        >
          {expandedRow === contact.id ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </TableRow>

      {/* Mobile Expanded Content */}
      {expandedRow === contact.id && (
        <TableRow className="md:hidden">
          <div className="p-4 space-y-4 bg-muted/20 w-full">
            {selectedHeaders.includes('Phone') && (
              <div className="grid grid-cols-2 gap-2">
                <span className="text-muted-foreground font-medium">Phone</span>
                <div>
                  {renderEditableField(
                    'Phone',
                    contact.phone || '',
                    CONTACT_CONSTANTS.MESSAGES.ADD_PHONE_PLACEHOLDER,
                    phoneInfo,
                    onSetPhoneInfo
                  )}
                </div>
              </div>
            )}

            {selectedHeaders.includes('Email Validation') && (
              <div className="grid grid-cols-2 gap-2">
                <span className="text-muted-foreground font-medium">Email Validation</span>
                <div>{renderEmailValidation()}</div>
              </div>
            )}
          </div>
        </TableRow>
      )}

      {/* Desktop View */}
      <TableRow className="hidden md:table-row">
        {selectedHeaders.includes('Name') && (
          <TableCell className="font-medium text-center cursor-pointer">
            {renderEditableField(
              'Name',
              contact.Name || '',
              CONTACT_CONSTANTS.MESSAGES.ADD_NAME_PLACEHOLDER,
              nameInfo,
              onSetNameInfo
            )}
          </TableCell>
        )}

        {selectedHeaders.includes('Email') && (
          <TableCell className="text-center cursor-pointer">
            <div className="relative inline-block group">
              {renderEditableField(
                'Email',
                contact.email || '',
                CONTACT_CONSTANTS.MESSAGES.ADD_EMAIL_PLACEHOLDER,
                emailInfo,
                onSetEmailInfo,
                'email'
              )}
              {contact.email && renderContactField(contact.email, 'email')}
            </div>
          </TableCell>
        )}

        {selectedHeaders.includes('Phone') && (
          <TableCell className="text-center cursor-pointer">
            <div className="relative inline-block group">
              {renderEditableField(
                'Phone',
                contact.phone || '',
                CONTACT_CONSTANTS.MESSAGES.ADD_PHONE_PLACEHOLDER,
                phoneInfo,
                onSetPhoneInfo
              )}
              {contact.phone && renderContactField(contact.phone, 'phone')}
            </div>
          </TableCell>
        )}

        {selectedHeaders.includes('Email Validation') && (
          <TableCell className="text-center cursor-pointer">
            {renderEmailValidation()}
          </TableCell>
        )}

        {selectedHeaders.includes('Platform') && (
          <TableCell className="w-[170px] text-center">
            {contact.sourceId ? (
              <WebhookStatus sourceId={contact.sourceId} workspaceId={workspaceId} />
            ) : (
              <span className="text-muted-foreground">No Source</span>
            )}
          </TableCell>
        )}

        {selectedHeaders.includes('Bussiness Info') && (
          <TableCell className="text-center cursor-pointer">
            {renderEditableField(
              'BusinessInfo',
              contact.businessInfo || '',
              CONTACT_CONSTANTS.MESSAGES.ADD_INFO_PLACEHOLDER,
              businessInfo,
              onSetBusinessInfo
            )}
          </TableCell>
        )}
      </TableRow>
    </>
  );
}
