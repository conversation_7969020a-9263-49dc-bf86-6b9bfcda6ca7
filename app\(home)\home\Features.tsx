import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  Users, MessageSquare, Shield, Globe, Zap, LineChart,
  CheckCircle, ArrowRight, BellRing, BarChart3, Target,
  Sparkles, Layers, Workflow
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const features = [
  {
    icon: Users,
    color: "primary",
    title: "Team Collaboration",
    description: "Work together seamlessly with your team to manage leads effectively.",
    highlights: ["Real-time updates", "Shared pipelines", "Role-based access"]
  },
  {
    icon: MessageSquare,
    color: "accent",
    title: "Smart Communication",
    description: "Stay in touch with leads through integrated communication tools.",
    highlights: ["Email templates", "SMS integration", "Conversation tracking"]
  },
  {
    icon: Shield,
    color: "purple",
    title: "Data Security",
    description: "Your data is protected with enterprise-grade security measures.",
    highlights: ["256-bit encryption", "GDPR compliant", "Regular backups"]
  },
  {
    icon: Globe,
    color: "primary",
    title: "Global Access",
    description: "Access your leads and data from anywhere in the world.",
    highlights: ["Mobile app", "Browser-based", "Offline capabilities"]
  },
  {
    icon: Zap,
    color: "accent",
    title: "Automation",
    description: "Automate repetitive tasks and focus on what matters most.",
    highlights: ["Email sequences", "Follow-up reminders", "Lead scoring"]
  },
  {
    icon: LineChart,
    color: "purple",
    title: "Analytics",
    description: "Get insights into your lead management performance.",
    highlights: ["Conversion tracking", "Custom reports", "Performance dashboards"]
  }
];

export const Features = () => {
  return (
    <section id="features" className="py-20 md:py-28 lg:py-36 relative overflow-hidden">
      {/* Enhanced background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-background/95 to-background" />
      <div className="absolute top-40 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '15s' }} />
      <div className="absolute bottom-20 right-10 w-72 h-72 bg-accent/5 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '20s' }} />
      <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-purple/5 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '18s' }} />

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid-pattern.svg')] bg-center opacity-[0.02] dark:opacity-[0.03]"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-3xl mx-auto text-center mb-16 md:mb-24">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 dark:from-primary/30 dark:to-accent/30 text-primary dark:text-primary/90 text-sm font-medium mb-8 shadow-sm">
            <Sparkles className="h-4 w-4 mr-2 animate-pulse" />
            Features That Drive Results
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-8 tracking-tight leading-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary via-accent to-purple">
              Everything You Need
            </span>
            <span className="block text-foreground mt-2 relative">
              To Close More Deals
              <span className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-accent rounded-full"></span>
            </span>
          </h2>

          <p className="text-lg text-muted-foreground max-w-2xl mx-auto mt-8 leading-relaxed">
            Our Sales CRM combines powerful features with an intuitive interface, helping your team collaborate effectively and convert more leads into customers.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="overflow-hidden border border-border/30 bg-card/80 backdrop-blur-sm hover:shadow-xl transition-all duration-500 group hover:-translate-y-1"
            >
              <CardContent className="p-8 flex flex-col h-full relative">
                {/* Decorative corner accent */}
                <div className={cn(
                  "absolute top-0 right-0 w-24 h-24 rounded-bl-full opacity-10",
                  feature.color === "primary" ? "bg-primary" :
                  feature.color === "accent" ? "bg-accent" : "bg-purple"
                )}></div>

                <div className={cn(
                  "relative rounded-xl w-14 h-14 flex items-center justify-center mb-6 transition-all duration-300",
                  feature.color === "primary" ? "bg-primary/10 group-hover:bg-primary/20" :
                  feature.color === "accent" ? "bg-accent/10 group-hover:bg-accent/20" :
                  "bg-purple/10 group-hover:bg-purple/20"
                )}>
                  <feature.icon className={cn(
                    "h-7 w-7",
                    feature.color === "primary" ? "text-primary" :
                    feature.color === "accent" ? "text-accent" : "text-purple"
                  )} />
                </div>

                <h3 className="text-xl font-bold mb-3 group-hover:text-primary transition-colors">{feature.title}</h3>
                <p className="text-muted-foreground mb-8 leading-relaxed">{feature.description}</p>

                <div className="mt-auto">
                  <div className="w-full h-px bg-gradient-to-r from-transparent via-border/70 to-transparent mb-6"></div>
                  <ul className="space-y-3">
                    {feature.highlights.map((highlight, i) => (
                      <li key={i} className="flex items-center text-sm">
                        <div className={cn(
                          "rounded-full p-1 mr-3 flex-shrink-0",
                          feature.color === "primary" ? "bg-primary/10" :
                          feature.color === "accent" ? "bg-accent/10" : "bg-purple/10"
                        )}>
                          <CheckCircle className={cn(
                            "h-3.5 w-3.5",
                            feature.color === "primary" ? "text-primary" :
                            feature.color === "accent" ? "text-accent" : "text-purple"
                          )} />
                        </div>
                        <span>{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-20 md:mt-24 text-center">
          <Button
            size="lg"
            className="gap-2 font-medium shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 bg-gradient-to-r from-primary to-accent text-white border-0"
          >
            Explore All Features <ArrowRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
    </section>
  );
};