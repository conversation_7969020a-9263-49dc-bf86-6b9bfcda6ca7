import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../utils/auth";

/**
 * Creates a new workspace
 * @route POST /api/workspace/create
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Parse request body
    const body = await request.json();
    const {
      name,
      status,
      companyType,
      companySize,
      industry,
      timezone,
      notifications,
    } = body;
    
    // Validate required fields
    if (!name || !status || !companyType || !companySize || !industry) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    
    // Create the workspace
    const { data: workspaceData, error: workspaceError } = await supabase
      .from("workspaces")
      .insert([
        {
          name,
          status,
          company_type: companyType,
          company_size: companySize,
          industry,
          timezone,
          notifications,
          owner_id: user.id,
        },
      ])
      .select();
    
    if (workspaceError) {
      return NextResponse.json({ error: workspaceError.message }, { status: 500 });
    }
    
    // Create the workspace member entry
    const { error: memberError } = await supabase
      .from("workspace_members")
      .insert({
        role: "SuperAdmin",
        added_by: user.id,
        email: user.email,
        status: "accepted",
        user_id: user.id,
        workspace_id: workspaceData[0].id,
      });
    
    if (memberError) {
      return NextResponse.json({ error: memberError.message }, { status: 500 });
    }
    
    return NextResponse.json({
      message: "Workspace and member created",
      data: workspaceData[0],
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
