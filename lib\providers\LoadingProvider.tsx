"use client";

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

// Define the shape of our loading context
interface LoadingContextType {
  isLoading: boolean;
  startLoading: (message?: string) => void;
  stopLoading: () => void;
  loadingMessage: string | null;
  setLoadingProgress: (progress: number) => void;
  loadingProgress: number;
}

// Create the context with a default value
const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

// Custom hook to use the loading context
export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState<string | null>(null);
  const [loadingProgress, setLoadingProgress] = useState(0);
  
  // Start loading with an optional message
  const startLoading = useCallback((message?: string) => {
    setIsLoading(true);
    setLoadingMessage(message || null);
    setLoadingProgress(0);
  }, []);
  
  // Stop loading
  const stopLoading = useCallback(() => {
    // Use a slight delay to ensure smooth transitions
    setTimeout(() => {
      setIsLoading(false);
      setLoadingMessage(null);
      setLoadingProgress(0);
    }, 300);
  }, []);
  
  // The value that will be provided to consumers of this context
  const value = {
    isLoading,
    startLoading,
    stopLoading,
    loadingMessage,
    setLoadingProgress,
    loadingProgress,
  };
  
  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};

// Higher-order component to wrap components that need loading functionality
export function withLoading<P extends object>(
  Component: React.ComponentType<P & { loading: LoadingContextType }>
): React.FC<P> {
  return (props: P) => {
    const loading = useLoading();
    return <Component {...props} loading={loading} />;
  };
}
