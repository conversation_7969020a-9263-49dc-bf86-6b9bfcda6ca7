import { ProfileDetails } from '../types/profile';

export const DEFAULT_PROFILE_DATA: ProfileDetails = {
  personalInfo: {
    firstName: "N/A",
    lastName: "N/A",
    email: "N/A",
    phone: "N/A",
    avatar: "/api/placeholder/200/200",
    dateOfBirth: "N/A",
  },
  professionalInfo: {
    title: "N/A",
    department: "N/A",
    company: "N/A",
    startDate: "N/A",
  },
  socialLinks: {},
  apiKeys: {
    openAI: "",
    togetherAPI: "",
    reoonEmail: "",
    bigDataCloud: "",
  },
};

export const STORAGE_BUCKET = "profiles";
export const AVATAR_PATH_PREFIX = "avatars/";

export const API_KEY_DISPLAY_CONFIG = {
  minLength: 9,
  prefixLength: 5,
  suffixLength: 4,
  placeholder: "Not set",
};
