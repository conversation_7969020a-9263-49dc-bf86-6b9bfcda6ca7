import { NextRequest, NextResponse } from "next/server";
import { sendEmail } from "../utils/mailer";

/**
 * Send an email
 * @route POST /api/email/send
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { to, subject, text, html, options } = body;
    
    // Validate required fields
    if (!to || !subject || (!text && !html)) {
      return NextResponse.json({ 
        success: false, 
        error: "Invalid input data. 'to', 'subject', and either 'text' or 'html' are required." 
      }, { status: 400 });
    }
    
    // Send the email
    const info = await sendEmail(
      to, 
      subject, 
      html || `<p>${text}</p>`, 
      options || {}
    );
    
    return NextResponse.json({ 
      success: true, 
      messageId: info.messageId 
    }, { status: 200 });
  } catch (error) {
    console.error("Error sending email:", error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}
