import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWebhookPermission } from "../../utils/auth";

/**
 * Update webhook status
 * @route PUT /api/webhooks/[id]/status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const id = params.id;

    // Parse request body
    const body = await request.json();
    const { status } = body;

    // Validate request body
    if (status === undefined) {
      return NextResponse.json({ error: "Status is required" }, { status: 400 });
    }

    // Check if user has permission to update this webhook
    const permission = await checkWebhookPermission(user.id, id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }

    // Update the webhook status
    const { data, error } = await supabase
      .from("webhooks")
      .update({ status })
      .eq("id", id)
      .select();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error updating webhook status:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
