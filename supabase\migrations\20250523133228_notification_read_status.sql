-- Migration for table: notification_read_status
CREATE TABLE IF NOT EXISTS "notification_read_status" (
  "id" uuid PRIMARY KEY NOT NULL,
  "notification_id" integer,
  "user_id" uuid NOT NULL,
  "read" boolean,
  "read_at" timestamp with time zone
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "notification_read_status_user_id_idx" ON "notification_read_status" ("user_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "notification_read_status" ADD CONSTRAINT "fk_notification_read_status_user_id"
--   FOREIGN KEY ("user_id") REFERENCES "auth.users" (id);

-- Enable Row Level Security
ALTER TABLE "notification_read_status" ENABLE ROW LEVEL SECURITY;

