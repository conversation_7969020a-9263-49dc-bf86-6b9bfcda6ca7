"use client";

import React from 'react';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { BillingCycleToggleProps } from '../types/subscription';

export function BillingCycleToggle({ billingCycle, onToggle }: BillingCycleToggleProps) {
  const handleToggle = (checked: boolean) => {
    onToggle(checked ? 'yearly' : 'monthly');
  };

  return (
    <div className="flex justify-center mb-8 md:mb-12 w-full">
      <div className="bg-gradient-to-r from-muted/80 to-muted/30 p-1.5 rounded-full flex items-center max-w-sm mx-auto shadow-sm border border-muted/50 backdrop-blur-sm">
        <div className="flex items-center space-x-4 px-5 py-1 w-full justify-between">
          {/* Monthly Option */}
          <div className="flex items-center gap-1.5">
            <svg 
              width="18" 
              height="18" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg" 
              className={`${billingCycle === 'monthly' ? 'text-primary' : 'text-muted-foreground/70'}`}
            >
              <path 
                d="M8 7V3M16 7V3M7 11H17M5 21H19C20.1046 21 21 20.1046 21 19V7C21 5.89543 20.1046 5 19 5H5C3.89543 5 3 5.89543 3 7V19C3 20.1046 3.89543 21 5 21Z" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
            <Label 
              htmlFor="billing-cycle" 
              className={`${
                billingCycle === 'monthly' 
                  ? 'font-medium text-primary' 
                  : 'text-muted-foreground'
              } text-sm sm:text-base transition-colors duration-200 cursor-pointer`} 
              suppressHydrationWarning
            >
              Monthly
            </Label>
          </div>
          
          {/* Switch */}
          <div className="relative">
            <Switch
              id="billing-cycle"
              checked={billingCycle === 'yearly'}
              onCheckedChange={handleToggle}
              className="data-[state=checked]:bg-primary/90 data-[state=unchecked]:bg-muted-foreground/30"
            />
            <div className="absolute -top-1 left-1/2 -translate-x-1/2 -translate-y-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
              <span className="bg-background text-xs px-2 py-1 rounded shadow-sm">
                {billingCycle === 'yearly' ? 'Switch to monthly' : 'Switch to yearly'}
              </span>
            </div>
          </div>
          
          {/* Yearly Option */}
          <div className="flex items-center gap-1.5">
            <svg 
              width="18" 
              height="18" 
              viewBox="0 0 24 24" 
              fill="none" 
              xmlns="http://www.w3.org/2000/svg"
              className={`${billingCycle === 'yearly' ? 'text-primary' : 'text-muted-foreground/70'}`}
            >
              <path 
                d="M21 10H3M16 2V6M8 2V6M9 14L11 16L15 12M7.8 22H16.2C17.8802 22 18.7202 22 19.362 21.673C19.9265 21.3854 20.3854 20.9265 20.673 20.362C21 19.7202 21 18.8802 21 17.2V8.8C21 7.11984 21 6.27976 20.673 5.63803C20.3854 5.07354 19.9265 4.6146 19.362 4.32698C18.7202 4 17.8802 4 16.2 4H7.8C6.11984 4 5.27976 4 4.63803 4.32698C4.07354 4.6146 3.6146 5.07354 3.32698 5.63803C3 6.27976 3 7.11984 3 8.8V17.2C3 18.8802 3 19.7202 3.32698 20.362C3.6146 20.9265 4.07354 21.3854 4.63803 21.673C5.27976 22 6.11984 22 7.8 22Z" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
            <Label 
              htmlFor="billing-cycle" 
              className={`${
                billingCycle === 'yearly' 
                  ? 'font-medium text-primary' 
                  : 'text-muted-foreground'
              } text-sm sm:text-base transition-colors duration-200 cursor-pointer`} 
              suppressHydrationWarning
            >
              Yearly
            </Label>
            <Badge 
              variant="outline" 
              className={`ml-1 ${
                billingCycle === 'yearly' 
                  ? 'bg-green-500/10 text-green-500 border-green-500/20' 
                  : 'bg-primary/5 text-primary/70 border-primary/10'
              } text-xs whitespace-nowrap transition-colors duration-200`}
            >
              Save 20%
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
}
