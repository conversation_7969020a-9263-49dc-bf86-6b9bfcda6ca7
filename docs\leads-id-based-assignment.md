# Lead Assignment System Migration: ID-Based References

## Overview

This document outlines the migration from name/color-based lead assignments to ID-based references for better data integrity and performance.

## Changes Made

### 1. Database Schema Updates

**New Columns Added to `leads` table:**
- `status_id` (INTEGER) - References `status.id`
- `assigned_to_id` (INTEGER) - References `workspace_members.id`

**Migration Files:**
- `20250101000000_update_leads_assignment_schema.sql` - Schema changes
- `20250101000001_migrate_leads_data.sql` - Data migration

### 2. Frontend Changes

**Updated Components:**
- `LeadTableRow.tsx` - Desktop table row with ID-based selects
- `LeadMobileCard.tsx` - Mobile card with ID-based selects
- `useLeadActions.ts` - Hook for handling status/assignment changes
- `useLeadsData.ts` - Data processing with new fields

**New Utility Functions:**
- `leadDataUtils.ts` - Helper functions for ID/name lookups

**Updated Types:**
- `leads.ts` - Added new ID fields to interfaces

### 3. API Updates

**Modified Routes:**
- `/api/leads/[id]/assign` - Handles both ID-based and legacy assignments
- `/api/leads/[id]/route` - Handles both ID-based and legacy status updates

## How It Works

### Status Assignment

**New Approach (ID-based):**
```typescript
// Frontend sends
{
  id: 123,
  name: "Qualified",
  color: "#00ff00"
}

// API stores
{
  status_id: 123,
  status: { name: "Qualified", color: "#00ff00" } // for backward compatibility
}
```

**Legacy Approach (still supported):**
```typescript
// Frontend sends
{
  name: "Qualified",
  color: "#00ff00"
}

// API stores
{
  status: { name: "Qualified", color: "#00ff00" }
}
```

### Member Assignment

**New Approach (ID-based):**
```typescript
// Frontend sends
{
  id: 456,
  name: "John Doe",
  role: "admin"
}

// API stores
{
  assigned_to_id: 456,
  assign_to: { name: "John Doe", role: "admin" } // for backward compatibility
}
```

**Legacy Approach (still supported):**
```typescript
// Frontend sends
{
  name: "John Doe",
  role: "admin"
}

// API stores
{
  assign_to: { name: "John Doe", role: "admin" }
}
```

## Benefits

1. **Data Integrity**: Foreign key constraints prevent invalid references
2. **Performance**: Indexed lookups instead of string matching
3. **Consistency**: Single source of truth for status/member data
4. **Scalability**: Better performance with large datasets
5. **Backward Compatibility**: Legacy fields maintained during transition

## Utility Functions

### `getLeadStatusDisplay(lead, statusData)`
Returns status name and color for display, checking ID first, then falling back to legacy data.

### `getLeadAssignmentDisplay(lead, membersData)`
Returns member name and role for display, checking ID first, then falling back to legacy data.

### `createStatusSelectValue(statusId, statusData, legacyStatus)`
Creates select component value with both ID and display data.

### `createMemberSelectValue(memberId, membersData, legacyAssignTo)`
Creates select component value with both ID and display data.

## Migration Process

1. **Run Schema Migration**: Adds new columns with foreign key constraints
2. **Run Data Migration**: Populates new ID fields from existing legacy data
3. **Deploy Frontend**: Updated components use new ID-based system
4. **Verify**: Both old and new data formats work correctly
5. **Future Cleanup**: Remove legacy columns after full migration

## Testing

The system is designed to work with:
- ✅ New leads with ID-based assignments
- ✅ Existing leads with legacy assignments
- ✅ Mixed scenarios during migration
- ✅ Fallback to legacy data when IDs are missing

## Rollback Plan

If issues occur:
1. Frontend automatically falls back to legacy fields
2. API continues to update legacy fields
3. New columns can be dropped if needed
4. No data loss occurs

## Future Enhancements

After successful migration:
1. Remove legacy `status` and `assign_to` columns
2. Make ID fields NOT NULL
3. Add additional constraints
4. Optimize queries further
