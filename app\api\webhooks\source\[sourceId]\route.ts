import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../../utils/auth";

/**
 * Get webhook by source ID
 * @route GET /api/webhooks/source/[sourceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { sourceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const sourceId = params.sourceId;

    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");

    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }

    // Construct the webhook URL
    const webhookUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/leads?action=getLeads&sourceId=${sourceId}&workspaceId=${workspaceId}`;

    // Query the webhooks table for matching webhook
    const { data, error } = await supabase
      .from("webhooks")
      .select("id, name, type")
      .eq("webhook_url", webhookUrl);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // If a matching webhook is found, return its details
    if (data && data.length > 0) {
      return NextResponse.json({
        id: data[0].id,
        name: data[0].name,
        type: data[0].type
      }, { status: 200 });
    }

    // If no webhook matches, return a 404
    return NextResponse.json({ error: "No matching webhook found" }, { status: 404 });
  } catch (error) {
    console.error("Error fetching webhook by source:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
