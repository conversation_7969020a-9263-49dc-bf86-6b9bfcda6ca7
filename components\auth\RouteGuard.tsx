"use client";

import React from 'react';
import { useAuth } from '@/lib/hooks/useAuth';
import { AuthLoadingScreen } from './AuthLoadingScreen';

interface RouteGuardProps {
  children: React.ReactNode;
}

export function RouteGuard({ children }: RouteGuardProps) {
  const { loading, isAuthenticated, isProtectedRoute, isAuthRoute } = useAuth();

  // Show loading screen while checking authentication
  if (loading) {
    return <AuthLoadingScreen />;
  }

  // For protected routes, ensure user is authenticated
  if (isProtectedRoute && !isAuthenticated) {
    // This will be handled by the AuthContext redirect
    return <AuthLoadingScreen />;
  }

  // For auth routes, ensure user is not already authenticated
  if (isAuthRoute && isAuthenticated) {
    // This will be handled by the AuthContext redirect
    return <AuthLoadingScreen />;
  }

  // Render children for all other cases
  return <>{children}</>;
}
