"use client";

import React from 'react';
import { Check, X, Users, CheckCircle, Zap, Mail, MessageSquare, Shield } from 'lucide-react';
import { FeatureComparisonProps, SUBSCRIPTION_PLANS } from '../types/subscription';

export function FeatureComparison({ billingCycle, getPrice }: FeatureComparisonProps) {
  const features = [
    {
      name: 'Workspace Limit',
      icon: Users,
      getValue: (plan: any) => plan.workspaceLimit === Infinity ? "Unlimited" : plan.workspaceLimit
    },
    {
      name: 'Lead Management',
      icon: CheckCircle,
      getValue: () => true // All plans have this
    },
    {
      name: 'AI Integration',
      icon: Zap,
      getValue: (plan: any) => plan.aiIntegration
    },
    {
      name: 'Email Marketing',
      icon: Mail,
      getValue: (plan: any) => plan.emailMarketing
    },
    {
      name: 'SMS Marketing',
      icon: MessageSquare,
      getValue: (plan: any) => plan.smsMarketing
    },
    {
      name: 'Priority Support',
      icon: Shield,
      getValue: (plan: any) => plan.id === 'enterprise'
    }
  ];

  const renderFeatureValue = (feature: any, plan: any) => {
    const value = feature.getValue(plan);
    
    if (typeof value === 'boolean') {
      return (
        <div className="flex justify-center">
          <div className={`h-5 w-5 rounded-full ${
            value ? 'bg-primary/10' : 'bg-muted'
          } flex items-center justify-center`}>
            {value ? (
              <Check className="h-3 w-3 text-primary" />
            ) : (
              <X className="h-3 w-3 text-muted-foreground" />
            )}
          </div>
        </div>
      );
    }
    
    return (
      <span className={plan.id !== 'starter' ? 'font-medium' : ''}>
        {value}
      </span>
    );
  };

  return (
    <div className="mt-16 md:mt-20 lg:mt-24 w-full">
      <div className="text-center mb-6 md:mb-8">
        <h2 className="text-xl md:text-2xl lg:text-3xl font-bold mb-3">Compare Plan Features</h2>
        <p className="text-sm md:text-base text-muted-foreground max-w-2xl mx-auto">
          See which plan is right for your business needs
        </p>
      </div>

      <div className="overflow-x-auto rounded-xl border shadow-sm w-full">
        <table className="w-full border-collapse bg-white dark:bg-black">
          <thead>
            <tr className="bg-muted/50">
              <th className="text-left py-3 md:py-4 px-4 md:px-6 text-sm font-medium min-w-[140px]">
                Feature
              </th>
              {Object.values(SUBSCRIPTION_PLANS).map((plan) => (
                <th key={plan.id} className="text-center py-3 md:py-4 px-3 md:px-4 text-sm font-medium min-w-[120px]">
                  <div className="flex flex-col items-center">
                    <span className="text-sm md:text-base font-semibold mb-1">{plan.name}</span>
                    {plan.id !== 'starter' && (
                      <span className="text-primary font-medium text-xs md:text-sm" suppressHydrationWarning>
                        ${getPrice(plan.price)}{billingCycle === 'monthly' ? '/mo' : '/yr'}
                      </span>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <tr key={feature.name} className="border-t hover:bg-muted/30 transition-colors">
                  <td className="py-3 md:py-4 px-4 md:px-6 text-xs md:text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      <span className="break-words">{feature.name}</span>
                    </div>
                  </td>
                  {Object.values(SUBSCRIPTION_PLANS).map((plan) => (
                    <td key={plan.id} className="text-center py-3 md:py-4 px-3 md:px-4 text-xs md:text-sm">
                      {renderFeatureValue(feature, plan)}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
