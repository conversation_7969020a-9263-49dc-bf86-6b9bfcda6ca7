import { useMemo } from "react";
import { PlatformIntegration, FilterType } from "../types/integration";
import { applyFilters } from "../utils/integrationUtils";

interface UseFilteredPlatformsProps {
  platforms: PlatformIntegration[];
  searchTerm: string;
  activeFilter: FilterType;
}

export const useFilteredPlatforms = ({
  platforms,
  searchTerm,
  activeFilter,
}: UseFilteredPlatformsProps) => {
  // Memoized filtered platforms
  const filteredPlatforms = useMemo(() => {
    return applyFilters(platforms, searchTerm, activeFilter);
  }, [platforms, searchTerm, activeFilter]);

  // Memoized analytics
  const analytics = useMemo(() => {
    const total = filteredPlatforms.length;
    const originalTotal = platforms.length;
    
    return {
      total,
      originalTotal,
      hasResults: total > 0,
      isFiltered: searchTerm.trim() !== "" || activeFilter !== "all",
      searchResultsCount: searchTerm.trim() !== "" ? total : null,
      filterResultsCount: activeFilter !== "all" ? total : null,
    };
  }, [filteredPlatforms, platforms, searchTerm, activeFilter]);

  return {
    filteredPlatforms,
    analytics,
  };
};
