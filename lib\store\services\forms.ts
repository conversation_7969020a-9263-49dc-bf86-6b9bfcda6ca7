import { formsApi } from "../base/forms";

// Define types for the API
interface Form {
  id: string;
  name: string;
  description?: string;
  html_content: string;
  css_content?: string;
  js_content?: string;
  is_active: boolean;
  workspace_id: number;
  lead_source_id?: string;
  created_at: string;
  updated_at: string;
}

interface CreateFormRequest {
  name: string;
  description?: string;
  html_content: string;
  css_content?: string;
  js_content?: string;
  is_active?: boolean;
  workspace_id: number;
  lead_source_id?: string;
}

interface UpdateFormRequest {
  id: string;
  name?: string;
  description?: string;
  html_content?: string;
  css_content?: string;
  js_content?: string;
  is_active?: boolean;
  lead_source_id?: string;
}

// Define the RTK Query API
export const formsApis = formsApi.injectEndpoints({
  endpoints: (builder) => ({
    // Get all forms for a workspace
    getForms: builder.query<Form[], string>({
      query: (workspaceId) => ({
        url: `?workspace_id=${workspaceId}`,
        method: "GET",
      }),
      transformResponse: (response: { forms: Form[] }) => response.forms,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Form' as const, id })),
              { type: 'FormList' as const, id: 'LIST' },
              { type: 'FormByWorkspace' as const, id: 'LIST' }
            ]
          : [
              { type: 'FormList' as const, id: 'LIST' },
              { type: 'FormByWorkspace' as const, id: 'LIST' }
            ],
    }),
    
    // Get a form by ID
    getFormById: builder.query<Form, string>({
      query: (id) => ({
        url: `/${id}`,
        method: "GET",
      }),
      transformResponse: (response: { form: Form }) => response.form,
      providesTags: (result, error, id) => [{ type: 'Form' as const, id }],
    }),
    
    // Create a new form
    createForm: builder.mutation<Form, CreateFormRequest>({
      query: (formData) => ({
        url: "",
        method: "POST",
        body: formData,
      }),
      transformResponse: (response: { form: Form }) => response.form,
      invalidatesTags: (result, error, arg) => [
        { type: 'FormList' as const, id: 'LIST' },
        { type: 'FormByWorkspace' as const, id: arg.workspace_id.toString() },
        { type: 'FormByWorkspace' as const, id: 'LIST' }
      ],
    }),
    
    // Update a form
    updateForm: builder.mutation<Form, UpdateFormRequest>({
      query: ({ id, ...formData }) => ({
        url: `?id=${id}`,
        method: "PATCH",
        body: formData,
      }),
      transformResponse: (response: { form: Form }) => response.form,
      invalidatesTags: (result, error, arg) => [
        { type: 'Form' as const, id: arg.id },
        { type: 'FormList' as const, id: 'LIST' },
        { type: 'FormByWorkspace' as const, id: 'LIST' }
      ],
    }),
    
    // Delete a form
    deleteForm: builder.mutation<{ success: boolean }, string>({
      query: (id) => ({
        url: `?id=${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Form' as const, id },
        { type: 'FormList' as const, id: 'LIST' },
        { type: 'FormByWorkspace' as const, id: 'LIST' }
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetFormsQuery,
  useGetFormByIdQuery,
  useCreateFormMutation,
  useUpdateFormMutation,
  useDeleteFormMutation,
} = formsApis;
