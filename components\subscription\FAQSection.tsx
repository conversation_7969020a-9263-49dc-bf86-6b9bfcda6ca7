"use client";

import React, { useState } from "react";
import { Plus, Minus } from "lucide-react";

const FAQSection = () => {
  const [expandedFaqs, setExpandedFaqs] = useState<Record<number, boolean>>({
    0: false,
    1: false,
    2: false,
    3: false
  });

  // Toggle FAQ expansion
  const toggleFaq = (index: number) => {
    setExpandedFaqs(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const faqItems = [
    {
      question: "Can I upgrade or downgrade my plan later?",
      answer: "Yes, you can change your subscription plan at any time. When upgrading, you'll have immediate access to new features. When downgrading, changes will take effect at the end of your current billing cycle."
    },
    {
      question: "How does the billing cycle work?",
      answer: "Monthly plans are billed every 30 days from your initial subscription date. Yearly plans are billed once every 12 months, offering a 20% discount compared to monthly billing."
    },
    {
      question: "Can I cancel my subscription?",
      answer: "Yes, you can cancel your subscription at any time from your account settings. Your subscription will remain active until the end of your current billing period."
    },
    {
      question: "Is there a free trial available?",
      answer: "You can start with our Starter plan which is free forever with basic features. This allows you to explore the platform before committing to a paid subscription."
    }
  ];

  return (
    <div className="mt-12 md:mt-16 w-full">
      <h2 className="text-xl md:text-2xl font-bold mb-4 md:mb-6 text-center">Frequently Asked Questions</h2>
      <div className="space-y-4 w-full">
        {faqItems.map((faq, index) => (
          <div key={index} className="bg-muted/30 rounded-lg overflow-hidden border border-transparent hover:border-muted transition-all duration-200">
            <div
              className="p-4 sm:p-6 flex justify-between items-center cursor-pointer"
              onClick={() => toggleFaq(index)}
            >
              <h3 className="font-medium text-base sm:text-lg">{faq.question}</h3>
              <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 ml-4">
                {expandedFaqs[index] ? (
                  <Minus className="h-3 w-3 text-primary" />
                ) : (
                  <Plus className="h-3 w-3 text-primary" />
                )}
              </div>
            </div>
            <div
              className={`px-4 sm:px-6 overflow-hidden transition-all duration-300 ease-in-out ${
                expandedFaqs[index] ? 'max-h-40 pb-4 sm:pb-6' : 'max-h-0'
              }`}
            >
              <p className="text-sm text-muted-foreground">
                {faq.answer}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FAQSection;
