# PostgreSQL configuration optimized for production
# Adjust these values based on your server's available resources

# Connection Settings
max_connections = 100                  # Reduce from default to prevent connection overload
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 1GB                   # 25% of available RAM for dedicated server
work_mem = 32MB                        # Increase for complex queries
maintenance_work_mem = 256MB           # Increase for maintenance operations
effective_cache_size = 3GB             # 75% of available RAM for dedicated server

# Write Ahead Log (WAL) Settings
wal_buffers = 16MB                     # Increase for better WAL performance
wal_writer_delay = 200ms
wal_level = replica                    # Minimum needed for replication
max_wal_size = 1GB                     # Increase to reduce checkpoint frequency
min_wal_size = 80MB

# Background Writer
bgwriter_delay = 200ms
bgwriter_lru_maxpages = 100
bgwriter_lru_multiplier = 2.0

# Query Planner
random_page_cost = 1.1                 # Lowered for SSD storage
effective_io_concurrency = 200         # Increased for SSD storage
default_statistics_target = 100        # Increase for complex queries

# Autovacuum Settings
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.05  # Vacuum when 5% of table is dirty
autovacuum_analyze_scale_factor = 0.025
autovacuum_vacuum_cost_delay = 20ms
autovacuum_vacuum_cost_limit = 200

# Statement Behavior
statement_timeout = 60000              # 60 seconds
lock_timeout = 10000                   # 10 seconds
idle_in_transaction_session_timeout = 60000  # 60 seconds

# Logging
log_min_duration_statement = 1000      # Log statements taking more than 1 second
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0
log_autovacuum_min_duration = 250      # Log autovacuum taking more than 250ms

# Query Optimization
enable_partitionwise_join = on
enable_partitionwise_aggregate = on
jit = on                               # Enable JIT compilation

# Client Connection Defaults
tcp_keepalives_idle = 60
tcp_keepalives_interval = 10
tcp_keepalives_count = 10
