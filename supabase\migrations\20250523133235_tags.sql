-- Migration for table: tags
CREATE TABLE IF NOT EXISTS "tags" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "name" text,
  "color" text,
  "work_id" integer,
  "user_id" uuid NOT NULL
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "tags_user_id_idx" ON "tags" ("user_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "tags" ADD CONSTRAINT "fk_tags_user_id"
--   FOREIGN KEY ("user_id") REFERENCES "auth.users" (id);

-- Enable Row Level Security
ALTER TABLE "tags" ENABLE ROW LEVEL SECURITY;

