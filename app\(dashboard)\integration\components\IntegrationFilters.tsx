import React from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { IntegrationFiltersProps } from "../types/integration";
import {
  INTEGRATION_MESSAGES,
  FILTER_LABELS,
  FILTER_OPTIONS
} from "../constants/integration";

export const IntegrationFilters: React.FC<IntegrationFiltersProps> = ({
  searchTerm,
  activeFilter,
  onSearchChange,
  onFilterChange,
}) => {
  return (
    <>
      {/* Search input */}
      <div className="relative">
        <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 dark:text-gray-400" />
        <Input
          placeholder={INTEGRATION_MESSAGES.SEARCH_PLACEHOLDER}
          className="pl-8 w-full md:w-64 border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-gray-900 dark:text-gray-100"
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>

      {/* Filter buttons */}
      <div className="flex flex-wrap gap-2 px-4 py-2 bg-gray-50 dark:bg-gray-800">
        {FILTER_OPTIONS.map((filter) => (
          <Button
            key={filter}
            variant={activeFilter === filter ? "default" : "outline"}
            size="sm"
            onClick={() => onFilterChange(filter)}
            className={
              activeFilter === filter
                ? "bg-black hover:bg-black/90 text-white dark:bg-white dark:hover:bg-white/90 dark:text-black"
                : "border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/50"
            }
          >
            {filter === "all" && FILTER_LABELS.ALL}
            {filter === "active" && FILTER_LABELS.ACTIVE}
            {filter === "beta" && FILTER_LABELS.BETA}
            {filter === "coming-soon" && FILTER_LABELS.COMING_SOON}
          </Button>
        ))}
      </div>
    </>
  );
};
