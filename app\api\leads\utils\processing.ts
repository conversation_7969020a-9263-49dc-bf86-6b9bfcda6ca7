import Together from "together-ai";

// Smaller, more focused system prompt
const SYSTEM_PROMPT = `Format the input data into a JSON object. ONLY OUTPUT THE JSON OBJECT, NO OTHER TEXT.
The JSON object should have the following structure:
{
  "name": "Full Name",
  "email": "<EMAIL>",
  "phone": "phone number",
  "company": "Company Name",
  "message": "Any message or notes",
  "source": "Where the lead came from",
  "additionalInfo": {
    // Any additional fields found in the input
  }
}
If a field is not present in the input, omit it from the output.`;

// Maximum number of retries for AI processing
const MAX_RETRIES = 2;

/**
 * Normalizes input data for AI processing
 * @param inputData The raw input data
 * @returns Normalized string for AI processing
 */
function normalizeInput(inputData: any): string {
  if (typeof inputData === 'string') {
    return inputData;
  }
  
  if (typeof inputData === 'object') {
    try {
      return JSON.stringify(inputData, null, 2);
    } catch (error) {
      console.error("Error stringifying input data:", error);
      return String(inputData);
    }
  }
  
  return String(inputData);
}

/**
 * Processes structured data without using AI
 * @param data The input data to process
 * @returns Processed data object or null if processing failed
 */
export function processStructuredData(data: any): any | null {
  try {
    // If data is already an object with expected fields, return it
    if (typeof data === 'object' && data !== null) {
      const result: any = {};
      
      // Extract common lead fields
      if (data.name || data.fullName || data.full_name) {
        result.name = data.name || data.fullName || data.full_name;
      }
      
      if (data.email || data.emailAddress || data.email_address) {
        result.email = data.email || data.emailAddress || data.email_address;
      }
      
      if (data.phone || data.phoneNumber || data.phone_number || data.mobile) {
        result.phone = data.phone || data.phoneNumber || data.phone_number || data.mobile;
      }
      
      if (data.company || data.companyName || data.company_name || data.organization) {
        result.company = data.company || data.companyName || data.company_name || data.organization;
      }
      
      if (data.message || data.notes || data.comments) {
        result.message = data.message || data.notes || data.comments;
      }
      
      if (data.source || data.leadSource || data.lead_source) {
        result.source = data.source || data.leadSource || data.lead_source;
      }
      
      // Add any additional fields to additionalInfo
      const additionalInfo: any = {};
      const processedFields = ['name', 'email', 'phone', 'company', 'message', 'source'];
      
      Object.keys(data).forEach(key => {
        if (!processedFields.includes(key) && 
            !['fullName', 'full_name', 'emailAddress', 'email_address', 
              'phoneNumber', 'phone_number', 'mobile', 'companyName', 
              'company_name', 'organization', 'notes', 'comments', 
              'leadSource', 'lead_source'].includes(key)) {
          additionalInfo[key] = data[key];
        }
      });
      
      if (Object.keys(additionalInfo).length > 0) {
        result.additionalInfo = additionalInfo;
      }
      
      // If we have at least one of the required fields, return the result
      if (result.name || result.email || result.phone) {
        return result;
      }
    }
    
    return null;
  } catch (error) {
    console.error("Error processing structured data:", error);
    return null;
  }
}

/**
 * Processes data using AI
 * @param data The input data to process
 * @param apiKey Optional Together AI API key
 * @returns Processed data object
 */
export async function processWithAI(data: any, apiKey?: string): Promise<any> {
  let attempt = 0;
  let together = new Together({ 
    apiKey: apiKey || process.env.TOGETHER_API_KEY 
  });
  
  while (attempt < MAX_RETRIES) {
    try {
      const normalizedInput = normalizeInput(data);
      let formattedResponse = "";
      
      // Use a less expensive model for the first attempt
      const model = attempt === 0 
        ? "mistralai/Mistral-7B-Instruct-v0.2" 
        : "meta-llama/Llama-3.3-70B-Instruct-Turbo";
      
      const response = await together.chat.completions.create({
        messages: [
          { role: "system", content: SYSTEM_PROMPT },
          { role: "user", content: normalizedInput }
        ],
        model: model,
        temperature: 0,
        top_p: 1,
        max_tokens: 500,
        stream: true,
      });

      for await (const token of response) {
        if (token.choices[0]?.delta?.content) {
          formattedResponse += token.choices[0].delta.content;
        }
      }
      
      // Try to parse the response as JSON
      try {
        const jsonResponse = JSON.parse(formattedResponse);
        return jsonResponse;
      } catch (parseError) {
        console.error("Error parsing AI response:", parseError);
        
        // Try to extract JSON from the response using regex
        const jsonMatch = formattedResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            const extractedJson = JSON.parse(jsonMatch[0]);
            return extractedJson;
          } catch (extractError) {
            console.error("Error extracting JSON from AI response:", extractError);
          }
        }
        
        // If we're on the last attempt, return a basic object with the raw response
        if (attempt === MAX_RETRIES - 1) {
          return { message: formattedResponse };
        }
      }
    } catch (error) {
      console.error(`AI processing attempt ${attempt + 1} failed:`, error);
      
      // If we're on the last attempt, return a basic object
      if (attempt === MAX_RETRIES - 1) {
        return { message: String(data) };
      }
    }
    
    attempt++;
  }
  
  // Fallback if all attempts fail
  return { message: String(data) };
}
