import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { validatePhoneNumber } from "../../utils/validation";
import { applyCors } from "../../utils/middleware";

/**
 * Validate a phone number
 * @route POST /api/leads/validate/phone
 */
export async function POST(request: NextRequest) {
  // Apply CORS middleware
  await applyCors(request);
  
  try {
    // Parse request body
    const body = await request.json();
    const { phone } = body;
    
    if (!phone) {
      return NextResponse.json({ 
        success: false, 
        error: "Phone number is required" 
      }, { status: 400 });
    }
    
    // Get user metadata for API keys if available
    let userMetadata = null;
    const authHeader = request.headers.get("authorization");
    
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.split(" ")[1];
      const { data: { user }, error } = await supabase.auth.getUser(token);
      
      if (!error && user) {
        userMetadata = user.user_metadata;
      }
    }
    
    // Extract API key from user metadata
    const phoneApiKey = userMetadata?.bigDataCloud || null;
    
    // Validate the phone number
    const isValid = await validatePhoneNumber(phone, phoneApiKey);
    
    return NextResponse.json({ 
      success: true, 
      isValid 
    }, { status: 200 });
  } catch (error) {
    console.error("Error validating phone number:", error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}
