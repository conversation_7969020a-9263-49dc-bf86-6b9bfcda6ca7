"use client";

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Mail, Phone, Calendar } from "lucide-react";
import { ProfileDetails } from '../../types/profile';
import { getUserInitials } from '../../utils/profile';

interface ProfileHeaderProps {
  profileData: ProfileDetails;
}

export function ProfileHeader({ profileData }: ProfileHeaderProps) {
  const { personalInfo } = profileData;
  const userInitials = getUserInitials(personalInfo.firstName, personalInfo.lastName);

  return (
    <Card className="md:col-span-1 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px] shadow-sm">
      <CardContent className="pt-4 sm:pt-6 flex flex-col items-center p-3 sm:p-4 md:p-6">
        <Avatar className="h-20 w-20 sm:h-24 sm:w-24 md:h-32 md:w-32 mb-3 sm:mb-4 ring-2 ring-gray-100 dark:ring-gray-800">
          <AvatarImage
            src={personalInfo.avatar}
            alt="Profile Picture"
            className="object-cover"
          />
          <AvatarFallback className="text-sm sm:text-base md:text-lg font-semibold bg-gradient-to-br from-primary/10 to-accent/10">
            {userInitials}
          </AvatarFallback>
        </Avatar>

        <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-center mb-3 sm:mb-4 leading-tight">
          {personalInfo.firstName} {personalInfo.lastName}
        </h2>

        <div className="w-full space-y-2 sm:space-y-3 px-2 sm:px-4">
          <div className="flex items-start sm:items-center gap-2 sm:gap-3">
            <Mail className="h-4 w-4 text-muted-foreground shrink-0 mt-0.5 sm:mt-0" />
            <span className="text-xs sm:text-sm break-all leading-relaxed">
              {personalInfo.email}
            </span>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <Phone className="h-4 w-4 text-muted-foreground shrink-0" />
            <span className="text-xs sm:text-sm">{personalInfo.phone}</span>
          </div>
          <div className="flex items-center gap-2 sm:gap-3">
            <Calendar className="h-4 w-4 text-muted-foreground shrink-0" />
            <span className="text-xs sm:text-sm">Born on {personalInfo.dateOfBirth}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
