"use client";

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { EditProfileDialogProps, ProfileFormData } from '../types/profile';
import { useImageUpload } from '../hooks/useImageUpload';
import { PersonalInfoSection } from './form-sections/PersonalInfoSection';
import { ProfessionalInfoSection } from './form-sections/ProfessionalInfoSection';
import { SocialLinksSection } from './form-sections/SocialLinksSection';
import { ApiKeysSection } from './form-sections/ApiKeysSection';

export function EditProfileDialog({
  profileData,
  onProfileUpdate,
  open,
  onOpenChange,
}: EditProfileDialogProps) {
  const { uploadingImage } = useImageUpload();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: profileData.personalInfo.firstName,
    lastName: profileData.personalInfo.lastName,
    phone: profileData.personalInfo.phone,
    dateOfBirth: profileData.personalInfo.dateOfBirth,
    title: profileData.professionalInfo.title,
    department: profileData.professionalInfo.department,
    company: profileData.professionalInfo.company,
    startDate: profileData.professionalInfo.startDate,
    linkedin: profileData.socialLinks.linkedin || "",
    twitter: profileData.socialLinks.twitter || "",
    github: profileData.socialLinks.github || "",
    avatar: profileData.personalInfo.avatar,
    openAI: profileData.apiKeys?.openAI || "",
    togetherAPI: profileData.apiKeys?.togetherAPI || "",
    reoonEmail: profileData.apiKeys?.reoonEmail || "",
    bigDataCloud: profileData.apiKeys?.bigDataCloud || "",
  });

  const updateFormData = (field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await onProfileUpdate(formData);
      onOpenChange(false);
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setLoading(false);
    }
  };

  const isSubmitDisabled = loading || uploadingImage;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95%] sm:w-[90%] max-w-lg max-h-[95vh] sm:max-h-[90vh] overflow-y-auto p-4 sm:p-6">
        <DialogHeader className="pb-4">
          <DialogTitle className="text-lg sm:text-xl font-bold">Edit Profile</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          <PersonalInfoSection
            formData={formData}
            updateFormData={updateFormData}
            uploadingImage={uploadingImage}
          />

          <ProfessionalInfoSection
            formData={formData}
            updateFormData={updateFormData}
          />

          <SocialLinksSection
            formData={formData}
            updateFormData={updateFormData}
          />

          <ApiKeysSection
            formData={formData}
            updateFormData={updateFormData}
          />

          <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 pt-4 sm:pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              className="w-full sm:w-auto order-2 sm:order-1"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitDisabled}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitDisabled}
              className="w-full sm:w-auto order-1 sm:order-2"
            >
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
