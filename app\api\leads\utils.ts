import { NextRequest } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

// Notification interfaces
export interface NotificationDetails {
  [key: string]: any;
}

export interface Notification {
  lead_id: string;
  action_type: string;
  user_id: string;
  workspace_id: string;
  details: NotificationDetails;
  read: boolean;
  created_at: string;
}

export interface WorkspaceMember {
  user_id: string;
}

// Authentication helper
export async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }

  const token = authHeader.split(" ")[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);

  if (error || !user) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }

  return { user, token };
}

// Notification helper
export async function notifyLeadChange(
  leadId: string,
  action: string,
  userId: string,
  workspaceId: string,
  details: NotificationDetails = {}
): Promise<Notification[] | undefined> {
  try {
    // Fetch user information from workspace_members for this specific workspace
    const { data: workspaceMemberData, error: workspaceMemberError } = await supabase
      .from("workspace_members")
      .select("name")
      .eq("user_id", userId)
      .eq("workspace_id", workspaceId) // Add this to get only the relevant workspace membership
      .single();

    let userDisplayName = "Unknown user";

    if (workspaceMemberError) {
      console.error("Failed to fetch workspace member data:", workspaceMemberError.message);

      // Fallback: try to get the name from other sources if available
      // For example, you might have a separate users or profiles table
    } else if (workspaceMemberData) {
      userDisplayName = workspaceMemberData.name;
    }

    // Enrich the details with names for various user IDs
    const enrichedDetails = { ...details };
    enrichedDetails.actor_name = userDisplayName;

    if (details.updated_by) {
      const { data: updaterData, error: updaterError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.updated_by)
        .eq("workspace_id", workspaceId)
        .single();

      if (!updaterError && updaterData) {
        enrichedDetails.updated_by_name = updaterData.name;
      }
    }

    // For deleted_by
    if (details.deleted_by) {
      const { data: deleterData, error: deleterError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.deleted_by)
        .eq("workspace_id", workspaceId)
        .single();

      if (!deleterError && deleterData) {
        enrichedDetails.deleted_by_name = deleterData.name;
      }
    }

    // For new_assignee (if present)
    if (details.new_assignee) {
      const { data: assigneeData, error: assigneeError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.new_assignee)
        .eq("workspace_id", workspaceId)
        .single();

      if (!assigneeError && assigneeData) {
        enrichedDetails.new_assignee_name = assigneeData.name;
      }
    }

    // For previous_assignee (if present)
    if (details.previous_assignee) {
      const { data: prevAssigneeData, error: prevAssigneeError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.previous_assignee)
        .eq("workspace_id", workspaceId)
        .single();

      if (!prevAssigneeError && prevAssigneeData) {
        enrichedDetails.previous_assignee_name = prevAssigneeData.name;
      }
    }

    console.log({
      leadId,
      action,
      userId,
      workspaceId,
      details: enrichedDetails,
    });

    const { data:notification, error } = await supabase.from("notifications").insert([
      {
        lead_id: leadId,
        action_type: action,
        user_id: userId,
        workspace_id: workspaceId,
        details: enrichedDetails,
        read: false,
        created_at: new Date().toISOString(),
      },
    ]).select("*");

    if (error) {
      console.error("Failed to create notification:", error.message);
    }

    const { data: members, error: membersError } = await supabase
      .from("workspace_members")
      .select("user_id")
      .eq("workspace_id", workspaceId);

    if (membersError) {
      console.error("Failed to fetch workspace members:", membersError.message);
      return;
    }
    console.log(notification)
    const { error: readStatusError } = await supabase
    .from("notification_read_status")
    .insert([
      {
        notification_id: notification[0].id,
        user_id: userId,
        read: false,
        read_at: new Date().toISOString(),
      },
    ]);
    return notification || undefined;
  } catch (err) {
    console.error("Notification error:", err);
  }
}

// Email and phone validation imports
export { validateEmail, validatePhoneNumber } from "./utils/validation";
