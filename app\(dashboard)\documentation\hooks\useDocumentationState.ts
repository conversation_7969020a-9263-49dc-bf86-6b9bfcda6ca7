import { useState, useCallback } from "react";
import { DocumentationState } from "../types/documentation";
import { copyToClipboard, handleCopyState, simulateLoading } from "../utils/documentationUtils";
import { COPY_TIMEOUT, LOADING_SIMULATION_TIME } from "../constants/documentation";

export const useDocumentationState = () => {
  // State management
  const [state, setState] = useState<DocumentationState>({
    activeTab: "overview",
    copied: null,
    expandedRow: null,
    loading: false,
  });

  // Update active tab
  const setActiveTab = useCallback((tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  // Handle copy to clipboard
  const handleCopy = useCallback(async (id: string, text: string) => {
    const success = await copyToClipboard(text);
    if (success) {
      handleCopyState(
        (copied) => setState(prev => ({ ...prev, copied })),
        id,
        COPY_TIMEOUT
      );
    }
  }, []);

  // Toggle expanded row
  const toggleRow = useCallback((rowId: string) => {
    setState(prev => ({
      ...prev,
      expandedRow: prev.expandedRow === rowId ? null : rowId
    }));
  }, []);

  // Simulate loading
  const triggerLoading = useCallback(() => {
    simulateLoading(
      (loading) => setState(prev => ({ ...prev, loading })),
      LOADING_SIMULATION_TIME
    );
  }, []);

  // Reset state
  const resetState = useCallback(() => {
    setState({
      activeTab: "overview",
      copied: null,
      expandedRow: null,
      loading: false,
    });
  }, []);

  return {
    // State
    activeTab: state.activeTab,
    copied: state.copied,
    expandedRow: state.expandedRow,
    loading: state.loading,

    // Actions
    setActiveTab,
    handleCopy,
    toggleRow,
    triggerLoading,
    resetState,
  };
};
