import { membersApi } from "../base/members";

// Request Interfaces
interface Member {
  id: string;
  email: string;
  role: "admin" | "member";
  status: "active" | "pending";
  profileImage?: string;
  name?: string;
}

interface AddMemberRequest {
  email: string;
  role: "admin" | "member";
  workspaceId: string;
  data: Member;
}

interface UpdateMemberRequest {
  id: string;
  workspaceId: string;
  updates: Partial<Member>;
}

interface DeleteMemberRequest {
  id: string;
  workspaceId: string;
}

interface UploadProfileImageRequest {
  memberId: string;
  workspaceId: string;
  imageFile: File;
}

interface ResendInviteRequest {
  memberId: any;
  workspaceId: string;
  email: string;
  status: any;
}

// Response Interfaces
interface MembersResponse {
  data: Member[];
  total: number;
}

interface AddMemberResponse {
  data: Member;
  message: string;
}

interface UpdateMemberResponse {
  data: Member;
  message: string;
}

interface DeleteMemberResponse {
  message: string;
}

interface UploadProfileImageResponse {
  imageUrl: string;
  message: string;
}

// API Definition
export const memberApi = membersApi.injectEndpoints({
  endpoints: (builder) => ({
    getMembers: builder.query<MembersResponse, string>({
      query: (workspaceId) => ({
        url: `/workspace/${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, workspaceId) => [
        { type: 'MemberByWorkspace', id: workspaceId },
        { type: 'MemberList', id: workspaceId },
        { type: 'Member', id: 'LIST' }
      ],
    }),

    // get member role
    getMemberRole: builder.query<MembersResponse, string>({
      query: (workspaceId) => ({
        url: `/role/${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, workspaceId) => [
        { type: 'MemberByWorkspace', id: workspaceId },
        { type: 'Member', id: 'LIST' }
      ],
    }),

    // Add a new member
    addMember: builder.mutation<AddMemberResponse, any>({
      query: ({ workspaceId, ...body }) => ({
        url: `/add?workspaceId=${workspaceId}`,
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'MemberByWorkspace', id: arg.workspaceId },
        { type: 'MemberList', id: arg.workspaceId },
        { type: 'Member', id: 'LIST' }
      ],
    }),

    updateMember: builder.mutation<UpdateMemberResponse, UpdateMemberRequest>({
      query: ({ workspaceId, id, updates }) => ({
        url: `/${id}/role?workspaceId=${workspaceId}`,
        method: "PUT",
        body: updates, // { role: "newRole" }
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Member', id: arg.id },
        { type: 'MemberByWorkspace', id: arg.workspaceId },
        { type: 'MemberList', id: arg.workspaceId },
        { type: 'Member', id: 'LIST' }
      ],
    }),

    // Delete a member
    deleteMember: builder.mutation<DeleteMemberResponse, DeleteMemberRequest>({
      query: ({ workspaceId, id }) => ({
        url: `/${id}?workspaceId=${workspaceId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Member', id: arg.id },
        { type: 'MemberByWorkspace', id: arg.workspaceId },
        { type: 'MemberList', id: arg.workspaceId },
        { type: 'Member', id: 'LIST' }
      ],
    }),

    // Upload profile image
    uploadProfileImage: builder.mutation<
      UploadProfileImageResponse,
      UploadProfileImageRequest
    >({
      query: ({ workspaceId, memberId, imageFile }) => {
        const formData = new FormData();
        formData.append("image", imageFile);

        return {
          url: `/${memberId}/profile-image?workspaceId=${workspaceId}`,
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: (result, error, arg) => [
        { type: 'Member', id: arg.memberId },
        { type: 'MemberByWorkspace', id: arg.workspaceId }
      ],
    }),

    // Resend invitation (legacy - kept for backward compatibility)
    resendInvite: builder.mutation<{ message: string }, ResendInviteRequest>({
      query: ({ workspaceId, email, status }) => ({
        url: `/resend-invite?workspaceId=${workspaceId}&email=${email}&status=${status}`,
        method: "POST",
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'MemberByWorkspace', id: arg.workspaceId },
        { type: 'MemberList', id: arg.workspaceId }
      ],
    }),

    // New secure invitation system
    sendSecureInvite: builder.mutation<
      { message: string; data: { id: string; email: string; role: string; expiresAt: string } },
      { workspaceId: string; email: string; role: string }
    >({
      query: ({ workspaceId, email, role }) => ({
        url: `/invite?workspaceId=${workspaceId}`,
        method: "POST",
        body: { email, role },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'MemberByWorkspace', id: arg.workspaceId },
        { type: 'MemberList', id: arg.workspaceId }
      ],
    }),

    // Accept invitation with token
    acceptInvitation: builder.mutation<
      { message: string; data: { workspaceId: string; workspaceName: string; role: string } },
      { token: string }
    >({
      query: ({ token }) => ({
        url: `/accept-invitation`,
        method: "POST",
        body: { token },
      }),
      invalidatesTags: ['MemberByWorkspace', 'MemberList', 'Workspace'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetMembersQuery,
  useGetMemberRoleQuery,
  useAddMemberMutation,
  useUpdateMemberMutation,
  useDeleteMemberMutation,
  useUploadProfileImageMutation,
  useResendInviteMutation,
  useSendSecureInviteMutation,
  useAcceptInvitationMutation,
} = memberApi;
