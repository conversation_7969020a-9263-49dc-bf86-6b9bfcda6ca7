export interface WorkspaceMember {
  id?: string;
  email: string;
  role: string;
  status: "active" | "pending";
  profileImage?: string;
  name?: string;
}

export interface MemberManagementProps {
  isAdding: boolean;
  isDeleting: boolean;
  isResending: boolean;
  isLoading: boolean;
  members: WorkspaceMember[];
  onMemberAdd: (member: WorkspaceMember) => void;
  onMemberDelete: (memberId: string) => void;
  onMemberUpdate: (member: WorkspaceMember) => void;
  onInviteResend?: (member: WorkspaceMember) => void;
}

export interface InviteFormProps {
  onInvite: (email: string, role: string) => void;
  isLoading: boolean;
}

export interface MembersListProps {
  members: WorkspaceMember[];
  isLoading: boolean;
  onMemberUpdate: (member: WorkspaceMember) => void;
  onMemberDelete: (member: WorkspaceMember) => void;
  onInviteResend?: (member: WorkspaceMember) => void;
  isDeleting: boolean;
  isResending: boolean;
}

export interface MemberCardProps {
  member: WorkspaceMember;
  onUpdate: (member: WorkspaceMember) => void;
  onDelete: (member: WorkspaceMember) => void;
  onResendInvite?: (member: WorkspaceMember) => void;
  isDeleting: boolean;
  isResending: boolean;
}

export interface EditRoleDialogProps {
  member: WorkspaceMember | null;
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { email: string; role: "admin" | "member" }) => void;
  workspaceName?: string;
}

export interface DeleteMemberDialogProps {
  member: WorkspaceMember | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isDeleting: boolean;
}

export interface ProfileImageUploadProps {
  member: WorkspaceMember;
  onImageUpdate: (memberId: string, imageUrl: string) => void;
}

export interface MemberFormData {
  email: string;
  role: "admin" | "member";
}

export interface ResendingState {
  [memberId: string]: boolean;
}

export type MemberRole = "admin" | "member" | "SuperAdmin";
export type MemberStatus = "active" | "pending";
