import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  useAddStatusMutation,
  useDeleteStatusMutation,
  useUpdateStatusMutation,
} from '@/lib/store/services/status';
import { Status, NewStatus } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';
import { validateStatus, formatStatusForAPI, handleAPIError } from '../utils/workspace';

interface UseStatusManagementProps {
  workspaceId: string;
  statusData?: any;
}

export function useStatusManagement({ workspaceId, statusData }: UseStatusManagementProps) {
  const [addStatus, { isLoading: isAddingStat }] = useAddStatusMutation();
  const [updateStatus, { isLoading: isUpdatingStatus }] = useUpdateStatusMutation();
  const [deleteStatus, { isLoading: isDeletingStatus }] = useDeleteStatusMutation();

  // State management
  const [statuses, setStatuses] = useState<Status[]>([]);
  const [newStatus, setNewStatus] = useState<NewStatus>(WORKSPACE_CONSTANTS.DEFAULTS.NEW_STATUS);
  const [isAddingStatus, setIsAddingStatus] = useState(false);
  const [statusToEdit, setStatusToEdit] = useState<Status | null>(null);
  const [statusToDelete, setStatusToDelete] = useState<Status | null>(null);

  // Initialize statuses from API data
  useEffect(() => {
    if (statusData?.data) {
      setStatuses(statusData.data);
    }
  }, [statusData]);

  // Add new status
  const handleAddStatus = async () => {
    // Validate status
    const validation = validateStatus(newStatus);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      const formattedStatus = formatStatusForAPI(newStatus);
      const result = await addStatus({
        statusData: formattedStatus,
        workspaceId,
      }).unwrap();

      // Update local state
      setStatuses((prevStatuses) => [
        ...prevStatuses,
        {
          id: result.id || '',
          name: newStatus.name,
          color: newStatus.color,
          countInStatistics: newStatus.count_statistics,
          workspace_show: newStatus.showInWorkspace,
        },
      ]);

      // Reset form
      setNewStatus(WORKSPACE_CONSTANTS.DEFAULTS.NEW_STATUS);
      setIsAddingStatus(false);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.STATUS_ADDED);

      // Refresh page to ensure data consistency
      window.location.reload();
    } catch (error: any) {
      const errorMessage = handleAPIError(error, WORKSPACE_CONSTANTS.MESSAGES.STATUS_ADD_ERROR);
      toast.error(errorMessage);
    }
  };

  // Edit status
  const handleEditStatus = (status: Status) => {
    setStatusToEdit({ ...status });
  };

  // Update status
  const handleUpdateStatus = async () => {
    if (!statusToEdit) return;

    // Validate status
    const validation = validateStatus(statusToEdit);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      await updateStatus({
        id: statusToEdit.id!,
        updatedStatus: statusToEdit,
        workspaceId,
      }).unwrap();

      // Update local state
      setStatuses((prevStatuses) =>
        prevStatuses.map((status) =>
          status.id === statusToEdit.id ? statusToEdit : status
        )
      );

      setStatusToEdit(null);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.STATUS_UPDATED);
    } catch (error: any) {
      const errorMessage = handleAPIError(error, WORKSPACE_CONSTANTS.MESSAGES.STATUS_UPDATE_ERROR);
      toast.error(errorMessage);
    }
  };

  // Delete status
  const handleDeleteStatus = (status: Status) => {
    setStatusToDelete(status);
  };

  const confirmDeleteStatus = async () => {
    if (!statusToDelete) return;

    try {
      await deleteStatus({
        id: statusToDelete.id!,
        workspace_id: workspaceId,
      }).unwrap();

      // Update local state
      setStatuses((prevStatuses) =>
        prevStatuses.filter((status) => status.id !== statusToDelete.id)
      );

      setStatusToDelete(null);
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.STATUS_DELETED);
    } catch (error: any) {
      const errorMessage = handleAPIError(error, WORKSPACE_CONSTANTS.MESSAGES.STATUS_DELETE_ERROR);
      toast.error(errorMessage);
    }
  };

  const cancelDeleteStatus = () => {
    setStatusToDelete(null);
  };

  return {
    // State
    statuses,
    newStatus,
    setNewStatus,
    isAddingStatus,
    setIsAddingStatus,
    statusToEdit,
    setStatusToEdit,
    statusToDelete,
    setStatusToDelete,

    // Loading states
    isAddingStat,
    isUpdatingStatus,
    isDeletingStatus,

    // Actions
    handleAddStatus,
    handleEditStatus,
    handleUpdateStatus,
    handleDeleteStatus,
    confirmDeleteStatus,
    cancelDeleteStatus,
  };
}
