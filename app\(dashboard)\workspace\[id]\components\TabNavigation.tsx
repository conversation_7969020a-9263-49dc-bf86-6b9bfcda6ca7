"use client";

import React from 'react';
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { TabButtonProps } from '../types/workspace';
import { TAB_CONFIG } from '../constants/workspace';

interface TabNavigationProps {
  activeTab: string;
  onTabClick: (id: string) => void;
}

function TabButton({ id, icon: Icon, label, activeTab, onTabClick }: TabButtonProps) {
  const isActive = activeTab === id;
  
  return (
    <Button
      variant={isActive ? "default" : "outline"}
      size="sm"
      onClick={() => onTabClick(id)}
      className={cn(
        "flex items-center gap-1.5 text-xs md:text-sm transition-all duration-200",
        "hover:scale-105 active:scale-95",
        isActive
          ? "bg-primary text-primary-foreground shadow-md"
          : "bg-background hover:bg-muted border-muted-foreground/20"
      )}
    >
      <Icon className="w-3 h-3 md:w-4 md:h-4" />
      <span className="hidden sm:inline">{label}</span>
    </Button>
  );
}

export function TabNavigation({ activeTab, onTabClick }: TabNavigationProps) {
  return (
    <div className="grid grid-cols-3 text-[15px] md:flex md:flex-row md:gap-2 gap-1 overflow-x-auto">
      {TAB_CONFIG.map((tab) => (
        <TabButton
          key={tab.id}
          id={tab.id}
          icon={tab.icon}
          label={tab.label}
          activeTab={activeTab}
          onTabClick={onTabClick}
        />
      ))}
    </div>
  );
}
