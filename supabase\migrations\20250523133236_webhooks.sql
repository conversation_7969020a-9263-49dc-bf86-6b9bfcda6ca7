-- Migration for table: webhooks
CREATE TABLE IF NOT EXISTS "webhooks" (
  "id" uuid PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "user_id" uuid NOT NULL,
  "webhook_url" text,
  "status" boolean,
  "name" text,
  "description" text,
  "type" text,
  "workspace_id" integer
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "webhooks_user_id_idx" ON "webhooks" ("user_id");

-- Create index on workspace_id
CREATE INDEX IF NOT EXISTS "webhooks_workspace_id_idx" ON "webhooks" ("workspace_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "webhooks" ADD CONSTRAINT "fk_webhooks_user_id"
--   FOREIGN KEY ("user_id") REFERENCES "auth.users" (id);

-- Add foreign key constraint for workspace_id (commented out, uncomment after verifying)
-- ALTE<PERSON> TABLE "webhooks" ADD CONSTRAINT "fk_webhooks_workspace_id"
--   FOREIGN KEY ("workspace_id") REFERENCES "workspaces" (id);

-- Enable Row Level Security
ALTER TABLE "webhooks" ENABLE ROW LEVEL SECURITY;

