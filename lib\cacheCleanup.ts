import { clearCachePattern, closeRedisConnection } from './redis';

// Define cache patterns for different data types
const CACHE_PATTERNS = {
  LEADS: 'leads:*',
  WORKSPACES: 'workspace:*',
  FORMS: 'forms:*',
  MARKETING: 'marketing:*',
  WEBHOOKS: 'webhooks:*',
  MEMBERS: 'members:*',
  ALL: '*'
};

// Function to clean up specific cache types
export const cleanupCache = async (pattern: string = CACHE_PATTERNS.ALL): Promise<boolean> => {
  try {
    return await clearCachePattern(pattern);
  } catch (error) {
    console.error('Error cleaning up cache:', error);
    return false;
  }
};

// Function to perform periodic cache cleanup
export const setupPeriodicCacheCleanup = (intervalMinutes: number = 720): NodeJS.Timeout => {
  // Default to 12 hours (720 minutes)
  const interval = intervalMinutes * 60 * 1000;
  
  console.log(`Setting up periodic cache cleanup every ${intervalMinutes} minutes`);
  
  return setInterval(async () => {
    console.log('Running periodic cache cleanup...');
    await cleanupCache();
    console.log('Periodic cache cleanup completed');
  }, interval);
};

// Function to clean up resources before server shutdown
export const cleanupResources = async (): Promise<void> => {
  try {
    console.log('Cleaning up resources before shutdown...');
    
    // Clear all Redis cache
    await cleanupCache();
    
    // Close Redis connection
    await closeRedisConnection();
    
    console.log('Resource cleanup completed');
  } catch (error) {
    console.error('Error during resource cleanup:', error);
  }
};

export { CACHE_PATTERNS };
