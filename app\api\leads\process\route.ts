import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { processStructuredData, processWithAI } from "../utils/processing";
import { applyCors } from "../utils/middleware";

/**
 * Process lead data using AI or structured methods
 * @route POST /api/leads/process
 */
export async function POST(request: NextRequest) {
  // Apply CORS middleware
  await applyCors(request);
  
  try {
    // Parse request body
    const body = await request.json();
    
    if (!body) {
      return NextResponse.json({ 
        success: false, 
        error: "Request body is required" 
      }, { status: 400 });
    }
    
    // Get user metadata for API keys if available
    let userMetadata = null;
    const authHeader = request.headers.get("authorization");
    
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.split(" ")[1];
      const { data: { user }, error } = await supabase.auth.getUser(token);
      
      if (!error && user) {
        userMetadata = user.user_metadata;
      }
    }
    
    // Extract API keys from user metadata
    const togetherApiKey = userMetadata?.togetherAPI || null;
    
    // TIERED APPROACH: Try simpler methods first, then fall back to AI
    
    // Step 1: Try to process as structured data first (cheapest)
    let processedData = processStructuredData(body);
    
    // Step 2: If that fails, use AI (more expensive)
    if (!processedData) {
      console.log("Structured processing failed, using AI processing");
      processedData = await processWithAI(body, togetherApiKey);
    }
    
    return NextResponse.json({ 
      success: true, 
      data: processedData 
    }, { status: 200 });
  } catch (error) {
    console.error("Error processing lead data:", error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}
