export interface WorkspaceMember {
  id?: string;
  email: string;
  role: string;
  status: "active" | "pending";
  profileImage?: string;
  name?: string;
}

export interface Status {
  id?: string;
  name: string;
  color: string;
  count_statistics?: boolean;
  countInStatistics?: any;
  workspace_show: boolean;
}

export interface Tags {
  id?: string;
  name: string;
  color: string;
}

export interface WorkspaceSettings {
  name: string;
  industry: string;
  company_size: string;
  timezone: string;
  notifications: {
    email: boolean;
    sms: boolean;
    inApp: boolean;
  };
  security: {
    twoFactor: boolean;
    ipRestriction: boolean;
  };
}

export interface NewStatus {
  name: string;
  color: string;
  countInStatistics: boolean;
  showInWorkspace: boolean;
  count_statistics: boolean;
}

export interface NewTags {
  name: string;
  color: string;
}

export interface TabButtonProps {
  id: string;
  icon: any;
  label: string;
  activeTab: string;
  onTabClick: (id: string) => void;
}

export interface StatusFormProps {
  status: Status | NewStatus;
  onSubmit: (status: Status | NewStatus) => void;
}

export interface TagsFormProps {
  tags: Tags | NewTags;
  onSubmit: (tags: Tags | NewTags) => void;
}

export interface WorkspaceSettingsPageProps {
  isCollapsed: boolean;
}

export interface GeneralSettingsProps {
  settings: WorkspaceSettings;
  setSettings: (settings: WorkspaceSettings) => void;
  isEditMode: boolean;
  isSaving: boolean;
  onEditClick: () => void;
  onCancelEdit: () => void;
  onSave: () => void;
}

export interface MembersTabProps {
  members: WorkspaceMember[];
  isLoadingMembers: boolean;
  onMemberAdd: (member: WorkspaceMember) => void;
  onMemberDelete: (memberId: string) => void;
  onMemberUpdate: (member: WorkspaceMember) => void;
  onInviteResend: (member: WorkspaceMember) => void;
  isAdding: boolean;
  isDeleting: boolean;
  isResending: boolean;
  workspaceId: string;
}

export interface StatusManagementProps {
  statuses: Status[];
  isLoadingStatus: boolean;
  onAddStatus: () => void;
  onEditStatus: (status: Status) => void;
  onDeleteStatus: (status: Status) => void;
  isAddingStatus: boolean;
  setIsAddingStatus: (adding: boolean) => void;
  newStatus: NewStatus;
  setNewStatus: (status: NewStatus) => void;
}

export interface TagsManagementProps {
  tags: Tags[];
  isLoadingTags: boolean;
  onAddTags: () => void;
  onEditTags: (tags: Tags) => void;
  onDeleteTags: (tags: Tags) => void;
  isAddingTags: boolean;
  setIsAddingTags: (adding: boolean) => void;
  newTags: NewTags;
  setNewTags: (tags: NewTags) => void;
}

export interface NotificationsTabProps {
  settings: WorkspaceSettings;
  setSettings: (settings: WorkspaceSettings) => void;
  isEditMode: boolean;
}

export interface SecurityTabProps {
  settings: WorkspaceSettings;
  setSettings: (settings: WorkspaceSettings) => void;
  isEditMode: boolean;
}

export interface StatusDialogsProps {
  isAddingStatus: boolean;
  setIsAddingStatus: (adding: boolean) => void;
  statusToEdit: Status | null;
  setStatusToEdit: (status: Status | null) => void;
  statusToDelete: Status | null;
  setStatusToDelete: (status: Status | null) => void;
  newStatus: NewStatus;
  setNewStatus: (status: NewStatus) => void;
  onAddStatus: () => void;
  onUpdateStatus: () => void;
  onConfirmDeleteStatus: () => void;
  isAddingStat: boolean;
}

export interface TagsDialogsProps {
  isAddingTags: boolean;
  setIsAddingTags: (adding: boolean) => void;
  tagsToEdit: Tags | null;
  setTagsToEdit: (tags: Tags | null) => void;
  tagsToDelete: Tags | null;
  setTagsToDelete: (tags: Tags | null) => void;
  newTags: NewTags;
  setNewTags: (tags: NewTags) => void;
  onAddTags: () => void;
  onUpdateTags: () => void;
  onConfirmDeleteTags: () => void;
  isAddingTag: boolean;
}

export type TabId = "general" | "members" | "notifications" | "security" | "status" | "tags";

export interface WorkspaceData {
  id: string;
  name: string;
  industry?: string;
  company_size?: string;
  timezone?: string;
  notifications?: {
    email: boolean;
    sms: boolean;
    inApp: boolean;
  };
  security?: {
    twoFactor: boolean;
    ipRestriction: boolean;
  };
}
