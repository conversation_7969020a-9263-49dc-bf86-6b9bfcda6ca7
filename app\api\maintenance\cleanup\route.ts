import { NextRequest, NextResponse } from "next/server";
import { runAllCleanupOperations } from "@/lib/cleanup";
import { cleanupInactiveSubscriptions } from "@/lib/supabaseServer";
import { authenticateMaintenanceRequest } from "../utils/auth";

/**
 * Run cleanup operations
 * @route POST /api/maintenance/cleanup
 */
export async function POST(request: NextRequest) {
  // Authenticate the request
  const auth = authenticateMaintenanceRequest(request);
  if (!auth.isAuthorized) {
    return NextResponse.json({ error: auth.error }, { status: auth.status });
  }
  
  try {
    // Get the operation type from the query
    const searchParams = request.nextUrl.searchParams;
    const operation = searchParams.get("operation");
    let result = false;
    
    // Perform the requested operation
    switch (operation) {
      case "all":
        // Run all cleanup operations
        result = await runAllCleanupOperations();
        break;
        
      case "subscriptions":
        // Clean up inactive subscriptions
        cleanupInactiveSubscriptions(15 * 60 * 1000); // 15 minutes
        result = true;
        break;
        
      default:
        // Default to running all cleanup operations
        result = await runAllCleanupOperations();
    }
    
    // Return success response
    return NextResponse.json({
      success: result,
      timestamp: new Date().toISOString(),
      operation: operation || "all"
    }, { status: 200 });
  } catch (error) {
    console.error("Error during maintenance operation:", error);
    return NextResponse.json({
      error: "Internal server error",
      message: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
