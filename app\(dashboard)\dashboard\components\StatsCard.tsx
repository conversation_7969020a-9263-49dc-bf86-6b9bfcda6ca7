import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { HelpCircle, TrendingUp, TrendingDown, DollarSign, Users, Target } from "lucide-react";
import { DashboardStat } from "../types/dashboard";

interface StatsCardProps {
  stat: DashboardStat;
  tooltipMessage?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({ stat, tooltipMessage }) => {
  const getIcon = () => {
    switch (stat.icon) {
      case "DollarSign":
        return <DollarSign className="h-4 w-4 text-green-600" />;
      case "Target":
        return <Target className="h-4 w-4 text-blue-600" />;
      case "Users":
        return <Users className="h-4 w-4 text-purple-600" />;
      case "TrendingUp":
        return <TrendingUp className="h-4 w-4 text-orange-600" />;
      default:
        return <DollarSign className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTrendIcon = () => {
    switch (stat.trend) {
      case "up":
        return <TrendingUp className="h-3 w-3 text-green-500" />;
      case "down":
        return <TrendingDown className="h-3 w-3 text-red-500" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (stat.trend) {
      case "up":
        return "text-green-600 dark:text-green-400";
      case "down":
        return "text-red-600 dark:text-red-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  return (
    <Card className="overflow-hidden border border-gray-200 dark:border-gray-800 rounded-lg hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-full">
            {getIcon()}
          </div>
          <div className="flex flex-col space-y-1 flex-1">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {stat.title}
              </p>
              {tooltipMessage && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-3 w-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-xs max-w-xs">{tooltipMessage}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <div className="flex items-baseline space-x-2">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                {stat.value}
              </h3>
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon()}
              <span className={`text-xs font-medium ${getTrendColor()}`}>
                {stat.change}
              </span>
              {stat.note && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {stat.note}
                </span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
