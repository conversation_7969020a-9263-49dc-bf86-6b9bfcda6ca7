import React from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info, Package, Terminal } from "lucide-react";
import { CodeBlock } from "./CodeBlock";
import { CODE_EXAMPLES } from "../constants/documentation";

interface InstallationTabProps {
  copied: string | null;
  onCopy: (id: string, text: string) => void;
}

export const InstallationTab: React.FC<InstallationTabProps> = ({ copied, onCopy }) => {
  return (
    <div className="space-y-6">
      {/* Prerequisites */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-gradient-primary">
            <Package className="mr-2 h-5 w-5" />
            Prerequisites
          </CardTitle>
          <CardDescription>
            Requirements before installing the Lead Source Manager
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">System Requirements</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                <li>Node.js 16.x or higher</li>
                <li>React 18.x or higher</li>
                <li>TypeScript 4.9.x or higher (recommended)</li>
                <li>Tailwind CSS 3.x</li>
              </ul>
            </div>
            
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                This component is designed to work with Next.js 13+ App Router and requires 
                server-side API endpoints for full functionality.
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>

      {/* Installation Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-gradient-accent">
            <Terminal className="mr-2 h-5 w-5" />
            Installation Steps
          </CardTitle>
          <CardDescription>
            Step-by-step guide to install and configure the component
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Step 1: Install Dependencies */}
          <div>
            <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">
              Step 1: Install Dependencies
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Install the required packages using your preferred package manager:
            </p>
            <CodeBlock
              code={CODE_EXAMPLES.DEPENDENCIES}
              language="bash"
              title="NPM Installation"
              copyId="dependencies"
              copied={copied}
              onCopy={onCopy}
            />
          </div>

          {/* Step 2: Import Component */}
          <div>
            <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">
              Step 2: Import the Component
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Import the LeadSourceManager component in your React application:
            </p>
            <CodeBlock
              code={CODE_EXAMPLES.IMPORT}
              language="typescript"
              title="Component Import"
              copyId="import"
              copied={copied}
              onCopy={onCopy}
            />
          </div>

          {/* Step 3: Basic Usage */}
          <div>
            <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">
              Step 3: Basic Usage
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Use the component in your page or layout:
            </p>
            <CodeBlock
              code={CODE_EXAMPLES.USAGE}
              language="typescript"
              title="Basic Usage Example"
              copyId="usage"
              copied={copied}
              onCopy={onCopy}
            />
          </div>

          {/* Step 4: Configuration */}
          <div>
            <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">
              Step 4: Environment Configuration
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Configure your environment variables for webhook URL generation:
            </p>
            <CodeBlock
              code={`# .env.local
NEXT_PUBLIC_BASE_URL=https://your-domain.com
DATABASE_URL=your_database_connection_string
NEXTAUTH_SECRET=your_nextauth_secret`}
              language="bash"
              title="Environment Variables"
              copyId="env-config"
              copied={copied}
              onCopy={onCopy}
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Setup */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gradient-purple">Additional Setup</CardTitle>
          <CardDescription>
            Optional configurations for enhanced functionality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">
              Tailwind CSS Configuration
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Ensure your Tailwind CSS configuration includes the component paths:
            </p>
            <CodeBlock
              code={`// tailwind.config.js
module.exports = {
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    // Add other paths as needed
  ],
  theme: {
    extend: {
      // Your custom theme extensions
    },
  },
  plugins: [],
}`}
              language="javascript"
              title="Tailwind Configuration"
              copyId="tailwind-config"
              copied={copied}
              onCopy={onCopy}
            />
          </div>

          <div>
            <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">
              TypeScript Configuration
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              For TypeScript projects, ensure proper type definitions:
            </p>
            <CodeBlock
              code={`// types/global.d.ts
export interface Source {
  id: string;
  name: string;
  type: string;
  status: boolean;
  webhook_url?: string;
  description?: string;
  workspace_id?: string | null;
  created_at?: string;
  user_id?: string;
}`}
              language="typescript"
              title="Type Definitions"
              copyId="type-definitions"
              copied={copied}
              onCopy={onCopy}
            />
          </div>
        </CardContent>
      </Card>

      {/* Troubleshooting */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gradient-teal">Troubleshooting</CardTitle>
          <CardDescription>
            Common issues and their solutions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2 text-gray-900 dark:text-white">
                Common Issues
              </h4>
              <div className="space-y-3">
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                    Module not found errors
                  </h5>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    Ensure all dependencies are installed and your import paths are correct.
                  </p>
                </div>
                
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Styling issues
                  </h5>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Verify that Tailwind CSS is properly configured and includes the component paths.
                  </p>
                </div>
                
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <h5 className="font-medium text-red-800 dark:text-red-200 mb-1">
                    API connection errors
                  </h5>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    Check your environment variables and ensure your API endpoints are properly configured.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
