import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Sign in a user
 * @route POST /api/auth/signin
 */
export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body = await request.json();
    const { email, password } = body;
    
    if (!email || !password) {
      return NextResponse.json({ 
        error: "Email and password are required" 
      }, { status: 400 });
    }
    
    // Sign in with Supabase
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      return NextResponse.json({ 
        error: error.message || AUTH_MESSAGES.SIGNIN_FAILED 
      }, { status: 401 });
    }
    
    return NextResponse.json({
      message: "Sign in successful",
      user: data.user,
      session: data.session,
    }, { status: 200 });
  } catch (error) {
    console.error("Error signing in:", error);
    return NextResponse.json({ 
      error: AUTH_MESSAGES.SIGNIN_FAILED 
    }, { status: 500 });
  }
}
