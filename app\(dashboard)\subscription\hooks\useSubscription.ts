import { useState, useEffect } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { useToast } from '@/components/ui/use-toast';
import { UserSubscription, PlanTier } from '../types/subscription';
import { calculateDaysRemaining } from '../utils/subscription';
import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

export function useSubscription() {
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [user, setUser] = useState<any>(null);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [daysRemaining, setDaysRemaining] = useState<number | null>(null);
  
  const supabase = createClientComponentClient();
  const { toast } = useToast();

  // Fetch current user and subscription data
  const fetchUserData = async () => {
    try {
      setIsDataLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        setUser(user);
        
        // Get subscription from user metadata
        const subscription = user.user_metadata?.subscription as UserSubscription;
        if (subscription) {
          setCurrentSubscription(subscription);
          
          // Calculate days remaining
          const days = calculateDaysRemaining(subscription);
          setDaysRemaining(days);
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      toast({
        title: "Error",
        description: SUBSCRIPTION_CONSTANTS.MESSAGES.LOAD_ERROR,
        variant: "destructive",
      });
    } finally {
      setIsDataLoading(false);
    }
  };

  // Update user subscription in Supabase
  const updateUserSubscription = async (subscription: UserSubscription) => {
    try {
      const { error } = await supabase.auth.updateUser({
        data: { subscription },
      });

      if (error) throw error;

      // Update local state
      setCurrentSubscription(subscription);
      
      // Recalculate days remaining
      const days = calculateDaysRemaining(subscription);
      setDaysRemaining(days);
      
    } catch (error) {
      console.error("Error updating subscription:", error);
      throw error;
    }
  };

  // Get days remaining with hydration safety
  const getDaysRemaining = () => {
    if (!currentSubscription?.currentPeriodEnd) return 0;

    // Use the pre-calculated value if available to avoid hydration mismatch
    if (daysRemaining !== null) return daysRemaining;

    return calculateDaysRemaining(currentSubscription);
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  return {
    currentSubscription,
    user,
    isDataLoading,
    daysRemaining: getDaysRemaining(),
    updateUserSubscription,
    refetchUserData: fetchUserData,
  };
}
