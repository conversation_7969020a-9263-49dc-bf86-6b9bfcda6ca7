"use client";

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/lib/store/hooks';
import { logout, setCredentials } from '@/lib/store/slices/authSlice';
import { useLoginMutation, useSignupMutation } from '@/lib/store/services/authApi';
import { supabase } from '@/lib/supabaseClient';
import { toast } from 'sonner';

// Protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/leads',
  '/leads-sources',
  '/contact',
  '/integration',
  '/documentation',
  '/workspace',
  '/forms',
  '/marketing',
  '/analytics'
];

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/signup',
  '/forgot-password',
  '/reset-password'
];

// Routes that authenticated users shouldn't access
const AUTH_ROUTES = ['/login', '/signup'];

export function useAuth() {
  const dispatch = useAppDispatch();
  const { user, token, session } = useAppSelector((state) => state.auth);
  const [loading, setLoading] = useState(true);
  const [loginMutation] = useLoginMutation();
  const [signupMutation] = useSignupMutation();
  const router = useRouter();
  const pathname = usePathname();

  // Check if current route is protected
  const isProtectedRoute = pathname ? PROTECTED_ROUTES.some(route =>
    pathname.startsWith(route)
  ) : false;

  // Check if current route is auth route
  const isAuthRoute = pathname ? AUTH_ROUTES.includes(pathname) : false;

  // Check if current route is public
  const isPublicRoute = pathname ? (PUBLIC_ROUTES.includes(pathname) || pathname === '/') : true;

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setLoading(true);
        
        // Get initial session from Supabase
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          return;
        }

        if (session?.user) {
          // Update Redux store with session data
          const userData = {
            id: session.user.id,
            email: session.user.email || '',
            role: (session.user.user_metadata?.role || 'admin') as 'admin' | 'sales_agent' | 'manager'
          };

          dispatch(setCredentials({
            token: session.access_token,
            user: userData
          }));

          // Handle redirections for authenticated users
          if (isAuthRoute) {
            router.replace('/dashboard');
            return;
          }
        } else {
          // User is not authenticated
          if (isProtectedRoute && pathname) {
            // Redirect to login with return URL
            const returnUrl = encodeURIComponent(pathname);
            router.replace(`/login?returnUrl=${returnUrl}`);
            return;
          }
        }
      } catch (error) {
        console.error('Error in initializeAuth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          const userData = {
            id: session.user.id,
            email: session.user.email || '',
            role: (session.user.user_metadata?.role || 'admin') as 'admin' | 'sales_agent' | 'manager'
          };

          dispatch(setCredentials({
            token: session.access_token,
            user: userData
          }));

          // Handle successful sign in
          const urlParams = new URLSearchParams(window.location.search);
          const returnUrl = urlParams.get('returnUrl');
          
          if (returnUrl && returnUrl !== '/login' && returnUrl !== '/signup') {
            router.replace(decodeURIComponent(returnUrl));
          } else {
            router.replace('/dashboard');
          }
          
          toast.success('Successfully signed in!');
        } else if (event === 'SIGNED_OUT') {
          dispatch(logout());
          router.replace('/login');
          toast.success('Successfully signed out!');
        }
        
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [dispatch, router, pathname, isProtectedRoute, isAuthRoute]);

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, metadata?: any) => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/dashboard`,
          data: metadata,
        },
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        toast.error('Error signing out');
        console.error('Error signing out:', error);
      }
    } catch (error) {
      console.error('Error in signOut:', error);
      toast.error('Error signing out');
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    token,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    isAuthenticated: !!user,
    isProtectedRoute,
    isAuthRoute,
    isPublicRoute,
  };
}
