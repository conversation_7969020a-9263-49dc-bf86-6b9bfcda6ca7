import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, notify<PERSON><PERSON><PERSON><PERSON><PERSON>, validateEmail, validatePhoneNumber } from "../utils";

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    
    if (!body || !body.name || !body.email) {
      return NextResponse.json({ error: "Name and Email are required" }, { status: 400 });
    }
    
    // Check for existing leads with the same email
    const { data: existingLeads, error: fetchError } = await supabase
      .from("leads")
      .select("created_at")
      .eq("email", body.email)
      .order("created_at", { ascending: false })
      .limit(1);
    
    if (fetchError) {
      return NextResponse.json({ error: "Error checking existing leads" }, { status: 500 });
    }
    
    if (existingLeads.length > 0) {
      const existingLead = existingLeads[0];
      const createdAt = new Date(existingLead.created_at);
      const now = new Date();
      
      if (!isNaN(createdAt.getTime())) {
        const timeDifference = (now.getTime() - createdAt.getTime()) / (1000 * 60);
        
        if (timeDifference < 60) {
          return NextResponse.json({
            error: "A lead with this email was created less than an hour ago."
          }, { status: 400 });
        }
      }
    }
    
    // Validate email and phone
    const isValidEmail = await validateEmail(body.email);
    const isValidPhone = await validatePhoneNumber(body.phone);
    
    // Insert lead into database
    const { data, error } = await supabase.from("leads").insert([
      {
        name: body.name,
        email: body.email,
        phone: body.phone || null,
        status: {
          name: "Arrived",
          color: "#FFA500",
        },
        company: body.company || null,
        position: body.position || null,
        contact_method: body.contact_method || "Call",
        assign_to: null,
        lead_source_id: body.source_id || null,
        work_id: workspaceId,
        user_id: user.id,
        text_area: body.text_area || "",
        is_email_valid: isValidEmail,
        is_phone_valid: isValidPhone,
        created_at: new Date().toISOString(),
      },
    ]).select();
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    // Create notification
    await notifyLeadChange(
      body.name,
      "created",
      user.id,
      workspaceId,
      { 
        lead_name: body.name,
        lead_email: body.email
      }
    );
    
    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating lead:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
