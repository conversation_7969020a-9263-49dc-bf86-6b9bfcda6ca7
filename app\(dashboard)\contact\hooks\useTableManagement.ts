import { useState, useCallback } from 'react';
import { CONTACT_CONSTANTS } from '../constants/contact';
import { getAvailableColumns } from '../utils/contactUtils';

export function useTableManagement() {
  // Column management
  const [selectedHeaders, setSelectedHeaders] = useState<string[]>(
    CONTACT_CONSTANTS.DEFAULT_SELECTED_HEADERS
  );
  
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>(
    CONTACT_CONSTANTS.DEFAULT_COLUMN_WIDTHS
  );

  // Dropdown states
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [dropdownOpenRemove, setDropdownOpenRemove] = useState<string | null>(null);

  // Column operations
  const addColumn = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedColumn = event.target.value;
    if (selectedColumn && !selectedHeaders.includes(selectedColumn)) {
      const updatedHeaders = [...selectedHeaders];
      
      // Find the correct index in the original tableHeaders
      const insertIndex = CONTACT_CONSTANTS.TABLE_HEADERS.indexOf(selectedColumn);
      
      // Find the correct position in selectedHeaders based on tableHeaders order
      for (let i = 0; i < selectedHeaders.length; i++) {
        if (CONTACT_CONSTANTS.TABLE_HEADERS.indexOf(selectedHeaders[i]) > insertIndex) {
          updatedHeaders.splice(i, 0, selectedColumn);
          setSelectedHeaders(updatedHeaders);
          return;
        }
      }
      
      // If it's the last item, push normally
      updatedHeaders.push(selectedColumn);
      setSelectedHeaders(updatedHeaders);
    }
  }, [selectedHeaders]);

  const removeColumn = useCallback((header: string) => {
    setSelectedHeaders(prevHeaders =>
      prevHeaders.filter(h => h !== header)
    );
    setDropdownOpenRemove(null);
  }, []);

  // Column resizing
  const handleResize = useCallback((header: string) => 
    (event: React.SyntheticEvent, { size }: { size: { width: number; height: number } }) => {
      setColumnWidths(prevWidths => ({
        ...prevWidths,
        [header]: size.width,
      }));
    }, []
  );

  // Dropdown management
  const toggleDropdown = useCallback((header: string) => {
    setDropdownOpenRemove(prev => prev === header ? null : header);
  }, []);

  const toggleAddColumnDropdown = useCallback(() => {
    setDropdownOpen(prev => !prev);
  }, []);

  // Reset to defaults
  const resetColumns = useCallback(() => {
    setSelectedHeaders(CONTACT_CONSTANTS.DEFAULT_SELECTED_HEADERS);
    setColumnWidths(CONTACT_CONSTANTS.DEFAULT_COLUMN_WIDTHS);
  }, []);

  // Get available columns for adding
  const availableColumns = getAvailableColumns(selectedHeaders);

  // Check if column is visible
  const isColumnVisible = useCallback((header: string) => {
    return selectedHeaders.includes(header);
  }, [selectedHeaders]);

  // Get column width
  const getColumnWidth = useCallback((header: string) => {
    return columnWidths[header] || CONTACT_CONSTANTS.DEFAULT_COLUMN_WIDTHS[header as keyof typeof CONTACT_CONSTANTS.DEFAULT_COLUMN_WIDTHS] || 150;
  }, [columnWidths]);

  // Column configuration
  const getColumnConfig = useCallback(() => {
    return selectedHeaders.map(header => ({
      key: header.toLowerCase().replace(/\s+/g, '_'),
      label: header,
      width: getColumnWidth(header),
      visible: true,
      sortable: ['Name', 'Email', 'Phone'].includes(header),
      editable: !['Platform'].includes(header),
    }));
  }, [selectedHeaders, getColumnWidth]);

  return {
    // State
    selectedHeaders,
    columnWidths,
    dropdownOpen,
    dropdownOpenRemove,
    availableColumns,

    // Actions
    addColumn,
    removeColumn,
    handleResize,
    toggleDropdown,
    toggleAddColumnDropdown,
    resetColumns,
    isColumnVisible,
    getColumnWidth,
    getColumnConfig,

    // Setters
    setSelectedHeaders,
    setColumnWidths,
    setDropdownOpen,
    setDropdownOpenRemove,
  };
}
