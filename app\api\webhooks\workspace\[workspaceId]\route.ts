import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../../utils/auth";
import { calculateWebhookMetrics } from "../../utils/metrics";

/**
 * Get webhooks for a workspace
 * @route GET /api/webhooks/workspace/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const workspaceId = params.workspaceId;

    // Fetch webhooks for the workspace
    const { data: webhooks, error: webhookError } = await supabase
      .from("webhooks")
      .select("*")
      .eq("workspace_id", workspaceId);

    if (webhookError) {
      return NextResponse.json({ error: webhookError.message }, { status: 400 });
    }

    // Calculate metrics for the webhooks
    const webhooksWithMetrics = await calculateWebhookMetrics(webhooks, workspaceId);

    return NextResponse.json({ data: webhooksWithMetrics }, { status: 200 });
  } catch (error) {
    console.error("Error fetching workspace webhooks:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
