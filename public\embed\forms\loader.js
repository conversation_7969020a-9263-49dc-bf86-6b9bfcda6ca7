/**
 * CRM Form Loader Script
 * This script dynamically loads forms from the CRM system
 */
(function() {
  // Store the current script URL for loading resources
  const scriptUrl = document.currentScript.src;
  const baseUrl = scriptUrl.substring(0, scriptUrl.lastIndexOf('/'));
  const origin = baseUrl.substring(0, baseUrl.lastIndexOf('/embed'));
  
  // Default configuration
  const defaultConfig = {
    width: '100%',
    height: 500,
    responsive: true,
    showBorder: false
  };
  
  // Initialize the form loader
  function init(config) {
    if (!config.formId) {
      console.error('CRM Form Loader: No form ID provided');
      return;
    }
    
    const container = config.container ? 
      document.getElementById(config.container) : 
      null;
      
    if (!container && config.container) {
      console.error(`CRM Form Loader: Container element "${config.container}" not found`);
      return;
    }
    
    // Merge default config with provided config
    const formConfig = { ...defaultConfig, ...config };
    
    // Create and insert the iframe
    createFormIframe(formConfig, container);
  }
  
  // Create and insert the form iframe
  function createFormIframe(config, container) {
    const iframe = document.createElement('iframe');
    
    // Set iframe attributes
    iframe.src = `${origin}/embed/forms/${config.formId}`;
    iframe.title = 'CRM Form';
    iframe.setAttribute('loading', 'lazy');
    iframe.setAttribute('allowtransparency', 'true');
    
    // Set iframe styles
    if (config.responsive) {
      iframe.style.width = '100%';
      iframe.style.maxWidth = config.width;
    } else {
      iframe.width = config.width;
    }
    
    iframe.height = config.height;
    
    if (!config.showBorder) {
      iframe.style.border = 'none';
      iframe.setAttribute('frameborder', '0');
    }
    
    // Insert the iframe into the container or create a new container
    if (container) {
      container.appendChild(iframe);
    } else {
      // Find all div elements with the data-crm-form attribute
      const formDivs = document.querySelectorAll(`div[data-crm-form="${config.formId}"]`);
      
      formDivs.forEach(div => {
        // Get configuration from data attributes
        const divConfig = {
          width: div.getAttribute('data-width') || config.width,
          height: parseInt(div.getAttribute('data-height') || config.height),
          responsive: div.getAttribute('data-responsive') === 'true',
          showBorder: div.getAttribute('data-show-border') === 'true'
        };
        
        // Create a clone of the iframe with the div's configuration
        const divIframe = iframe.cloneNode(true);
        
        if (divConfig.responsive) {
          divIframe.style.width = '100%';
          divIframe.style.maxWidth = divConfig.width;
        } else {
          divIframe.width = divConfig.width;
        }
        
        divIframe.height = divConfig.height;
        
        if (!divConfig.showBorder) {
          divIframe.style.border = 'none';
          divIframe.setAttribute('frameborder', '0');
        }
        
        // Replace the div with the iframe
        div.parentNode.replaceChild(divIframe, div);
      });
    }
  }
  
  // Auto-initialize forms from div elements
  function autoInitialize() {
    // Find all div elements with the data-crm-form attribute
    const formDivs = document.querySelectorAll('div[data-crm-form]');
    
    formDivs.forEach(div => {
      const formId = div.getAttribute('data-crm-form');
      
      if (formId) {
        // Get configuration from data attributes
        const config = {
          formId: formId,
          width: div.getAttribute('data-width') || defaultConfig.width,
          height: parseInt(div.getAttribute('data-height') || defaultConfig.height),
          responsive: div.getAttribute('data-responsive') === 'true',
          showBorder: div.getAttribute('data-show-border') === 'true'
        };
        
        // Create and insert the iframe
        createFormIframe(config, null);
      }
    });
  }
  
  // Check if the script was loaded via the CRM-Form-Widget method
  if (window['CRM-Form-Widget']) {
    // Get the queue of commands
    const queue = window[window['CRM-Form-Widget']].q || [];
    
    // Process the queue
    for (let i = 0; i < queue.length; i++) {
      const args = queue[i];
      const command = args[0];
      const params = args.slice(1);
      
      if (command === 'init') {
        init(params[0]);
      }
    }
    
    // Replace the queue with direct function calls
    window[window['CRM-Form-Widget']] = function(command) {
      if (command === 'init') {
        init(arguments[1]);
      }
    };
  } else {
    // Auto-initialize forms from div elements
    autoInitialize();
  }
  
  // Run auto-initialization when the DOM is fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', autoInitialize);
  } else {
    autoInitialize();
  }
})();
