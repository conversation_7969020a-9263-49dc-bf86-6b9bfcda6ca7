"use client";

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabaseClient';
import { toast } from 'sonner';
import { CRM_MESSAGES } from '@/lib/constant/crm';
import { ProfileDetails, ProfileFormData } from '../types/profile';
import { DEFAULT_PROFILE_DATA } from '../constants/profile';
import { transformUserMetadataToProfile, transformFormDataToProfile } from '../utils/profile';

export const useProfile = () => {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any | null>(null);
  const [profileData, setProfileData] = useState<ProfileDetails>(DEFAULT_PROFILE_DATA);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) throw error;

      if (user) {
        setUser(user);
        const transformedProfile = transformUserMetadataToProfile(user);
        setProfileData(transformedProfile);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
      toast.error("Failed to load profile data");
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (formData: ProfileFormData): Promise<void> => {
    try {
      setLoading(true);
      
      const { error } = await supabase.auth.updateUser({
        data: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone,
          dateOfBirth: formData.dateOfBirth,
          title: formData.title,
          department: formData.department,
          company: formData.company,
          startDate: formData.startDate,
          avatar: formData.avatar,
          linkedin: formData.linkedin,
          twitter: formData.twitter,
          github: formData.github,
          openAI: formData.openAI,
          togetherAPI: formData.togetherAPI,
          reoonEmail: formData.reoonEmail,
          bigDataCloud: formData.bigDataCloud,
        },
      });

      if (error) throw error;

      // Update local state
      const updatedProfile = transformFormDataToProfile(formData, profileData.personalInfo.email);
      setProfileData(updatedProfile);
      
      toast.success(CRM_MESSAGES.PROFILE_UPDATED);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserData();
  }, []);

  return {
    loading,
    user,
    profileData,
    updateProfile,
    refetchProfile: fetchUserData,
  };
};
