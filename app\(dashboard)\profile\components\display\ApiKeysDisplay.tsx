"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Key } from "lucide-react";
import { ProfileDetails } from '../../types/profile';
import { maskApi<PERSON>ey } from '../../utils/profile';

interface ApiKeysDisplayProps {
  profileData: ProfileDetails;
}

export function ApiKeysDisplay({ profileData }: ApiKeysDisplayProps) {
  const { apiKeys } = profileData;

  return (
    <Card className="md:col-span-3 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px] shadow-sm">
      <CardHeader className="p-3 sm:p-4 md:p-6 pb-2 sm:pb-3 md:pb-4">
        <CardTitle className="flex items-center gap-2 text-base sm:text-lg md:text-xl lg:text-2xl font-bold">
          <Key className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-primary" />
          API Keys
        </CardTitle>
      </CardHeader>
      <CardContent className="p-3 sm:p-4 md:p-6 pt-0 space-y-3 sm:space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">OpenAI API Key</p>
            <p className="text-sm sm:text-base font-mono bg-gray-50 dark:bg-gray-900 px-2 py-1 rounded border text-foreground break-all">
              {maskApiKey(apiKeys?.openAI)}
            </p>
          </div>
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Together API Key</p>
            <p className="text-sm sm:text-base font-mono bg-gray-50 dark:bg-gray-900 px-2 py-1 rounded border text-foreground break-all">
              {maskApiKey(apiKeys?.togetherAPI)}
            </p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Reoon Email API Key</p>
            <p className="text-sm sm:text-base font-mono bg-gray-50 dark:bg-gray-900 px-2 py-1 rounded border text-foreground break-all">
              {maskApiKey(apiKeys?.reoonEmail)}
            </p>
          </div>
          <div className="space-y-1 sm:space-y-2">
            <p className="text-xs sm:text-sm text-muted-foreground font-medium">Big Data Cloud API Key</p>
            <p className="text-sm sm:text-base font-mono bg-gray-50 dark:bg-gray-900 px-2 py-1 rounded border text-foreground break-all">
              {maskApiKey(apiKeys?.bigDataCloud)}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
