import { ProfileDetails, ProfileFormData } from '../types/profile';
import { API_KEY_DISPLAY_CONFIG } from '../constants/profile';

/**
 * Calculate years of experience from start date
 */
export const calculateYearsOfExperience = (startDate: string): string => {
  if (startDate === "N/A") return "N/A";
  
  try {
    const start = new Date(startDate);
    const today = new Date();
    const years = today.getFullYear() - start.getFullYear();
    return `${years} Years`;
  } catch {
    return "N/A";
  }
};

/**
 * Mask API key for display purposes
 */
export const maskApiKey = (apiKey?: string): string => {
  if (!apiKey) return API_KEY_DISPLAY_CONFIG.placeholder;
  
  if (apiKey.length <= API_KEY_DISPLAY_CONFIG.minLength) {
    return apiKey;
  }
  
  const prefix = apiKey.substring(0, API_KEY_DISPLAY_CONFIG.prefixLength);
  const suffix = apiKey.substring(apiKey.length - API_KEY_DISPLAY_CONFIG.suffixLength);
  return `${prefix}...${suffix}`;
};

/**
 * Transform user metadata to profile data
 */
export const transformUserMetadataToProfile = (user: any): ProfileDetails => {
  const metadata = user.user_metadata || {};
  
  return {
    personalInfo: {
      firstName: metadata.firstName || "N/A",
      lastName: metadata.lastName || "N/A",
      email: user.email || "N/A",
      phone: metadata.phone || "N/A",
      avatar: metadata.avatar || "/api/placeholder/200/200",
      dateOfBirth: metadata.dateOfBirth || "N/A",
    },
    professionalInfo: {
      title: metadata.title || "N/A",
      department: metadata.department || "N/A",
      company: metadata.company || "N/A",
      startDate: metadata.startDate || "N/A",
    },
    socialLinks: {
      linkedin: metadata.linkedin,
      twitter: metadata.twitter,
      github: metadata.github,
    },
    apiKeys: {
      openAI: metadata.openAI || "",
      togetherAPI: metadata.togetherAPI || "",
      reoonEmail: metadata.reoonEmail || "",
      bigDataCloud: metadata.bigDataCloud || "",
    },
  };
};

/**
 * Transform form data to profile data
 */
export const transformFormDataToProfile = (
  formData: ProfileFormData,
  currentEmail: string
): ProfileDetails => {
  return {
    personalInfo: {
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: currentEmail,
      phone: formData.phone,
      avatar: formData.avatar,
      dateOfBirth: formData.dateOfBirth,
    },
    professionalInfo: {
      title: formData.title,
      department: formData.department,
      company: formData.company,
      startDate: formData.startDate,
    },
    socialLinks: {
      linkedin: formData.linkedin,
      twitter: formData.twitter,
      github: formData.github,
    },
    apiKeys: {
      openAI: formData.openAI,
      togetherAPI: formData.togetherAPI,
      reoonEmail: formData.reoonEmail,
      bigDataCloud: formData.bigDataCloud,
    },
  };
};

/**
 * Generate file path for avatar upload
 */
export const generateAvatarFilePath = (fileName: string): string => {
  const fileExt = fileName.split(".").pop();
  const uniqueName = `${Math.random()}.${fileExt}`;
  return `avatars/${uniqueName}`;
};

/**
 * Extract file path from public URL
 */
export const extractFilePathFromUrl = (publicUrl: string): string => {
  return publicUrl.split("/").slice(-2).join("/");
};

/**
 * Get user initials for avatar fallback
 */
export const getUserInitials = (firstName: string, lastName: string): string => {
  const firstInitial = firstName && firstName !== "N/A" ? firstName[0] : "";
  const lastInitial = lastName && lastName !== "N/A" ? lastName[0] : "";
  return `${firstInitial}${lastInitial}`.toUpperCase() || "U";
};
