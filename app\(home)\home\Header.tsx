"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Zap, Menu, ChevronDown, BarChart3, Users, Target, MessageSquare, Shield } from "lucide-react";
import Link from "next/link";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useState, useEffect } from "react";
import { ThemeToggle } from "@/components/theme-toggle";

export const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const navItems = [
    {
      label: "Features",
      href: "#features",
      hasDropdown: true,
      dropdownItems: [
        { label: "Team Collaboration", href: "#features", icon: Users },
        { label: "Smart Communication", href: "#features", icon: MessageSquare },
        { label: "Data Security", href: "#features", icon: Shield },
        { label: "Analytics", href: "#features", icon: BarChart3 },
      ]
    },
    { label: "Pricing", href: "#pricing", hasDropdown: false },
    { label: "Testimonials", href: "#testimonials", hasDropdown: false },
    { label: "Login", href: "/login", hasDropdown: false },
  ];

  return (
    <header className={`sticky top-0 z-50 transition-all duration-300 ${
      scrolled
        ? 'bg-background/95 backdrop-blur-md supports-[backdrop-filter]:bg-background/80 shadow-md border-b border-border/30'
        : 'bg-background/50 backdrop-blur-sm supports-[backdrop-filter]:bg-background/30 border-b border-border/10'
    }`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="flex items-center gap-2 hover:opacity-90 transition-opacity">
            <div className="relative">
              <div className="absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full blur-sm opacity-70 group-hover:opacity-100 transition duration-200"></div>
              <div className="relative bg-background dark:bg-background rounded-full p-1">
                <Zap className="h-6 w-6 text-primary" />
              </div>
            </div>
            <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">InfiLabs</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            {navItems.map((item) => (
              item.hasDropdown ? (
                <DropdownMenu key={item.label}>
                  <DropdownMenuTrigger className="flex items-center gap-1 text-sm font-medium hover:text-primary transition-colors focus:outline-none">
                    {item.label} <ChevronDown className="h-4 w-4 opacity-70" />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="center" className="w-56 p-2">
                    {item.dropdownItems?.map((dropdownItem) => (
                      <DropdownMenuItem key={dropdownItem.label} asChild>
                        <a
                          href={dropdownItem.href}
                          className="flex items-center gap-2 py-2 px-3 cursor-pointer rounded-md hover:bg-primary/5 dark:hover:bg-primary/10"
                        >
                          <dropdownItem.icon className="h-4 w-4 text-primary" />
                          <span>{dropdownItem.label}</span>
                        </a>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <a
                  key={item.label}
                  href={item.href}
                  className="text-sm font-medium hover:text-primary transition-colors"
                >
                  {item.label}
                </a>
              )
            ))}
            <div className="pl-2">
              <ThemeToggle />
            </div>
            <Link href="/login">
              <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white border-0 shadow-md hover:shadow-lg transition-all duration-300">
                Get Started
              </Button>
            </Link>
          </nav>

          {/* Mobile Navigation */}
          <div className="flex items-center gap-4 md:hidden">
            <ThemeToggle />
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild className="md:hidden">
                <Button variant="ghost" size="icon" className="hover:bg-primary/5 dark:hover:bg-primary/10">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent className="border-l border-border/30">
                <div className="flex flex-col gap-6 mt-8">
                  <div className="flex items-center gap-2 mb-6">
                    <div className="relative">
                      <div className="absolute -inset-1 bg-gradient-to-r from-primary to-accent rounded-full blur-sm opacity-70"></div>
                      <div className="relative bg-background dark:bg-background rounded-full p-1">
                        <Zap className="h-6 w-6 text-primary" />
                      </div>
                    </div>
                    <span className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">InfiLabs</span>
                  </div>

                  {navItems.map((item) => (
                    <div key={item.label}>
                      {item.hasDropdown ? (
                        <div className="space-y-3">
                          <p className="text-sm font-semibold text-muted-foreground">{item.label}</p>
                          <div className="grid gap-2 pl-2">
                            {item.dropdownItems?.map((dropdownItem) => (
                              <a
                                key={dropdownItem.label}
                                href={dropdownItem.href}
                                className="flex items-center gap-2 text-base hover:text-primary transition-colors py-1"
                                onClick={() => setIsOpen(false)}
                              >
                                <dropdownItem.icon className="h-4 w-4 text-primary" />
                                <span>{dropdownItem.label}</span>
                              </a>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <a
                          href={item.href}
                          className="text-base font-medium hover:text-primary transition-colors"
                          onClick={() => setIsOpen(false)}
                        >
                          {item.label}
                        </a>
                      )}
                    </div>
                  ))}

                  <div className="flex items-center justify-between py-4 border-t border-b my-2 border-border/30">
                    <span className="text-sm font-medium">Toggle Theme</span>
                    <ThemeToggle />
                  </div>

                  <Link href="/login" onClick={() => setIsOpen(false)}>
                    <Button className="w-full bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white border-0">
                      Get Started
                    </Button>
                  </Link>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};