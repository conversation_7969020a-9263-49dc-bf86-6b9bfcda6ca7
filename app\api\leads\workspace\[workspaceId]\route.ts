import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, notifyLeadChange } from "../../utils";

export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    
    const workspaceId = params.workspaceId;
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Get pagination parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get("limit") || "12");
    const offset = parseInt(searchParams.get("offset") || "0");
    const sortBy = searchParams.get("sortBy") || "created_at";
    const sortOrder = searchParams.get("sortOrder") || "desc";
    
    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from("leads")
      .select("*", { count: "exact", head: true })
      .eq("work_id", workspaceId);
    
    if (countError) {
      return NextResponse.json({ error: countError.message }, { status: 400 });
    }
    
    // Get paginated leads
    const { data, error } = await supabase
      .from("leads")
      .select("*")
      .eq("work_id", workspaceId)
      .order(sortBy, { ascending: sortOrder === "asc" })
      .range(offset, offset + limit - 1);
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({
      data,
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > (offset + limit)
      }
    }, { status: 200 });
  } catch (error) {
    console.error("Error fetching workspace leads:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const workspaceId = params.workspaceId;
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    const { id } = body;
    
    if (!id || !Array.isArray(id) || id.length === 0) {
      return NextResponse.json({ error: "A list of Lead IDs is required" }, { status: 400 });
    }
    
    // Check if user is workspace owner
    const { data: workspaceData, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();
    
    if (workspaceError) {
      console.error("Workspace Check Error:", workspaceError.message);
      return NextResponse.json({ error: workspaceError.message }, { status: 400 });
    }
    
    const isWorkspaceOwner = workspaceData?.owner_id === user.id;
    
    // If not workspace owner, check if user is admin
    if (!isWorkspaceOwner) {
      const { data: memberData, error: memberError } = await supabase
        .from("workspace_members")
        .select("role")
        .eq("workspace_id", workspaceId)
        .eq("user_id", user.id)
        .single();
      
      if (memberError) {
        console.error("Workspace Member Check Error:", memberError.message);
        return NextResponse.json({ error: memberError.message }, { status: 400 });
      }
      
      const isAdmin = memberData?.role === "admin" || memberData?.role === "SuperAdmin";
      
      if (!isAdmin) {
        return NextResponse.json({
          error: "Only workspace owners and admins can delete leads"
        }, { status: 403 });
      }
    }
    
    // Get lead names for notification
    const { data: leadsToDelete, error: fetchError } = await supabase
      .from("leads")
      .select("id, name")
      .in("id", id)
      .eq("work_id", workspaceId);
    
    if (fetchError) {
      console.error("Fetch leads error:", fetchError.message);
      return NextResponse.json({ error: fetchError.message }, { status: 400 });
    }
    
    // Store lead names before deletion
    const leadNames = leadsToDelete ? leadsToDelete.map(lead => lead.name) : [];
    const leadIds = leadsToDelete ? leadsToDelete.map(lead => lead.id) : [];
    
    // Delete the leads
    const { data, error } = await supabase
      .from("leads")
      .delete()
      .in("id", id)
      .eq("work_id", workspaceId);
    
    if (error) {
      console.error("Supabase Delete Error:", error.message);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    // Create notification
    await notifyLeadChange(
      "multiple",
      "leads_deleted",
      user.id,
      workspaceId,
      {
        deleted_by: user.id,
        lead_count: leadIds.length,
        lead_names: leadNames,
        lead_ids: leadIds
      }
    );
    
    return NextResponse.json({
      message: "Leads deleted successfully",
      data
    }, { status: 200 });
  } catch (error) {
    console.error("Error deleting leads:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
