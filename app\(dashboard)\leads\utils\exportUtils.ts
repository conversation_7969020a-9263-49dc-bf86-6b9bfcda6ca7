import * as XLSX from "xlsx";
import { toast } from "sonner";
import { Lead, LeadSource } from "../types/leads";
import { formatLeadsForExport, normalizeImportedData } from "./leadUtils";
import { leadSchema } from "../constants/leads";

/**
 * Export leads to CSV format
 */
export const exportToCSV = (leads: Lead[], leadSources?: { data: LeadSource[] }) => {
  const formattedLeads = formatLeadsForExport(leads, leadSources);
  const worksheet = XLSX.utils.json_to_sheet(formattedLeads);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Leads");
  XLSX.writeFile(workbook, "leads_export.csv");
};

/**
 * Export leads to JSON format
 */
export const exportToJSON = (leads: Lead[]) => {
  const dataStr = JSON.stringify(leads, null, 2);
  const dataUri =
    "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);
  const exportFileDefaultName = "leads_export.json";

  const linkElement = document.createElement("a");
  linkElement.setAttribute("href", dataUri);
  linkElement.setAttribute("download", exportFileDefaultName);
  linkElement.click();
};

/**
 * Simple CSV parser
 */
const parseCSV = (csvText: string): any[] => {
  const lines = csvText.split('\n').filter(line => line.trim());
  if (lines.length === 0) return [];

  const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
    const row: any = {};

    headers.forEach((header, index) => {
      row[header] = values[index] || '';
    });

    data.push(row);
  }

  return data;
};

/**
 * Handle CSV import
 */
export const handleImportCSV = async (
  file: File,
  createManyLead: any,
  workspaceId: string,
  onSuccess: (leads: any[]) => void
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const csvData = e.target?.result as string;
        const parsedData = parseCSV(csvData);

        if (parsedData.length === 0) {
          toast.error("No data found in the CSV file.");
          reject(new Error("No data found"));
          return;
        }

        const normalizedData = normalizeImportedData(parsedData);

        // Validate data using Zod schema
        const validLeads = normalizedData.filter((lead: any) => {
          try {
            leadSchema.parse(lead);
            return true;
          } catch (error) {
            console.warn("Invalid lead data:", lead, error);
            return false;
          }
        });

        if (validLeads.length === 0) {
          toast.error("No valid leads found in the file.");
          reject(new Error("No valid leads found"));
          return;
        }

        if (validLeads.length < normalizedData.length) {
          toast.warning(
            `${normalizedData.length - validLeads.length} invalid leads were skipped.`
          );
        }

        // Create leads via API
        const response = await createManyLead({
          workspaceId,
          body: validLeads,
        });

        if (response.error) {
          toast.error("Error adding leads to database");
          reject(response.error);
          return;
        }

        onSuccess(validLeads);
        toast.success(`${validLeads.length} leads imported successfully`);
        resolve();
      } catch (error) {
        console.error("Error processing CSV data:", error);
        toast.error("Error processing CSV data");
        reject(error);
      }
    };

    reader.onerror = () => {
      toast.error("Error reading file");
      reject(new Error("Error reading file"));
    };

    reader.readAsText(file);
  });
};

/**
 * Validate file before import
 */
export const validateImportFile = (file: File): boolean => {
  // Check file type
  if (!file.name.toLowerCase().endsWith('.csv')) {
    toast.error("Please select a CSV file");
    return false;
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    toast.error("File size must be less than 10MB");
    return false;
  }

  return true;
};

/**
 * Download sample CSV template
 */
export const downloadSampleCSV = () => {
  const sampleData = [
    {
      Name: "John Doe",
      email: "<EMAIL>",
      phone: "+1234567890",
      company: "Example Corp",
      position: "Manager",
      contact_method: "WhatsApp",
      revenue: 5000,
    },
    {
      Name: "Jane Smith",
      email: "<EMAIL>",
      phone: "+0987654321",
      company: "Sample Inc",
      position: "Director",
      contact_method: "Call",
      revenue: 7500,
    },
  ];

  const worksheet = XLSX.utils.json_to_sheet(sampleData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Sample Leads");
  XLSX.writeFile(workbook, "leads_import_template.csv");

  toast.success("Sample CSV template downloaded");
};
