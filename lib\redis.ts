import { createClient } from 'redis';

// Redis client configuration
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

// Create Redis client
let redisClient: ReturnType<typeof createClient> | null = null;
let connectionAttempts = 0;
const MAX_CONNECTION_ATTEMPTS = 3;
let lastConnectionAttempt = 0;
const CONNECTION_RETRY_DELAY = 5000; // 5 seconds

// Initialize Redis client with improved error handling and reconnection logic
export const initRedis = async () => {
  try {
    // If we already have a client, return it
    if (redisClient?.isOpen) {
      return redisClient;
    }

    // Check if we've tried to connect too many times recently
    const now = Date.now();
    if (connectionAttempts >= MAX_CONNECTION_ATTEMPTS &&
        now - lastConnectionAttempt < CONNECTION_RETRY_DELAY) {
      console.warn('Too many Redis connection attempts, backing off');
      return null;
    }

    // Reset connection attempts if it's been a while
    if (now - lastConnectionAttempt > CONNECTION_RETRY_DELAY) {
      connectionAttempts = 0;
    }

    lastConnectionAttempt = now;
    connectionAttempts++;

    // Create a new client
    redisClient = createClient({
      url: redisUrl,
      socket: {
        reconnectStrategy: (retries) => {
          // Exponential backoff with max delay of 10 seconds
          const delay = Math.min(Math.pow(2, retries) * 100, 10000);
          return delay;
        },
      }
    });

    // Set up error handling
    redisClient.on('error', (err:any) => {
      console.error('Redis client error:', err);
      // Don't set to null here to allow reconnection strategy to work
    });

    // Reset connection attempts on successful connection
    redisClient.on('connect', () => {
      connectionAttempts = 0;
      console.log('Redis client connected');
    });

    await redisClient.connect();
    return redisClient;
  } catch (error) {
    console.error('Failed to initialize Redis client:', error);
    return null;
  }
};

// Cache statistics for monitoring
interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
}

const cacheStats: CacheStats = {
  hits: 0,
  misses: 0,
  sets: 0,
  deletes: 0,
  errors: 0
};

// Get data from cache with improved error handling and stats tracking
export const getFromCache = async <T>(key: string): Promise<T | null> => {
  try {
    const client = await initRedis();
    if (!client) return null;

    const data = await client.get(key);

    if (data) {
      cacheStats.hits++;
      return JSON.parse(data) as T;
    } else {
      cacheStats.misses++;
      return null;
    }
  } catch (error) {
    cacheStats.errors++;
    console.error('Error getting data from Redis cache:', error);
    return null;
  }
};

// Set data in cache with expiration and compression for large objects
export const setInCache = async <T>(
  key: string,
  data: T,
  expirationInSeconds: number = 1800 // Default: 30 minutes (reduced from 1 hour)
): Promise<boolean> => {
  try {
    const client = await initRedis();
    if (!client) return false;

    // Don't cache null or undefined values
    if (data === null || data === undefined) {
      return false;
    }

    // Serialize data
    const serialized = JSON.stringify(data);

    // Skip caching extremely large objects to prevent memory issues
    if (serialized.length > 1024 * 1024) { // 1MB limit
      console.warn(`Object too large to cache (${serialized.length} bytes): ${key.substring(0, 100)}...`);
      return false;
    }

    await client.set(key, serialized, {
      EX: expirationInSeconds,
    });

    cacheStats.sets++;
    return true;
  } catch (error) {
    cacheStats.errors++;
    console.error('Error setting data in Redis cache:', error);
    return false;
  }
};

// Delete data from cache with improved error handling
export const deleteFromCache = async (key: string): Promise<boolean> => {
  try {
    const client = await initRedis();
    if (!client) return false;

    await client.del(key);
    cacheStats.deletes++;
    return true;
  } catch (error) {
    cacheStats.errors++;
    console.error('Error deleting data from Redis cache:', error);
    return false;
  }
};

// Clear cache with pattern - optimized to handle large key sets
export const clearCachePattern = async (pattern: string): Promise<boolean> => {
  try {
    const client = await initRedis();
    if (!client) return false;

    // Use SCAN instead of KEYS for production environments with large datasets
    // This avoids blocking the Redis server
    let cursor = 0;
    let keys: string[] = [];

    do {
      const reply = await client.scan(cursor, {
        MATCH: pattern,
        COUNT: 100 // Process in smaller batches
      });

      cursor = reply.cursor;
      keys = keys.concat(reply.keys);

      // Delete in batches to avoid large commands
      if (keys.length >= 100) {
        if (keys.length > 0) {
          await client.del(keys);
          cacheStats.deletes += keys.length;
        }
        keys = [];
      }
    } while (cursor !== 0);

    // Delete any remaining keys
    if (keys.length > 0) {
      await client.del(keys);
      cacheStats.deletes += keys.length;
    }

    return true;
  } catch (error) {
    cacheStats.errors++;
    console.error('Error clearing cache pattern:', error);
    return false;
  }
};

// Get cache statistics
export const getCacheStats = (): CacheStats => {
  return { ...cacheStats };
};

// Reset cache statistics
export const resetCacheStats = (): void => {
  cacheStats.hits = 0;
  cacheStats.misses = 0;
  cacheStats.sets = 0;
  cacheStats.deletes = 0;
  cacheStats.errors = 0;
};

// Close Redis connection with proper cleanup
export const closeRedisConnection = async (): Promise<boolean> => {
  try {
    if (redisClient) {
      // Remove all listeners to prevent memory leaks
      redisClient.removeAllListeners();
      await redisClient.quit();
      redisClient = null;
      connectionAttempts = 0;
      console.log('Redis connection closed');
    }
    return true;
  } catch (error) {
    console.error('Error closing Redis connection:', error);
    return false;
  }
};
