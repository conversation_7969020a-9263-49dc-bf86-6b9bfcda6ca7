"use client";

import * as React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { SectionLoader } from "@/components/ui/global-loader";
import { cn } from "@/lib/utils";

interface LoadingCardProps extends React.HTMLAttributes<HTMLDivElement> {
  isLoading: boolean;
  title?: string;
  loadingText?: string;
  minHeight?: string;
}

export function LoadingCard({
  isLoading,
  title,
  loadingText = "Loading data...",
  minHeight = "300px",
  children,
  className,
  ...props
}: LoadingCardProps) {
  return (
    <Card className={cn("relative overflow-hidden", className)} {...props}>
      {title && (
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent className={cn("p-6", isLoading ? "min-h-[300px]" : "")} style={{ minHeight: isLoading ? minHeight : "auto" }}>
        {isLoading ? (
          <SectionLoader text={loadingText} />
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
}
