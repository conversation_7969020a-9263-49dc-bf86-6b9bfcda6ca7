import { TagsApi as BaseTagsA<PERSON> } from "../base/tags";

// Define types for the API
interface Tags {
  id: string; // or number, depending on your backend
  name: string;
  description?: string;
  workspaceId: string; // Include workspaceId if applicable
}

interface NewTags {
  id?: string;
  name: string;
  color: string;
  //   countInStatistics: boolean;
  //   showInWorkspace: boolean;
}

interface UpdatedTags extends Partial<NewTags> {
  id: string; // `id` is required for update
}

// Define the RTK Query API
export const TagsApi = BaseTagsApi.injectEndpoints({
  endpoints: (builder) => ({
    addTags: builder.mutation<Tags, { tagsData: NewTags; workspaceId: string }>(
      {
        query: ({ tagsData, workspaceId }) => ({
          url: `/create?workspaceId=${workspaceId}`,
          method: "POST",
          body: tagsData,
        }),
        invalidatesTags: (result, error, arg) => [
          { type: 'Tag', id: 'LIST' },
          { type: 'TagByWorkspace', id: arg.workspaceId },
          { type: 'TagList', id: arg.workspaceId }
        ],
      }
    ),
    updateTags: builder.mutation<void, { id: any; updatedTags: any }>({
      query: ({ id, updatedTags }) => ({
        url: `/${id}`,
        method: "PUT",
        body: { updatedTags },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Tag', id: arg.id },
        { type: 'Tag', id: 'LIST' },
        { type: 'TagByWorkspace', id: 'LIST' }
      ],
    }),

    getTags: builder.query<Tags, any>({
      query: (workspaceId) => ({
        url: `/workspace/${workspaceId}`,
        method: "GET",
      }),
      providesTags: (result, error, workspaceId) => [
        { type: 'TagByWorkspace', id: workspaceId },
        { type: 'TagList', id: workspaceId },
        { type: 'Tag', id: 'LIST' }
      ],
    }),
    deleteTags: builder.mutation<void, { id: any; workspace_id: string }>({
      query: ({ id }) => ({
        url: `/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Tag', id: arg.id },
        { type: 'Tag', id: 'LIST' },
        { type: 'TagByWorkspace', id: arg.workspace_id },
        { type: 'TagList', id: arg.workspace_id }
      ],
    }),
  }),
});

export const {
  useAddTagsMutation,
  useGetTagsQuery,
  useUpdateTagsMutation,
  useDeleteTagsMutation,
} = TagsApi;
