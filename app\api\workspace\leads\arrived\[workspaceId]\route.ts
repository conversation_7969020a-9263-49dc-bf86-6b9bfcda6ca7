import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../../../utils/auth";

/**
 * Get arrived leads count for a workspace
 * @route GET /api/workspace/leads/arrived/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const workspaceId = params.workspaceId;
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to view this workspace
    const permission = await checkWorkspacePermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Convert workspaceId to integer
    const workspaceIdInt = parseInt(workspaceId);
    if (isNaN(workspaceIdInt)) {
      return NextResponse.json({ error: "Invalid workspace ID format" }, { status: 400 });
    }
    
    try {
      const { data, error } = await supabase.rpc("count_arrived_leads", {
        workspace_id: workspaceIdInt,
      });
      
      if (error) {
        console.error("Error counting arrived leads:", error);
        return NextResponse.json({ error: error.message }, { status: 400 });
      }
      
      return NextResponse.json({ arrivedLeadsCount: data }, { status: 200 });
    } catch (error) {
      console.error("Error counting arrived leads:", error);
      return NextResponse.json({ 
        error: "Failed to count arrived leads" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching arrived leads count:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
