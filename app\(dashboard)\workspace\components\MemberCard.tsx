"use client";

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { User<PERSON>ircle, Trash2, <PERSON>ader2, <PERSON>ci<PERSON> } from "lucide-react";
import { ProfileImageUpload } from './ProfileImageUpload';
import { MemberCardProps } from '../types/member';
import { MEMBER_CONSTANTS, STATUS_COLORS } from '../constants/member';
import { getMemberDisplayName, formatRole, formatStatus } from '../utils/member';

interface MemberCardComponentProps extends MemberCardProps {
  onEditRole?: (member: any) => void;
  resendingMembers?: { [key: string]: boolean };
}

export function MemberCard({
  member,
  onUpdate,
  onDelete,
  onResendInvite,
  onEditRole,
  isDeleting,
  resendingMembers = {},
}: MemberCardComponentProps) {
  const displayName = getMemberDisplayName(member);
  const isResendingThisMember = member.id ? resendingMembers[member.id] : false;
  const statusColor = STATUS_COLORS[member.status] || 'text-muted-foreground';

  const handleResendInvite = () => {
    if (onResendInvite && member.id) {
      onResendInvite(member);
    }
  };

  const handleDelete = () => {
    onDelete(member);
  };

  const handleEditRole = () => {
    if (onEditRole) {
      onEditRole(member);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between p-2 md:p-4 bg-secondary rounded-lg gap-2 hover:bg-secondary/80 transition-colors">
      <div className="flex items-center space-x-2 sm:space-x-3">
        <div className="relative flex-shrink-0">
          {member.profileImage ? (
            <img
              src={member.profileImage}
              alt={displayName}
              className="md:w-10 md:h-10 w-8 h-8 rounded-full object-cover ring-2 ring-background"
            />
          ) : (
            <UserCircle className="md:w-10 md:h-10 w-8 h-8 text-muted-foreground" />
          )}

          <ProfileImageUpload
            member={member}
            onImageUpdate={(memberId, imageUrl) => {
              onUpdate({ ...member, profileImage: imageUrl });
            }}
          />
        </div>

        <div className="min-w-0 flex-1">
          <p className="font-medium text-xs sm:text-sm truncate">
            {displayName}
          </p>
          <p className="text-xs sm:text-sm text-muted-foreground truncate">
            {member.email}
          </p>
          <p className={`text-xs sm:text-sm capitalize ${statusColor}`}>
            {formatRole(member.role)} • {formatStatus(member.status)}
          </p>
        </div>
      </div>

      <div className="flex flex-row items-center justify-end space-x-2 self-end sm:self-center">
        {/* Resend Invite Button */}
        {member.status === MEMBER_CONSTANTS.STATUS.PENDING && onResendInvite && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleResendInvite}
            disabled={isResendingThisMember}
            className="text-xs"
          >
            {isResendingThisMember ? (
              <>
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                {MEMBER_CONSTANTS.UI_TEXT.RESENDING_BUTTON}
              </>
            ) : (
              MEMBER_CONSTANTS.UI_TEXT.RESEND_BUTTON
            )}
          </Button>
        )}

        <div className="flex items-center space-x-1">
          {/* Edit Role Button */}
          {member.role === MEMBER_CONSTANTS.ROLES.MEMBER && onEditRole && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleEditRole}
              className="h-8 w-8 text-muted-foreground hover:text-foreground"
            >
              <Pencil className="h-3 w-3" />
            </Button>
          )}

          {/* Delete Button */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-destructive hover:text-destructive/90 hover:bg-destructive/10"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Trash2 className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
