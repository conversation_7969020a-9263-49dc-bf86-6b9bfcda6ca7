import { useState, useCallback } from "react";
import { FilterType } from "../types/integration";

export const useIntegrationState = () => {
  // UI state
  const [expandedRow, setExpandedRow] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState<FilterType>("all");

  // Toggle row expansion for mobile view
  const toggleRow = useCallback((id: string) => {
    setExpandedRow(current => current === id ? null : id);
  }, []);

  // Handle search term change
  const handleSearchChange = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // Handle filter change
  const handleFilterChange = useCallback((filter: FilterType) => {
    setActiveFilter(filter);
  }, []);

  // Reset all filters
  const resetFilters = useCallback(() => {
    setSearchTerm("");
    setActiveFilter("all");
    setExpandedRow(null);
  }, []);

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  // Collapse all rows
  const collapseAllRows = useCallback(() => {
    setExpandedRow(null);
  }, []);

  return {
    // State
    expandedRow,
    searchTerm,
    activeFilter,

    // Actions
    toggleRow,
    handleSearchChange,
    handleFilterChange,
    resetFilters,
    clearSearch,
    collapseAllRows,

    // Setters (for direct access if needed)
    setExpandedRow,
    setSearchTerm,
    setActiveFilter,
  };
};
