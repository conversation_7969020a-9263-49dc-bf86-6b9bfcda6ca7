import { supabase } from "@/lib/supabaseServer";

// Notification interfaces
export interface NotificationDetails {
  [key: string]: any;
}

export interface Notification {
  lead_id: string;
  action_type: string;
  user_id: string;
  workspace_id: string;
  details: NotificationDetails;
  read: boolean;
  created_at: string;
}

/**
 * Creates a notification for a lead change
 * @param leadId The ID of the lead that changed
 * @param action The type of action that occurred
 * @param userId The ID of the user who performed the action
 * @param workspaceId The ID of the workspace the lead belongs to
 * @param details Additional details about the change
 * @returns The created notification or undefined if an error occurred
 */
export async function notifyLeadChange(
  leadId: string,
  action: string,
  userId: string,
  workspaceId: string,
  details: NotificationDetails = {}
): Promise<Notification[] | undefined> {
  try {
    // Fetch user information from workspace_members for this specific workspace
    const { data: workspaceMemberData, error: workspaceMemberError } = await supabase
      .from("workspace_members")
      .select("name")
      .eq("user_id", userId)
      .eq("workspace_id", workspaceId)
      .single();
    
    let userDisplayName = "Unknown user";
    
    if (workspaceMemberError) {
      console.error("Failed to fetch workspace member data:", workspaceMemberError.message);
    } else if (workspaceMemberData) {
      userDisplayName = workspaceMemberData.name;
    }
    
    // Enrich the details with names for various user IDs
    const enrichedDetails = { ...details };
    enrichedDetails.actor_name = userDisplayName;

    if (details.updated_by) {
      const { data: updaterData, error: updaterError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.updated_by)
        .eq("workspace_id", workspaceId)
        .single();
        
      if (!updaterError && updaterData) {
        enrichedDetails.updated_by_name = updaterData.name;
      }
    }
    
    // For deleted_by
    if (details.deleted_by) {
      const { data: deleterData, error: deleterError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.deleted_by)
        .eq("workspace_id", workspaceId)
        .single();
        
      if (!deleterError && deleterData) {
        enrichedDetails.deleted_by_name = deleterData.name;
      }
    }
    
    // For new_assignee (if present)
    if (details.new_assignee) {
      const { data: assigneeData, error: assigneeError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.new_assignee)
        .eq("workspace_id", workspaceId)
        .single();
        
      if (!assigneeError && assigneeData) {
        enrichedDetails.new_assignee_name = assigneeData.name;
      }
    }
    
    // For previous_assignee (if present)
    if (details.previous_assignee) {
      const { data: prevAssigneeData, error: prevAssigneeError } = await supabase
        .from("workspace_members")
        .select("name")
        .eq("user_id", details.previous_assignee)
        .eq("workspace_id", workspaceId)
        .single();
        
      if (!prevAssigneeError && prevAssigneeData) {
        enrichedDetails.previous_assignee_name = prevAssigneeData.name;
      }
    }
    
    // Create the notification
    const { data: notification, error } = await supabase.from("notifications").insert([
      {
        lead_id: leadId,
        action_type: action,
        user_id: userId,
        workspace_id: workspaceId,
        details: enrichedDetails,
        read: false,
        created_at: new Date().toISOString(),
      },
    ]).select("*");

    if (error) {
      console.error("Failed to create notification:", error.message);
      return undefined;
    }

    // Create notification read status
    if (notification && notification.length > 0) {
      const { error: readStatusError } = await supabase
        .from("notification_read_status")
        .insert([
          {
            notification_id: notification[0].id,
            user_id: userId,
            read: false,
            read_at: new Date().toISOString(),
          },
        ]);
      
      if (readStatusError) {
        console.error("Failed to create notification read status:", readStatusError.message);
      }
    }

    return notification;
  } catch (err) {
    console.error("Notification error:", err);
    return undefined;
  }
}
