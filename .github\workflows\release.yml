name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Get version from tag
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT

      - name: Generate changelog
        id: changelog
        uses: metcalfc/changelog-generator@v4.1.0
        with:
          myToken: ${{ secrets.GITHUB_TOKEN }}

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          name: Release ${{ steps.get_version.outputs.VERSION }}
          body: |
            # INFILABS CRM v${{ steps.get_version.outputs.VERSION }}

            ## Changes in this Release
            ${{ steps.changelog.outputs.changelog }}

            ## Installation
            ```bash
            # Clone the repository
            git clone https://github.com/your-username/infilabs-crm.git
            cd infilabs-crm

            # Checkout this release
            git checkout v${{ steps.get_version.outputs.VERSION }}

            # Install dependencies
            npm install

            # Build the application
            npm run build

            # Start the application
            npm start
            ```

            ## Documentation
            For full documentation, visit our [docs](./docs).
          draft: false
          prerelease: false
