import { sendMail } from "@/lib/sendmail";

export interface InvitationEmailData {
  email: string;
  token: string;
  workspaceName: string;
  inviterName: string;
  expiresAt: Date;
}

/**
 * Sends a secure invitation email to a new workspace member
 */
export async function sendInvitationEmail(data: InvitationEmailData): Promise<boolean> {
  try {
    const { email, token, workspaceName, inviterName, expiresAt } = data;

    const inviteUrl = `${process.env.PUBLIC_URL}/accept-invite?token=${token}`;
    const expiryDate = expiresAt.toLocaleDateString();
    const expiryTime = expiresAt.toLocaleTimeString();

    await sendMail(
      email,
      `You're invited to join ${workspaceName}`,
      `
        <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; padding: 0; background-color: #ffffff;">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 20px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 600;">You're Invited!</h1>
            <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">${inviterName} has invited you to join ${workspaceName}</p>
          </div>

          <!-- Content -->
          <div style="padding: 40px 20px;">
            <div style="background-color: #f8fafc; border-radius: 12px; padding: 24px; margin-bottom: 32px;">
              <h2 style="color: #1a202c; margin: 0 0 16px 0; font-size: 20px; font-weight: 600;">What's Next?</h2>
              <p style="color: #4a5568; margin: 0; font-size: 16px; line-height: 1.6;">
                Click the button below to accept your invitation and start collaborating with your team.
                You'll be able to access all workspace features and begin contributing immediately.
              </p>
            </div>

            <!-- CTA Button -->
            <div style="text-align: center; margin: 32px 0;">
              <a href="${inviteUrl}"
                 style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 16px 32px; text-decoration: none; border-radius: 8px; font-size: 16px; font-weight: 600; box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4); transition: all 0.2s ease;">
                Accept Invitation
              </a>
            </div>

            <!-- Security Info -->
            <div style="background-color: #fff5f5; border-left: 4px solid #f56565; padding: 16px; margin: 32px 0; border-radius: 4px;">
              <h3 style="color: #c53030; margin: 0 0 8px 0; font-size: 14px; font-weight: 600;">Security Notice</h3>
              <p style="color: #742a2a; margin: 0; font-size: 14px; line-height: 1.5;">
                This invitation expires on <strong>${expiryDate} at ${expiryTime}</strong>.
                If you didn't expect this invitation, you can safely ignore this email.
              </p>
            </div>

            <!-- Alternative Link -->
            <div style="margin-top: 32px; padding-top: 24px; border-top: 1px solid #e2e8f0;">
              <p style="color: #718096; font-size: 14px; margin: 0 0 8px 0;">
                Having trouble with the button? Copy and paste this link into your browser:
              </p>
              <p style="color: #4299e1; font-size: 14px; word-break: break-all; margin: 0;">
                ${inviteUrl}
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #f7fafc; padding: 24px 20px; text-align: center; border-top: 1px solid #e2e8f0;">
            <p style="color: #718096; font-size: 12px; margin: 0;">
              This invitation was sent by ${inviterName} from ${workspaceName}
            </p>
            <p style="color: #a0aec0; font-size: 12px; margin: 8px 0 0 0;">
              © ${new Date().getFullYear()} INFILABS. All rights reserved.
            </p>
          </div>
        </div>
      `,
      {
        requestReadReceipt: true,
        deliveryNotification: true
      }
    );

    return true;
  } catch (error) {
    console.error("Error sending invitation email:", error);
    return false;
  }
}

/**
 * Legacy function for backward compatibility
 * @deprecated Use sendInvitationEmail with InvitationEmailData instead
 */
export async function sendInvitationEmailLegacy(email: string, workspaceId: string, status: string): Promise<boolean> {
  return sendInvitationEmail({
    email,
    token: 'legacy',
    workspaceName: 'Workspace',
    inviterName: 'Team',
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  });
}
