"use client";

import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { GeneralSettingsProps } from '../types/workspace';
import { WORKSPACE_CONSTANTS, INDUSTRY_OPTIONS, COMPANY_SIZE_OPTIONS, TIMEZONE_OPTIONS } from '../constants/workspace';

export function GeneralSettings({
  settings,
  setSettings,
  isEditMode,
  isSaving,
  onEditClick,
  onCancelEdit,
  onSave,
}: GeneralSettingsProps) {
  return (
    <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
      <CardHeader className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg md:text-xl font-semibold">
              {WORKSPACE_CONSTANTS.UI_TEXT.GENERAL_TITLE}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground mt-1">
              {WORKSPACE_CONSTANTS.UI_TEXT.GENERAL_DESCRIPTION}
            </CardDescription>
          </div>
          {!isEditMode && (
            <Button
              variant="outline"
              size="sm"
              onClick={onEditClick}
              className="flex items-center gap-2"
            >
              {WORKSPACE_CONSTANTS.UI_TEXT.EDIT_BUTTON}
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-4 md:p-6 pt-0 space-y-6">
        <div className="grid gap-4 md:gap-6">
          {/* Workspace Name */}
          <div className="space-y-2">
            <Label htmlFor="workspace-name" className="text-sm font-medium">
              {WORKSPACE_CONSTANTS.UI_TEXT.WORKSPACE_NAME}
            </Label>
            <Input
              id="workspace-name"
              value={settings.name}
              onChange={(e) =>
                setSettings({ ...settings, name: e.target.value })
              }
              disabled={!isEditMode}
              className="w-full"
              placeholder="Enter workspace name"
            />
          </div>

          {/* Industry */}
          <div className="space-y-2">
            <Label htmlFor="industry" className="text-sm font-medium">
              {WORKSPACE_CONSTANTS.UI_TEXT.INDUSTRY}
            </Label>
            <Select
              value={settings.industry}
              onValueChange={(value) =>
                setSettings({ ...settings, industry: value })
              }
              disabled={!isEditMode}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select industry" />
              </SelectTrigger>
              <SelectContent>
                {INDUSTRY_OPTIONS.map((industry) => (
                  <SelectItem key={industry} value={industry}>
                    {industry}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Company Size */}
          <div className="space-y-2">
            <Label htmlFor="company-size" className="text-sm font-medium">
              {WORKSPACE_CONSTANTS.UI_TEXT.COMPANY_SIZE}
            </Label>
            <Select
              value={settings.company_size}
              onValueChange={(value) =>
                setSettings({ ...settings, company_size: value })
              }
              disabled={!isEditMode}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select company size" />
              </SelectTrigger>
              <SelectContent>
                {COMPANY_SIZE_OPTIONS.map((size) => (
                  <SelectItem key={size} value={size}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Timezone */}
          <div className="space-y-2">
            <Label htmlFor="timezone" className="text-sm font-medium">
              {WORKSPACE_CONSTANTS.UI_TEXT.TIMEZONE}
            </Label>
            <Select
              value={settings.timezone}
              onValueChange={(value) =>
                setSettings({ ...settings, timezone: value })
              }
              disabled={!isEditMode}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
              <SelectContent>
                {TIMEZONE_OPTIONS.map((timezone) => (
                  <SelectItem key={timezone} value={timezone}>
                    {timezone}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        {isEditMode && (
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-4 pt-4 border-t">
            <Button
              variant="outline"
              onClick={onCancelEdit}
              disabled={isSaving}
              className="w-full sm:w-auto"
            >
              {WORKSPACE_CONSTANTS.UI_TEXT.CANCEL_BUTTON}
            </Button>
            <Button
              onClick={onSave}
              disabled={isSaving}
              className="w-full sm:w-auto"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {WORKSPACE_CONSTANTS.UI_TEXT.SAVING_BUTTON}
                </>
              ) : (
                WORKSPACE_CONSTANTS.UI_TEXT.SAVE_BUTTON
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
