-- Migration for table: workspaces
CREATE TABLE IF NOT EXISTS "workspaces" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "name" text,
  "owner_id" uuid,
  "company_type" text,
  "status" boolean,
  "profile_url" text,
  "company_size" text,
  "industry" text,
  "timezone" text,
  "notifications" jsonb,
  "lead_status" text,
  "members" text,
  "security" text
);

-- Enable Row Level Security
ALTER TABLE "workspaces" ENABLE ROW LEVEL SECURITY;

