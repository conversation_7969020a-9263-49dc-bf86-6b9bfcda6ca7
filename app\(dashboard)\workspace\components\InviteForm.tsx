"use client";

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Mail, Loader2 } from "lucide-react";
import { InviteFormProps } from '../types/member';
import { MEMBER_CONSTANTS, ROLE_OPTIONS } from '../constants/member';

interface InviteFormComponentProps extends InviteFormProps {
  email: string;
  role: string;
  onEmailChange: (email: string) => void;
  onRoleChange: (role: string) => void;
  isValid: boolean;
}

export function InviteForm({
  email,
  role,
  onEmailChange,
  onRoleChange,
  onInvite,
  isLoading,
  isValid,
}: InviteFormComponentProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      onInvite(email, role);
    }
  };

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">{MEMBER_CONSTANTS.UI_TEXT.INVITE_LABEL}</Label>
      <form onSubmit={handleSubmit}>
        <div className="flex flex-col md:flex-row gap-2">
          <Input
            type="email"
            placeholder={MEMBER_CONSTANTS.UI_TEXT.EMAIL_PLACEHOLDER}
            value={email}
            onChange={(e) => onEmailChange(e.target.value)}
            className="flex-1 h-10"
            disabled={isLoading}
            required
          />
          <div className="flex flex-col md:flex-row gap-2">
            <Select
              value={role}
              onValueChange={onRoleChange}
              disabled={isLoading}
            >
              <SelectTrigger className="w-full sm:w-[140px] h-10">
                <SelectValue placeholder={MEMBER_CONSTANTS.UI_TEXT.ROLE_PLACEHOLDER} />
              </SelectTrigger>
              <SelectContent>
                {ROLE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              type="submit"
              className="w-full sm:w-auto h-10 px-4"
              disabled={isLoading || !isValid}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {MEMBER_CONSTANTS.UI_TEXT.INVITING_BUTTON}
                </>
              ) : (
                <>
                  <Mail className="mr-2 h-4 w-4" />
                  {MEMBER_CONSTANTS.UI_TEXT.INVITE_BUTTON}
                </>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
