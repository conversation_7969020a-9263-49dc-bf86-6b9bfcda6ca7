"use client";

import React, { ReactNode } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store/store';

// Loading overlay component that uses Redux state
const LoadingOverlay = () => {
  const { isLoading, loadingMessage, loadingProgress } = useSelector(
    (state: RootState) => state.loading
  );

  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-card rounded-lg shadow-lg p-6 max-w-md w-full mx-4">
        <div className="flex flex-col items-center">
          <div className="relative h-12 w-12 mb-4">
            <div className="absolute inset-0 border-4 border-primary/30 rounded-full"></div>
            <div 
              className="absolute inset-0 border-4 border-primary rounded-full"
              style={{ 
                clipPath: `polygon(0 0, 100% 0, 100% 100%, 0 100%)`,
                animation: 'spin 1.5s linear infinite'
              }}
            ></div>
          </div>
          
          {loadingMessage && (
            <p className="text-center text-lg font-medium mb-2">{loadingMessage}</p>
          )}
          
          {loadingProgress > 0 && (
            <div className="w-full bg-muted rounded-full h-2 mt-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300 ease-in-out"
                style={{ width: `${loadingProgress}%` }}
              ></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

interface LoadingProviderProps {
  children: ReactNode;
}

// This component now just renders the loading overlay alongside children
// It doesn't manage state anymore - Redux does that
export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  return (
    <>
      <LoadingOverlay />
      {children}
    </>
  );
};

// Export the useLoading hook from our Redux hook
export { useLoading } from '@/lib/hooks/useLoading';
