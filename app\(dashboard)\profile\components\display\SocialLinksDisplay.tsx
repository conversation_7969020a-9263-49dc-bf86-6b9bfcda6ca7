"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ProfileDetails } from '../../types/profile';

interface SocialLinksDisplayProps {
  profileData: ProfileDetails;
}

export function SocialLinksDisplay({ profileData }: SocialLinksDisplayProps) {
  const { socialLinks } = profileData;
  const hasSocialLinks = Object.values(socialLinks).some(Boolean);

  const handleLinkClick = (url: string) => {
    window.open(url, "_blank");
  };

  return (
    <Card className="mt-3 sm:mt-4 md:mt-6 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px] shadow-sm">
      <CardHeader className="p-3 sm:p-4 md:p-6 pb-2 sm:pb-3 md:pb-4">
        <CardTitle className="text-base sm:text-lg md:text-xl font-bold">
          Connect with Me
        </CardTitle>
      </CardHeader>
      <CardContent className="p-3 sm:p-4 md:p-6 pt-0">
        <div className="flex flex-wrap gap-2 sm:gap-3 md:gap-4">
          {socialLinks.linkedin && (
            <Button
              variant="outline"
              size="sm"
              className="h-8 sm:h-9 text-xs sm:text-sm px-3 sm:px-4 hover:bg-blue-50 hover:border-blue-200 dark:hover:bg-blue-950 dark:hover:border-blue-800"
              onClick={() => handleLinkClick(socialLinks.linkedin!)}
            >
              LinkedIn
            </Button>
          )}
          {socialLinks.twitter && (
            <Button
              variant="outline"
              size="sm"
              className="h-8 sm:h-9 text-xs sm:text-sm px-3 sm:px-4 hover:bg-sky-50 hover:border-sky-200 dark:hover:bg-sky-950 dark:hover:border-sky-800"
              onClick={() => handleLinkClick(socialLinks.twitter!)}
            >
              Twitter
            </Button>
          )}
          {socialLinks.github && (
            <Button
              variant="outline"
              size="sm"
              className="h-8 sm:h-9 text-xs sm:text-sm px-3 sm:px-4 hover:bg-gray-50 hover:border-gray-200 dark:hover:bg-gray-950 dark:hover:border-gray-800"
              onClick={() => handleLinkClick(socialLinks.github!)}
            >
              GitHub
            </Button>
          )}
          {!hasSocialLinks && (
            <p className="text-xs sm:text-sm text-muted-foreground px-1 py-2">
              No social links added
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
