import { NextRequest } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { AUTH_MESSAGES } from "@/lib/constant/auth";

/**
 * Authenticates a user from the request's authorization header
 * @param request The Next.js request object
 * @returns Object containing the authenticated user or an error
 */
export async function authenticateUser(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  const token = authHeader.split(" ")[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { error: AUTH_MESSAGES.UNAUTHORIZED, status: 401 };
  }
  
  return { user, token };
}

/**
 * Checks if a user has admin permissions for a workspace
 * @param userId The user ID to check
 * @param workspaceId The workspace ID to check permissions for
 * @returns Object indicating if the user is an admin and any error
 */
export async function checkWorkspaceAdminPermission(userId: string, workspaceId: string) {
  try {
    // First check if user is workspace owner
    const { data: workspaceData, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();

    if (workspaceError) {
      console.error("Workspace Check Error:", workspaceError.message);
      return { isAdmin: false, error: workspaceError.message };
    }

    const isWorkspaceOwner = workspaceData?.owner_id === userId;

    // If user is workspace owner, they have admin permissions
    if (isWorkspaceOwner) {
      return { isAdmin: true, error: null };
    }

    // If not workspace owner, check if user is admin
    const { data: memberData, error: memberError } = await supabase
      .from("workspace_members")
      .select("role")
      .eq("workspace_id", workspaceId)
      .eq("user_id", userId)
      .single();

    if (memberError) {
      console.error("Workspace Member Check Error:", memberError.message);
      return { isAdmin: false, error: memberError.message };
    }

    const isAdmin = memberData?.role === "admin" || memberData?.role === "SuperAdmin";

    return { isAdmin, error: null };
  } catch (error) {
    console.error("Permission check error:", error);
    return { isAdmin: false, error: "Failed to check permissions" };
  }
}
