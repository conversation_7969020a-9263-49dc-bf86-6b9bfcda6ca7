export interface ProfileDetails {
  personalInfo: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    avatar: string;
    dateOfBirth: string;
  };
  professionalInfo: {
    title: string;
    department: string;
    company: string;
    startDate: string;
  };
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
  apiKeys?: {
    openAI?: string;
    togetherAPI?: string;
    reoonEmail?: string;
    bigDataCloud?: string;
  };
}

export interface ProfileFormData {
  firstName: string;
  lastName: string;
  phone: string;
  dateOfBirth: string;
  title: string;
  department: string;
  company: string;
  startDate: string;
  linkedin: string;
  twitter: string;
  github: string;
  avatar: string;
  openAI: string;
  togetherAPI: string;
  reoonEmail: string;
  bigDataCloud: string;
}

export interface EditProfileDialogProps {
  profileData: ProfileDetails;
  onProfileUpdate: (data: ProfileFormData) => Promise<void>;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}
