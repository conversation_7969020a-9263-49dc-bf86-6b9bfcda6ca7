"use client";

import React from 'react';
import CartForm from '@/components/ui/cardForm';
import { EditRoleDialogProps } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';

export function EditRoleDialog({
  member,
  isOpen,
  onClose,
  onSubmit,
  workspaceName,
}: EditRoleDialogProps) {
  if (!isOpen || !member) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900 bg-opacity-20 dark:bg-opacity-10 z-50">
      <div className="relative p-4 sm:p-6 rounded-lg w-[90%] max-w-md sm:max-w-lg h-auto max-h-[90vh] bg-white dark:bg-gray-800 shadow-lg border border-gray-200 dark:border-gray-700 overflow-y-auto">
        {/* Modal Header */}
        <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100 border-b border-gray-300 dark:border-gray-600 pb-2">
          {MEMBER_CONSTANTS.UI_TEXT.EDIT_ROLE_TITLE}
        </h3>

        {/* Workspace Name */}
        {workspaceName && (
          <h1 className="text-base sm:text-lg font-semibold mb-4 text-gray-900 dark:text-gray-200">
            {MEMBER_CONSTANTS.UI_TEXT.WORKSPACE_LABEL}
            <span className="text-gray-500 dark:text-gray-400 ml-2">
              {workspaceName}
            </span>
          </h1>
        )}

        {/* Cart Form */}
        <div className="mb-4">
          <CartForm
            onSubmit={onSubmit}
            defaultEmail={member.email}
          />
        </div>

        {/* Close Button */}
        <div className="absolute h-6 w-6 top-3 right-3 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center p-1 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">
          <button
            onClick={onClose}
            className="text-gray-400 dark:text-gray-300 hover:text-gray-600 dark:hover:text-gray-100 text-sm font-bold"
            aria-label="Close dialog"
          >
            ✖
          </button>
        </div>
      </div>
    </div>
  );
}
