"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ooter } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface SubscriptionSkeletonProps {
  isCollapsed?: boolean;
}

export function SubscriptionSkeleton({ isCollapsed = false }: SubscriptionSkeletonProps) {
  return (
    <div
      className={`transition-all duration-500 ease-in-out p-2 sm:p-4 md:p-6 lg:p-8 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden bg-gradient-to-b from-background to-muted/20`}
    >
      {/* Header Skeleton */}
      <div className="mb-8 md:mb-12 text-center max-w-3xl mx-auto">
        <Skeleton className="h-6 w-32 mx-auto mb-4 rounded-full" />
        <Skeleton className="h-10 md:h-12 w-64 md:w-80 mx-auto mb-4" />
        <Skeleton className="h-5 w-full max-w-2xl mx-auto" />
      </div>

      {/* Current Subscription Skeleton */}
      <Card className="mb-8 md:mb-10 w-full max-w-4xl mx-auto bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
        <CardContent className="pt-6 p-4 md:p-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-5">
            <div className="space-y-3 flex-1">
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <Skeleton className="h-6 w-48" />
              </div>
              <Skeleton className="h-4 w-64" />
            </div>
            <div className="flex gap-3">
              <Skeleton className="h-6 w-16 rounded-full" />
              <Skeleton className="h-9 w-32 rounded-md" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Billing Cycle Toggle Skeleton */}
      <div className="flex justify-center mb-8 md:mb-12 w-full">
        <Skeleton className="h-12 w-80 rounded-full" />
      </div>

      {/* Pricing Cards Skeleton */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 w-full mb-12 md:mb-16">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="flex flex-col h-full bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
            <CardHeader className="pb-4 pt-6 p-4 md:p-6">
              <div className="text-center space-y-3">
                <Skeleton className="h-6 w-24 mx-auto" />
                <Skeleton className="h-8 w-20 mx-auto" />
                <Skeleton className="h-4 w-32 mx-auto" />
              </div>
            </CardHeader>
            <CardContent className="flex-grow p-4 md:p-6 pt-0">
              <div className="h-px w-full bg-border mb-6"></div>
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map((j) => (
                  <div key={j} className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4 rounded-full" />
                    <Skeleton className="h-4 w-full" />
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="pt-2 pb-6 p-4 md:p-6">
              <Skeleton className="h-10 w-full rounded-md" />
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Payment Method Skeleton */}
      <div className="w-full max-w-3xl mx-auto mb-12 md:mb-16">
        <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
          <CardContent className="p-6 md:p-8">
            <div className="text-center mb-6 md:mb-8">
              <Skeleton className="h-12 w-12 rounded-full mx-auto mb-4" />
              <Skeleton className="h-6 w-48 mx-auto mb-2" />
              <Skeleton className="h-4 w-64 mx-auto" />
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-5">
              {[1, 2, 3].map((i) => (
                <Card key={i} className="border border-gray-200 dark:border-gray-800">
                  <CardContent className="p-4 md:p-5 flex flex-col items-center">
                    <Skeleton className="h-14 w-14 rounded-full mb-4" />
                    <Skeleton className="h-5 w-16 mb-2" />
                    <Skeleton className="h-4 w-24 mb-3" />
                    <Skeleton className="h-6 w-20" />
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <div className="mt-6 text-center">
              <Skeleton className="h-4 w-80 mx-auto" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Subscribe Button Skeleton */}
      <div className="flex justify-center mb-8 md:mb-12">
        <div className="w-full max-w-md mx-auto">
          <Skeleton className="h-14 w-full rounded-xl" />
        </div>
      </div>

      {/* Security Note Skeleton */}
      <div className="flex justify-center mb-16 md:mb-20">
        <Skeleton className="h-4 w-72" />
      </div>

      {/* Feature Comparison Table Skeleton */}
      <div className="w-full">
        <div className="text-center mb-6 md:mb-8">
          <Skeleton className="h-8 w-64 mx-auto mb-3" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>

        <Card className="overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
          <CardContent className="p-0">
            {/* Table Header */}
            <div className="bg-muted/50 p-4 border-b">
              <div className="grid grid-cols-4 gap-4">
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-20" />
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
            
            {/* Table Rows */}
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="p-4 border-b last:border-b-0">
                <div className="grid grid-cols-4 gap-4 items-center">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="flex justify-center">
                    <Skeleton className="h-5 w-5 rounded-full" />
                  </div>
                  <div className="flex justify-center">
                    <Skeleton className="h-5 w-5 rounded-full" />
                  </div>
                  <div className="flex justify-center">
                    <Skeleton className="h-5 w-5 rounded-full" />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* FAQ Section Skeleton */}
      <div className="mt-16 md:mt-20 w-full">
        <div className="text-center mb-6 md:mb-8">
          <Skeleton className="h-8 w-80 mx-auto mb-3" />
        </div>
        
        <div className="space-y-4 w-full max-w-4xl mx-auto">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
              <CardContent className="p-4 sm:p-6">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

// Mobile-specific skeleton for smaller screens
export function SubscriptionMobileSkeleton({ isCollapsed = false }: SubscriptionSkeletonProps) {
  return (
    <div
      className={`transition-all duration-500 ease-in-out p-3 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden`}
    >
      {/* Mobile Header */}
      <div className="text-center mb-6">
        <Skeleton className="h-5 w-24 mx-auto mb-3 rounded-full" />
        <Skeleton className="h-8 w-48 mx-auto mb-3" />
        <Skeleton className="h-4 w-full" />
      </div>

      {/* Mobile Current Subscription */}
      <Card className="mb-6 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
        <CardContent className="p-4">
          <div className="flex items-center gap-3 mb-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-5 w-32" />
          </div>
          <Skeleton className="h-4 w-48" />
        </CardContent>
      </Card>

      {/* Mobile Billing Toggle */}
      <div className="flex justify-center mb-6">
        <Skeleton className="h-10 w-64 rounded-full" />
      </div>

      {/* Mobile Pricing Cards */}
      <div className="space-y-4 mb-8">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
            <CardHeader className="p-4 pb-2">
              <div className="text-center space-y-2">
                <Skeleton className="h-5 w-20 mx-auto" />
                <Skeleton className="h-6 w-16 mx-auto" />
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-2">
              <div className="space-y-2">
                {[1, 2, 3].map((j) => (
                  <div key={j} className="flex items-center gap-2">
                    <Skeleton className="h-3 w-3 rounded-full" />
                    <Skeleton className="h-3 w-full" />
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="p-4 pt-2">
              <Skeleton className="h-8 w-full rounded-md" />
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Mobile Subscribe Button */}
      <div className="mb-6">
        <Skeleton className="h-12 w-full rounded-lg" />
      </div>

      {/* Mobile Security Note */}
      <div className="text-center mb-8">
        <Skeleton className="h-3 w-56 mx-auto" />
      </div>
    </div>
  );
}
