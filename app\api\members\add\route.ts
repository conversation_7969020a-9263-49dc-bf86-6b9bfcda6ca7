import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspaceAdminPermission } from "../utils/auth";
import { sendInvitationEmail } from "../utils/email";

/**
 * Add a member to a workspace
 * @route POST /api/members/add
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    const { data } = body;
    
    if (!data || !data.email || !data.role || !data.status) {
      return NextResponse.json({ 
        error: "Email, role, and status are required" 
      }, { status: 400 });
    }
    
    const { email, role, status } = data;
    
    // Check if user has permission to add members to this workspace
    const permission = await checkWorkspaceAdminPermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Check if member already exists
    const { data: existingMember, error: existingError } = await supabase
      .from("workspace_members")
      .select("*")
      .eq("workspace_id", workspaceId)
      .eq("email", email);
    
    if (existingMember && existingMember.length > 0) {
      return NextResponse.json({
        error: "User is already a member of this workspace"
      }, { status: 400 });
    }
    
    // Add member to workspace
    const { data: newMember, error } = await supabase
      .from("workspace_members")
      .insert({
        workspace_id: workspaceId,
        role: role,
        added_by: user.id,
        email: email,
        status: status,
      })
      .select('*')
      .single();
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    // Send invitation email
    const emailSent = await sendInvitationEmail(email, workspaceId, status);
    
    if (!emailSent) {
      console.warn("Failed to send invitation email to:", email);
    }
    
    return NextResponse.json({ 
      data: newMember,
      emailSent
    }, { status: 201 });
  } catch (error) {
    console.error("Error adding member:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
