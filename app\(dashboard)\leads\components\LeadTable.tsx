import React from "react";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Lead, SortField, SortDirection, WorkspaceMember, Status } from "../types/leads";
import { LeadTableRow } from "./LeadTableRow";
import { LeadMobileCard } from "./LeadMobileCard";

interface LeadTableProps {
  leads: Lead[];
  selectedLeads: number[];
  expandedRow: number | null;
  sortField: SortField;
  sortDirection: SortDirection;
  workspaceMembers?: { data: WorkspaceMember[] };
  statusData?: { data: Status[] };
  lastLeadElementRef?: (node: HTMLElement | null) => void;
  onToggleLeadSelection: (leadId: number) => void;
  onToggleSelectAll: () => void;
  onSortChange: (field: SortField) => void;
  onToggleRow: (id: number) => void;
  onEditLead: (lead: Lead) => void;
  onViewLead: (id: number) => void;
  onStatusChange: (id: number, value: string) => void;
  onAssignChange: (id: number, value: string) => void;
  onInitiateContact: (lead: Lead, method: string) => void;
  isAllSelected: boolean;
}

export const LeadTable: React.FC<LeadTableProps> = ({
  leads,
  selectedLeads,
  expandedRow,
  sortField,
  sortDirection,
  workspaceMembers,
  statusData,
  lastLeadElementRef,
  onToggleLeadSelection,
  onToggleSelectAll,
  onSortChange,
  onToggleRow,
  onEditLead,
  onViewLead,
  onStatusChange,
  onAssignChange,
  onInitiateContact,
  isAllSelected,
}) => {
  const getSortIcon = (field: SortField) => {
    if (sortField === field) {
      return sortDirection === 'asc' ? (
        <ChevronUp className="h-3 w-3" />
      ) : (
        <ChevronDown className="h-3 w-3" />
      );
    }
    return <div className="h-3 w-3" />;
  };

  return (
    <div className="text-xs">
      <Table className="text-xs">
        <TableHeader className="hidden md:table-header-group">
          <TableRow className="border-b border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-black">
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={onToggleSelectAll}
                className="h-4 w-4"
              />
            </TableHead>

            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              <div className="flex items-center">
                Name
                <button
                  onClick={() => onSortChange('name')}
                  className="ml-1 p-0.5 rounded-sm hover:bg-gray-200 dark:hover:bg-gray-800"
                >
                  {getSortIcon('name')}
                </button>
              </div>
            </TableHead>

            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              Email
            </TableHead>
            
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              Phone
            </TableHead>

            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              <div className="flex items-center">
                Generated At
                <button
                  onClick={() => onSortChange('created_at')}
                  className="ml-1 p-0.5 rounded-sm hover:bg-gray-200 dark:hover:bg-gray-800"
                >
                  {getSortIcon('created_at')}
                </button>
              </div>
            </TableHead>

            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              Actions
            </TableHead>
            
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              Status
            </TableHead>
            
            <TableHead className="py-3 px-4 text-left font-medium text-black dark:text-white">
              Assign
            </TableHead>
          </TableRow>
        </TableHeader>
        
        <TableBody>
          {/* Mobile View */}
          {leads.map((lead) => (
            <LeadMobileCard
              key={`${lead.id}-mobile`}
              lead={lead}
              isSelected={selectedLeads.includes(lead.id)}
              isExpanded={expandedRow === lead.id}
              workspaceMembers={workspaceMembers}
              statusData={statusData}
              onToggleSelection={() => onToggleLeadSelection(lead.id)}
              onToggleExpand={() => onToggleRow(lead.id)}
              onEdit={() => onEditLead(lead)}
              onView={() => onViewLead(lead.id)}
              onStatusChange={(value) => onStatusChange(lead.id, value)}
              onAssignChange={(value) => onAssignChange(lead.id, value)}
              onInitiateContact={(method) => onInitiateContact(lead, method)}
            />
          ))}

          {/* Desktop View */}
          {leads.map((lead, index) => (
            <LeadTableRow
              key={`${lead.id}-desktop`}
              lead={lead}
              isSelected={selectedLeads.includes(lead.id)}
              workspaceMembers={workspaceMembers}
              statusData={statusData}
              ref={index === leads.length - 1 ? lastLeadElementRef : null}
              onToggleSelection={() => onToggleLeadSelection(lead.id)}
              onEdit={() => onEditLead(lead)}
              onView={() => onViewLead(lead.id)}
              onStatusChange={(value) => onStatusChange(lead.id, value)}
              onAssignChange={(value) => onAssignChange(lead.id, value)}
              onInitiateContact={(method) => onInitiateContact(lead, method)}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
