export type IntegrationStatus = "active" | "beta" | "coming-soon";

export type IntegrationCategory = 
  | "CRM" 
  | "Marketing" 
  | "Automation" 
  | "Email Marketing" 
  | "Communication" 
  | "Analytics";

export type FilterType = "all" | IntegrationStatus;

export interface PlatformIntegration {
  id: string;
  name: string;
  category: IntegrationCategory;
  description: string;
  status: IntegrationStatus;
  documentationUrl?: string;
  logoUrl?: string;
}

export interface IntegrationFilters {
  searchTerm: string;
  activeFilter: FilterType;
}

export interface IntegrationState {
  platforms: PlatformIntegration[];
  filteredPlatforms: PlatformIntegration[];
  expandedRow: string | null;
  filters: IntegrationFilters;
  isLoading: boolean;
}

export interface IntegrationProps {
  isCollapsed: boolean;
  workspaceData: any;
  isWorkspaceLoading: boolean;
}

export interface IntegrationTableProps {
  platforms: PlatformIntegration[];
  expandedRow: string | null;
  onToggleRow: (id: string) => void;
  onOpenDocumentation: (url: string) => void;
}

export interface IntegrationFiltersProps {
  searchTerm: string;
  activeFilter: FilterType;
  onSearchChange: (term: string) => void;
  onFilterChange: (filter: FilterType) => void;
}

export interface IntegrationSkeletonProps {
  isCollapsed: boolean;
}

export interface StatusBadgeProps {
  status: IntegrationStatus;
}

export interface DocumentationButtonProps {
  url?: string;
  size?: "sm" | "default" | "lg";
  variant?: "default" | "outline" | "ghost";
}
