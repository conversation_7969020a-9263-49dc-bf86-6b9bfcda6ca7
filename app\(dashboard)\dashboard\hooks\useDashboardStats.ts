import { useMemo } from "react";
import { DashboardStat } from "../types/dashboard";
import { formatRevenue, formatConversionRate } from "../utils/dashboardUtils";

interface UseDashboardStatsProps {
  workspaceRevenue: any;
  qualifiedCount: any;
  workspaceCount: any;
  ROC: any;
}

export const useDashboardStats = ({
  workspaceRevenue,
  qualifiedCount,
  workspaceCount,
  ROC,
}: UseDashboardStatsProps) => {
  const stats: DashboardStat[] = useMemo(() => {
    const revenue = formatRevenue(workspaceRevenue?.totalRevenue);
    const qualified = qualifiedCount?.qualifiedLeadsCount || 0;
    const totalLeads = workspaceCount?.arrivedLeadsCount || 0;
    const conversionRate = formatConversionRate(ROC?.conversion_rate);

    return [
      {
        icon: "DollarSign",
        title: "Total Revenue",
        value: `$${revenue}`,
        change: workspaceRevenue?.change || "+0%",
        trend: "up" as const,
        note: "from last month"
      },
      {
        icon: "Target",
        title: "Qualified Leads",
        value: qualified.toString(),
        change: "+12%",
        trend: "up" as const,
        note: "from last month"
      },
      {
        icon: "Users",
        title: "Total Leads",
        value: totalLeads.toString(),
        change: "+8%",
        trend: "up" as const,
        note: "from last month"
      },
      {
        icon: "TrendingUp",
        title: "Conversion Rate",
        value: conversionRate,
        change: "+2.1%",
        trend: "up" as const,
        note: "from last month"
      }
    ];
  }, [workspaceRevenue, qualifiedCount, workspaceCount, ROC]);

  return { stats };
};
