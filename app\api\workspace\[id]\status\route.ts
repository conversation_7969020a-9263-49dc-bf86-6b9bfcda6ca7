import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../../utils/auth";

/**
 * Update workspace status (active/inactive)
 * @route PUT /api/workspace/[id]/status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    // Parse request body
    const body = await request.json();
    const { status } = body;
    
    if (status === undefined) {
      return NextResponse.json({ error: "Status is required" }, { status: 400 });
    }
    
    try {
      // First deactivate all workspaces for this user
      const { error: resetError } = await supabase
        .from("workspace_members")
        .update({ is_active: false })
        .eq("user_id", user.id);
      
      if (resetError) throw resetError;
      
      // Then activate only the specified workspace
      const { data: activated, error: activateError } = await supabase
        .from("workspace_members")
        .update({ is_active: status })
        .eq("user_id", user.id)
        .eq("workspace_id", id)
        .select(`
          workspace_id,
          role,
          workspaces (*)
        `)
        .single();
      
      if (activateError) throw activateError;
      
      return NextResponse.json({
        message: "Workspace activated successfully",
        activated
      }, { status: 200 });
    } catch (error) {
      console.error("Error setting active workspace:", error);
      return NextResponse.json({ 
        error: "Failed to set active workspace" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error updating workspace status:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
