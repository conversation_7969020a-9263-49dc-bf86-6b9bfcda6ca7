export interface Lead {
  id: number;
  Name: string;
  email: string;
  phone: string;
  company?: string;
  position?: string;
  contact_method: "WhatsApp" | "SMS" | "Call";
  owner?: string;
  // New ID-based fields
  status_id?: number;
  assigned_to_id?: number;
  // Legacy fields for backward compatibility (will be deprecated)
  status?: {
    name: string;
    color: string;
  };
  assign_to?: {
    name: string;
    role: string;
  };
  revenue?: number;
  createdAt: string;
  isDuplicate: boolean;
  is_email_valid: boolean;
  is_phone_valid: boolean;
  sourceId?: string | null;
}

export interface LeadFilters {
  leadSource: string;
  owner: string;
  status: string;
  contact_method: string;
  contactType: string;
  startDate: string;
  endDate: string;
  showDuplicates: boolean;
}

export interface LeadFormData {
  name: string;
  email: string;
  phone: string;
  company?: string;
  position?: string;
  contact_method: "WhatsApp" | "SMS" | "Call";
  revenue?: number;
}

export interface WorkspaceMember {
  id?: number;
  name: string;
  role: string;
  email?: string;
}

export interface Status {
  id?: number;
  name: string;
  color: string;
}

export interface LeadSource {
  id: string;
  name: string;
  webhook_url: string;
  created_at: string;
}

export interface PaginationInfo {
  total: number;
  hasMore: boolean;
}

export interface LeadsApiResponse {
  data: Lead[];
  pagination?: PaginationInfo;
  count?: number;
  leads?: Lead[];
}

export type DialogMode = "create" | "edit" | "delete" | null;

export type SortField = "name" | "created_at" | "email" | "phone";
export type SortDirection = "asc" | "desc";
