import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs";
import { NextRequest, NextResponse } from "next/server";
import { cleanupInactiveSubscriptions } from '../lib/supabaseServer';
import { runAllCleanupOperations } from '../lib/cleanup';

export async function middleware(req: NextRequest) {
  // Initialize response
  const res = NextResponse.next();

  // Create Supabase client
  const supabase = createMiddlewareClient({ req, res });

  const {
    data: { session },
  } = await supabase.auth.getSession();

  const isAuthRoute = ["/login", "/signup"].includes(req.nextUrl.pathname);

  // If user is already logged in and on an auth route, redirect to dashboard
  if (session && isAuthRoute) {
    return NextResponse.redirect(new URL("/dashboard", req.url));
  }

  // If user is not logged in and trying to access a protected route, redirect to login
  if (!session && !isAuthRoute) {
    return NextResponse.redirect(new URL("/login", req.url));
  }

  // Clean up any realtime subscriptions to prevent memory leaks
  if (req.nextUrl.pathname.startsWith('/api/')) {
    // Only clean up inactive subscriptions rather than all subscriptions
    // This is less disruptive to active users
    cleanupInactiveSubscriptions(10 * 60 * 1000); // Clean up subscriptions inactive for 10+ minutes
  }
  
  // Periodically run all cleanup operations (approximately once per hour)
  // Use a random check to distribute cleanup operations and prevent thundering herd
  if (Math.random() < 0.001) { // ~0.1% chance per request
    runAllCleanupOperations().catch(error => {
      console.error('Error in periodic cleanup:', error);
    });
  }

  return res;
}

export const config = {
  matcher: [
    "/dashboard/:path*", // Protect dashboard and its subroutes
    "/profile/:path*", // Protect profile routes
    "/settings/:path*", // Protect settings
    "/api/:path*",      // Apply optimization to API routes
  ],
};
