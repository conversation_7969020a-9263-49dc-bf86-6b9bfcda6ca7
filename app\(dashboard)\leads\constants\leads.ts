import * as z from "zod";
import { LeadFilters } from "../types/leads";

// Zod validation schema for lead
export const leadSchema = z.object({
  name: z.string().min(2, { message: "First name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().regex(/^\+?[1-9]\d{9,14}$/, "Invalid phone number"),
  company: z.string().optional(),
  position: z.string().optional(),
  contact_method: z.enum(["WhatsApp", "SMS", "Call"], {
    required_error: "Please select a contact method",
  }),
  revenue: z.number().optional(),
});

export const initialFilters: LeadFilters = {
  leadSource: "",
  owner: "",
  status: "",
  contact_method: "",
  contactType: "",
  startDate: "",
  endDate: "",
  showDuplicates: false,
};

export const LEADS_PER_PAGE = 12;

export const CONTACT_METHODS = [
  { value: "WhatsApp", label: "WhatsApp", icon: "Send" },
  { value: "SMS", label: "SMS", icon: "MessageCircle" },
  { value: "Call", label: "Call", icon: "Phone" },
] as const;

export const CONTACT_TYPES = [
  { value: "phone", label: "Phone" },
  { value: "email", label: "Email" },
  { value: "id", label: "ID" },
] as const;

export const SORT_OPTIONS = [
  { value: "name", label: "Name" },
  { value: "created_at", label: "Created Date" },
  { value: "email", label: "Email" },
  { value: "phone", label: "Phone" },
] as const;
