import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../../../utils/auth";
import { getQualifiedLeadsCount } from "../../../utils/analytics";

/**
 * Get qualified leads count for a workspace
 * @route GET /api/workspace/leads/qualified/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const workspaceId = params.workspaceId;
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to view this workspace
    const permission = await checkWorkspacePermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    try {
      // Get qualified leads count for the workspace
      const qualifiedLeadsData = await getQualifiedLeadsCount(workspaceId);
      
      return NextResponse.json(qualifiedLeadsData, { status: 200 });
    } catch (error) {
      console.error("Error getting qualified leads count:", error);
      return NextResponse.json({ 
        error: "Failed to get qualified leads count" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching qualified leads count:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
