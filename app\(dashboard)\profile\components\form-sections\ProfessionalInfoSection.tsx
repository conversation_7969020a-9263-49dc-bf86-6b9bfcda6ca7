"use client";

import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ProfileFormData } from '../../types/profile';

interface ProfessionalInfoSectionProps {
  formData: ProfileFormData;
  updateFormData: (field: keyof ProfileFormData, value: string) => void;
}

export function ProfessionalInfoSection({
  formData,
  updateFormData,
}: ProfessionalInfoSectionProps) {
  return (
    <>
      {/* Job Title and Department */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">Job Title</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => updateFormData('title', e.target.value)}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="department">Department</Label>
          <Input
            id="department"
            value={formData.department}
            onChange={(e) => updateFormData('department', e.target.value)}
          />
        </div>
      </div>

      {/* Company and Start Date */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="company">Company</Label>
          <Input
            id="company"
            value={formData.company}
            onChange={(e) => updateFormData('company', e.target.value)}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="startDate">Start Date</Label>
          <Input
            id="startDate"
            type="date"
            value={formData.startDate}
            onChange={(e) => updateFormData('startDate', e.target.value)}
          />
        </div>
      </div>
    </>
  );
}
