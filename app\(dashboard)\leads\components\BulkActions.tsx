import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { CheckSquare, X, Trash2 } from "lucide-react";
import { DialogMode } from "../types/leads";

interface BulkActionsProps {
  selectedLeads: number[];
  onDeselectAll: () => void;
  onSetDialogMode: (mode: DialogMode) => void;
}

export const BulkActions: React.FC<BulkActionsProps> = ({
  selectedLeads,
  onDeselectAll,
  onSetDialogMode,
}) => {
  if (selectedLeads.length === 0) return null;

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between mb-6 p-4 bg-gray-50 dark:bg-black rounded-lg border border-gray-200 dark:border-gray-800 shadow-sm">
      <div className="flex items-center mb-3 sm:mb-0">
        <div className="bg-black/10 dark:bg-white/10 p-2 rounded-full mr-3">
          <CheckSquare className="h-5 w-5 text-black dark:text-white" />
        </div>
        <span className="font-medium text-black dark:text-white">
          {selectedLeads.length} leads selected
        </span>
      </div>
      
      <div className="flex space-x-3 w-full sm:w-auto">
        <Button
          variant="outline"
          size="sm"
          onClick={onDeselectAll}
          className="flex-1 sm:flex-none border-gray-200 dark:border-gray-800 text-black dark:text-white"
        >
          <X className="mr-2 h-4 w-4" />
          Clear Selection
        </Button>
        <Button
          variant="destructive"
          size="sm"
          onClick={() => onSetDialogMode("delete")}
          className="flex-1 sm:flex-none bg-black text-white dark:bg-white dark:text-black hover:bg-black/90 dark:hover:bg-white/90"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete Selected
        </Button>
      </div>
    </div>
  );
};
