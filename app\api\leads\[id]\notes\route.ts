import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, notifyLeadChange } from "../../utils";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Lead ID is required" }, { status: 400 });
    }
    
    // Fetch notes for the lead
    const { data, error } = await supabase
      .from("leads")
      .select("text_area")
      .eq("id", id);
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    if (!data || data.length === 0) {
      return NextResponse.json({ error: "No notes found for this lead" }, { status: 404 });
    }
    
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching lead notes:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    if (!id) {
      return NextResponse.json({ error: "Lead ID is required" }, { status: 400 });
    }
    
    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    
    if (!body) {
      return NextResponse.json({ error: "Update data is required in 'text_area' field" }, { status: 400 });
    }
    
    // Update the lead notes
    const { data, error } = await supabase
      .from("leads")
      .update({ text_area: body })
      .eq("id", id)
      .select();
    
    if (error) {
      console.error("Supabase Update Error:", error.message);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    // Create notification
    await notifyLeadChange(
      id,
      "notes_updated",
      user.id,
      workspaceId,
      { 
        lead_id: id,
        updated_by: user.id
      }
    );
    
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error updating lead notes:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
