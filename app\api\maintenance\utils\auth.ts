import { NextRequest } from "next/server";

/**
 * Authenticates a maintenance request using the API key
 * @param request The Next.js request object
 * @returns Object indicating if the request is authorized
 */
export function authenticateMaintenanceRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const maintenanceKey = process.env.MAINTENANCE_API_KEY;
  
  if (!authHeader || !maintenanceKey || authHeader !== `Bearer ${maintenanceKey}`) {
    return { 
      isAuthorized: false, 
      error: "Unauthorized", 
      status: 401 
    };
  }
  
  return { 
    isAuthorized: true, 
    error: null, 
    status: 200 
  };
}
