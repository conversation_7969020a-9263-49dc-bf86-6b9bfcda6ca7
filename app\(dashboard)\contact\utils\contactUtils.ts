import { Contact, ContactFilters, Tag } from '../types/contact';
import { CONTACT_CONSTANTS } from '../constants/contact';

/**
 * Filter contacts based on search and status criteria
 */
export const filterContacts = (
  contacts: Contact[],
  filters: ContactFilters
): Contact[] => {
  if (!Array.isArray(contacts)) return [];

  return contacts.filter((contact) => {
    const searchLower = filters.search.toLowerCase();
    const statusLower = filters.statusFilter.toLowerCase();

    const matchesSearch =
      contact?.Name?.toLowerCase().includes(searchLower) ||
      contact?.name?.toLowerCase().includes(searchLower) ||
      contact?.email?.toLowerCase().includes(searchLower) ||
      contact?.phone?.includes(filters.search);

    const matchesStatus =
      filters.statusFilter === CONTACT_CONSTANTS.STATUS_OPTIONS.ALL ||
      contact?.status?.name?.toLowerCase() === statusLower;

    return matchesSearch && matchesStatus;
  });
};

/**
 * Paginate contacts array
 */
export const paginateContacts = (
  contacts: Contact[],
  currentPage: number,
  itemsPerPage: number
): { paginatedContacts: Contact[]; totalPages: number; startIndex: number } => {
  const totalPages = Math.ceil(contacts.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedContacts = contacts.slice(startIndex, startIndex + itemsPerPage);

  return {
    paginatedContacts,
    totalPages,
    startIndex,
  };
};

/**
 * Transform lead data to contact format
 */
export const transformLeadToContact = (lead: any, index: number): Contact => {
  return {
    id: lead.id || index + 1,
    Name: lead.name || "",
    email: lead.email || "",
    phone: lead.phone || "",
    company: lead.company || "",
    position: lead.position || "",
    contact_method: lead.contact_method,
    owner: lead.owner || "Unknown",
    status: lead.status || { name: "New" },
    revenue: lead.revenue || 0,
    assign_to: lead.assign_to || "Not Assigned",
    createdAt: lead.created_at
      ? new Date(lead.created_at).toISOString()
      : new Date().toISOString(),
    isDuplicate: false,
    is_email_valid: lead.is_email_valid,
    is_phone_valid: lead.is_phone_valid,
    sourceId: lead?.lead_source_id ?? null,
    businessInfo: lead?.businessInfo ?? "",
    tag: lead?.tags ?? {},
    address: lead?.address ?? "",
  };
};

/**
 * Detect and mark duplicate contacts
 */
export const markDuplicateContacts = (contacts: Contact[]): Contact[] => {
  const duplicates = new Set<number>();
  
  contacts.forEach((contact) => {
    const duplicate = contacts.find(
      (c) =>
        c.id !== contact.id &&
        (c.email === contact.email || c.phone === contact.phone)
    );
    if (duplicate) {
      duplicates.add(contact.id);
      duplicates.add(duplicate.id);
    }
  });

  return contacts.map((contact) => ({
    ...contact,
    isDuplicate: duplicates.has(contact.id),
  }));
};

/**
 * Sort contacts by creation date (most recent first)
 */
export const sortContactsByDate = (contacts: Contact[]): Contact[] => {
  return contacts.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
};

/**
 * Filter contacts by status for qualified contacts
 */
export const filterQualifiedContacts = (
  contacts: Contact[],
  contactStatuses: Set<string>
): Contact[] => {
  return contacts.filter((contact) =>
    contactStatuses.has(contact.status.name)
  );
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): boolean => {
  return CONTACT_CONSTANTS.VALIDATION.EMAIL_REGEX.test(email);
};

/**
 * Validate phone format
 */
export const validatePhone = (phone: string): boolean => {
  return CONTACT_CONSTANTS.VALIDATION.PHONE_REGEX.test(phone);
};

/**
 * Sanitize phone number for external services
 */
export const sanitizePhoneNumber = (phone: string): string => {
  return phone.replace(/\D/g, "");
};

/**
 * Generate contact methods URLs
 */
export const generateContactUrls = (contact: Contact) => {
  const sanitizedPhone = sanitizePhoneNumber(contact.phone);
  
  return {
    whatsapp: `https://wa.me/${sanitizedPhone}`,
    call: `tel:${contact.phone}`,
    sms: `sms:${contact.phone}`,
    email: `mailto:${contact.email}`,
    gmail: `https://mail.google.com/mail/?view=cm&fs=1&to=${contact.email}`,
  };
};

/**
 * Handle tag operations
 */
export const handleTagOperations = {
  add: (currentTags: string[], newTag: string): string[] => {
    return currentTags.includes(newTag) 
      ? currentTags.filter(tag => tag !== newTag)
      : [...currentTags, newTag];
  },
  
  remove: (currentTags: string[], tagToRemove: string): string[] => {
    return currentTags.filter(tag => tag !== tagToRemove);
  },
  
  parse: (tagData: any): string[] => {
    try {
      return Array.isArray(tagData) ? tagData : JSON.parse(tagData || "[]");
    } catch {
      return [];
    }
  },
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString();
  } catch {
    return "Invalid Date";
  }
};

/**
 * Check if update data is valid (not empty)
 */
export const isValidUpdateData = (updateData: any): boolean => {
  return !(
    updateData.businessInfo === undefined &&
    (!updateData.tags || updateData.tags.length === 0) &&
    !updateData.address?.trim() &&
    !updateData.email?.trim() &&
    !updateData.phone?.trim() &&
    updateData.is_email_valid === undefined &&
    !updateData.name?.trim()
  );
};

/**
 * Create table headers configuration
 */
export const createTableHeadersConfig = (selectedHeaders: string[]) => {
  return CONTACT_CONSTANTS.TABLE_HEADERS.map(header => ({
    key: header.toLowerCase().replace(/\s+/g, '_'),
    label: header,
    width: CONTACT_CONSTANTS.DEFAULT_COLUMN_WIDTHS[header as keyof typeof CONTACT_CONSTANTS.DEFAULT_COLUMN_WIDTHS] || 150,
    visible: selectedHeaders.includes(header),
    sortable: ['Name', 'Email', 'Phone'].includes(header),
    editable: !['Platform'].includes(header),
  }));
};

/**
 * Get available columns for adding
 */
export const getAvailableColumns = (selectedHeaders: string[]): string[] => {
  return CONTACT_CONSTANTS.TABLE_HEADERS.filter(
    header => !selectedHeaders.includes(header)
  );
};
