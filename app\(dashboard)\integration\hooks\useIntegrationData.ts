import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";
import { useGetActiveWorkspaceQuery } from "@/lib/store/services/workspace";
import { PlatformIntegration } from "../types/integration";
import { MOCK_PLATFORMS } from "../constants/integration";

export const useIntegrationData = () => {
  // Redux state
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);
  
  // API queries
  const { 
    data: workspaceData, 
    isLoading: isWorkspaceLoading,
    error: workspaceError 
  } = useGetActiveWorkspaceQuery();

  // Local state for platforms (in real app, this would come from API)
  const [platforms, setPlatforms] = useState<PlatformIntegration[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize platforms data
  useEffect(() => {
    // Simulate API call delay
    const timer = setTimeout(() => {
      setPlatforms(MOCK_PLATFORMS);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Determine if initial loading
  const isInitialLoading = isWorkspaceLoading || isLoading;

  return {
    // Workspace data
    workspaceData,
    isWorkspaceLoading,
    workspaceError,
    isCollapsed,

    // Platforms data
    platforms,
    setPlatforms,

    // Loading states
    isLoading,
    isInitialLoading,
    setIsLoading,
  };
};
