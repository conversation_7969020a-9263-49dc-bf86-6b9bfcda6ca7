import { NextRequest, NextResponse } from "next/server";
import { authenticateUser } from "../utils/auth";
import { acceptInvitation, validateInvitationToken } from "../utils/invitation";

/**
 * Accept a workspace invitation using secure token
 * @route POST /api/members/accept-invitation
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get token from request body
    const { token } = await request.json();
    
    if (!token) {
      return NextResponse.json({ 
        error: "Invitation token is required" 
      }, { status: 400 });
    }
    
    // Accept the invitation
    const result = await acceptInvitation(token, user.id, user.email);
    
    if (!result.success) {
      return NextResponse.json({ 
        error: result.error 
      }, { status: 400 });
    }
    
    return NextResponse.json({
      message: "Invitation accepted successfully",
      data: result.member
    }, { status: 200 });
    
  } catch (error) {
    console.error("Error accepting invitation:", error);
    return NextResponse.json({ 
      error: "Internal server error" 
    }, { status: 500 });
  }
}

/**
 * Get invitation details using token (for preview before accepting)
 * @route GET /api/members/accept-invitation?token=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get("token");
    
    if (!token) {
      return NextResponse.json({ 
        error: "Invitation token is required" 
      }, { status: 400 });
    }
    
    // Validate token and get invitation details
    const validation = await validateInvitationToken(token);
    
    if (!validation.valid) {
      return NextResponse.json({ 
        error: validation.error 
      }, { status: 400 });
    }
    
    return NextResponse.json({
      data: validation.invitation
    }, { status: 200 });
    
  } catch (error) {
    console.error("Error validating invitation:", error);
    return NextResponse.json({ 
      error: "Internal server error" 
    }, { status: 500 });
  }
}
