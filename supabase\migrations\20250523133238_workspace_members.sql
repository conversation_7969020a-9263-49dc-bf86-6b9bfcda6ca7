-- Migration for table: workspace_members
CREATE TABLE IF NOT EXISTS "workspace_members" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "workspace_id" integer,
  "user_id" uuid NOT NULL,
  "role" text,
  "status" text,
  "profile_image" text,
  "added_by" uuid,
  "email" text,
  "last_active" boolean,
  "name" text,
  "updated_at" timestamp with time zone,
  "is_active" boolean
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "workspace_members_user_id_idx" ON "workspace_members" ("user_id");

-- Create index on workspace_id
CREATE INDEX IF NOT EXISTS "workspace_members_workspace_id_idx" ON "workspace_members" ("workspace_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "workspace_members" ADD CONSTRAINT "fk_workspace_members_user_id"
--   FOREIGN KEY ("user_id") REFERENCES "auth.users" (id);

-- Add foreign key constraint for workspace_id (commented out, uncomment after verifying)
-- ALTER TABLE "workspace_members" ADD CONSTRAINT "fk_workspace_members_workspace_id"
--   FOREIGN KEY ("workspace_id") REFERENCES "workspaces" (id);

-- Enable Row Level Security
ALTER TABLE "workspace_members" ENABLE ROW LEVEL SECURITY;

