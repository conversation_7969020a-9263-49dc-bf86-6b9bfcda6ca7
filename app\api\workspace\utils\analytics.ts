import { supabase } from "@/lib/supabaseServer";

/**
 * Calculate analytics for a workspace
 * @param workspaceId The workspace ID to get analytics for
 * @returns Object containing analytics data
 */
export async function calculateWorkspaceAnalytics(workspaceId: string) {
  try {
    // Fetch webhooks for the workspace
    const { data: webhooks, error: webhooksError } = await supabase
      .from("webhooks")
      .select("*")
      .eq("workspace_id", workspaceId);

    if (webhooksError) {
      console.error("Error fetching webhooks:", webhooksError);
      throw new Error("Failed to fetch webhooks");
    }

    // Get lead counts for each webhook
    const webhookAnalytics = await Promise.all(
      webhooks.map(async (webhook) => {
        // Extract source_id from webhook URL
        let sourceId = null;
        try {
          const url = new URL(webhook.webhook_url);
          sourceId = url.searchParams.get("sourceId");
        } catch (error) {
          console.error(`Invalid webhook URL: ${webhook.webhook_url}`, error);
        }

        if (!sourceId) {
          return {
            webhook_name: webhook.name,
            lead_count: 0,
            webhook_url: webhook.webhook_url,
          };
        }

        // Fetch leads count for this webhook's source
        const { count, error: leadsError } = await supabase
          .from("leads")
          .select("*", { count: "exact", head: true })
          .eq("work_id", workspaceId)
          .eq("lead_source_id", sourceId);

        if (leadsError) {
          console.error(
            `Error fetching leads for webhook ${webhook.name}:`,
            leadsError
          );
          return {
            webhook_name: webhook.name,
            lead_count: 0,
            webhook_url: webhook.webhook_url,
            error: "Failed to fetch leads count",
          };
        }

        return {
          webhook_name: webhook.name,
          lead_count: count || 0,
          webhook_url: webhook.webhook_url,
        };
      })
    );

    return webhookAnalytics;
  } catch (error) {
    console.error("Error calculating workspace analytics:", error);
    throw error;
  }
}

/**
 * Calculate revenue for a workspace
 * @param workspaceId The workspace ID to get revenue for
 * @returns Object containing revenue data
 */
export async function calculateWorkspaceRevenue(workspaceId: string) {
  try {
    // Validate workspaceId
    if (!workspaceId) {
      console.error("Invalid workspace ID provided to calculateWorkspaceRevenue");
      return { totalRevenue: 0, change: "+0%" };
    }

    // Convert workspaceId to number if needed
    // The database function expects a string, but we need to make sure it's a valid number
    const workspaceIdNumber = parseInt(workspaceId);

    if (isNaN(workspaceIdNumber)) {
      console.error("Invalid workspace ID format:", workspaceId);
      return { totalRevenue: 0, change: "+0%" };
    }

    // Call the database function to calculate revenue
    // The database function expects a string but will convert it to a number internally
    console.log("Calculating revenue for workspace ID:", workspaceId, "as number:", workspaceIdNumber);

    const { data, error } = await supabase.rpc(
      "calculate_total_revenue",
      {
        workspace_id: workspaceId,
      }
    );

    // Log the result for debugging
    console.log("Revenue calculation result:", { data, error });

    if (error) {
      console.error("Error calculating revenue:", error);
      return { totalRevenue: 0, change: "+0%" };
    }

    // Calculate month-over-month change (placeholder for now)
    // In a real implementation, you would calculate this from historical data
    const change = "+10%";

    return {
      totalRevenue: data || 0,
      change: change
    };
  } catch (error) {
    console.error("Error calculating workspace revenue:", error);
    // Return a default value instead of throwing to prevent dashboard from breaking
    return {
      totalRevenue: 0,
      change: "+0%",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

/**
 * Get qualified leads count for a workspace
 * @param workspaceId The workspace ID to get qualified leads count for
 * @returns Object containing qualified leads count
 */
export async function getQualifiedLeadsCount(workspaceId: string) {
  try {
    // Validate workspaceId
    if (!workspaceId) {
      console.error("Invalid workspace ID provided to getQualifiedLeadsCount");
      return {
        qualifiedLeadsCount: 0,
        qualifiedStatuses: [],
      };
    }

    // Convert workspaceId to number
    const workspaceIdNumber = parseInt(workspaceId);

    if (isNaN(workspaceIdNumber)) {
      console.error("Invalid workspace ID format:", workspaceId);
      return {
        qualifiedLeadsCount: 0,
        qualifiedStatuses: [],
      };
    }

    // First, get all statuses where count_statistics is true
    const { data: qualifiedStatuses, error: statusError } = await supabase
      .from("status")
      .select("name, color")
      .eq("count_statistics", true)
      .eq("work_id", workspaceIdNumber);

    if (statusError) {
      console.error("Error fetching qualified statuses:", statusError);
      throw statusError;
    }

    // If no qualified statuses found, return 0
    if (!qualifiedStatuses || qualifiedStatuses.length === 0) {
      console.log("No qualified statuses found for workspace:", workspaceId);
      return {
        qualifiedLeadsCount: 0,
        qualifiedStatuses: [],
      };
    }

    // Extract status names that should be counted
    const qualifiedStatusNames = qualifiedStatuses.map(
      (status) => status.name
    );

    // Prepare the filter condition properly
    // If there's only one status, we don't need the IN operator
    let query = supabase
      .from("leads")
      .select("id", { count: "exact" })
      .eq("work_id", workspaceIdNumber);

    if (qualifiedStatusNames.length === 1) {
      query = query.filter("status->>name", "eq", qualifiedStatusNames[0]);
    } else {
      query = query.filter("status->>name", "in", `(${qualifiedStatusNames.join(',')})`);
    }

    // Execute the query
    const { data: leadsCount, error: leadsError, count } = await query;

    if (leadsError) {
      console.error("Error counting qualified leads:", leadsError);
      throw leadsError;
    }

    return {
      qualifiedLeadsCount: count || 0,
      qualifiedStatuses: qualifiedStatusNames,
    };
  } catch (error) {
    console.error("Error getting qualified leads count:", error);
    // Return a default value instead of throwing to prevent dashboard from breaking
    return {
      qualifiedLeadsCount: 0,
      qualifiedStatuses: [],
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
