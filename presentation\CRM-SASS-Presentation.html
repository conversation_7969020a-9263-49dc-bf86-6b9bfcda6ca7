<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM-SASS Project Presentation</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Inter:wght@300;400;500&display=swap');

        :root {
            --primary: #7C5DFA;
            --primary-light: #9277FF;
            --secondary: #0EA5E9;
            --accent: #F59E0B;
            --background: #FFFFFF;
            --text: #1E293B;
            --text-light: #64748B;
            --card-bg: #F8FAFC;
            --card-hover: #F1F5F9;
            --gradient-start: #7C5DFA;
            --gradient-end: #0EA5E9;
            --slide-width: 960px;
            --slide-height: 1000px;
            --box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #F1F5F9;
            color: var(--text);
            line-height: 1.6;
        }

        .presentation-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
        }

        .slide {
            width: var(--slide-width);
            height: var(--slide-height);
            background-color: var(--background);
            margin-bottom: 60px;
            border-radius: 16px;
            box-shadow: var(--box-shadow);
            overflow: hidden;
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .slide:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 30px -10px rgba(0, 0, 0, 0.15);
        }

        .slide-content {
            padding: 50px;
            height: auto;
            display: flex;
            flex-direction: column;
        }

        .title-slide {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .title-slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: 0;
        }

        h1, h2, h3, h4 {
            font-family: 'Montserrat', sans-serif;
            margin: 0;
        }

        h1 {
            font-size: 54px;
            font-weight: 700;
            margin-bottom: 20px;
            letter-spacing: -0.5px;
        }

        h2 {
            font-size: 38px;
            font-weight: 600;
            margin-bottom: 35px;
            position: relative;
            padding-bottom: 15px;
            letter-spacing: -0.5px;
        }

        h2::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 5px;
            background: linear-gradient(90deg, var(--primary), var(--primary-light));
            border-radius: 2.5px;
        }

        h3 {
            font-size: 26px;
            font-weight: 500;
            margin-bottom: 25px;
            color: var(--text-light);
            letter-spacing: -0.3px;
        }

        .title-slide h1 {
            font-size: 68px;
            margin-top: 20px;
            position: relative;
            z-index: 1;
            background: linear-gradient(to right, #ffffff, #E0E7FF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .title-slide h2 {
            font-size: 36px;
            margin-bottom: 15px;
            padding-bottom: 0;
            position: relative;
            z-index: 1;
        }

        .title-slide h2::after {
            display: none;
        }

        .title-slide h3 {
            font-size: 24px;
            opacity: 0.9;
            font-weight: 400;
            color: white;
            position: relative;
            z-index: 1;
        }

        .logo-placeholder {
            width: 170px;
            height: 170px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 35px;
            font-size: 42px;
            font-weight: bold;
            backdrop-filter: blur(5px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
            position: relative;
            z-index: 1;
        }

        ul {
            margin: 0;
            padding: 0 0 0 20px;
        }

        li {
            margin-bottom: 18px;
            font-size: 20px;
            line-height: 1.6;
            position: relative;
        }

        li::marker {
            color: var(--primary);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 30px;
        }

        .feature-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s, background-color 0.3s;
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            background-color: var(--card-hover);
        }

        .feature-card h4 {
            font-size: 22px;
            margin-bottom: 12px;
            color: var(--primary);
            display: flex;
            align-items: center;
        }

        .feature-card h4::before {
            content: '';
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: var(--primary);
            border-radius: 50%;
            margin-right: 10px;
        }

        .feature-card p {
            font-size: 17px;
            margin: 0;
            color: var(--text-light);
        }

        .image-placeholder {
            width: 100%;
            height: 300px;
            background-color: var(--card-bg);
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 25px 0;
            font-size: 18px;
            color: var(--text-light);
            border: 1px dashed rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .image-placeholder::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: radial-gradient(var(--card-hover) 1px, transparent 1px);
            background-size: 20px 20px;
            opacity: 0.5;
        }

        .tech-icons {
            display: flex;
            justify-content: space-around;
            margin: 35px 0;
        }

        .tech-icon {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: transform 0.3s;
        }

        .tech-icon:hover {
            transform: translateY(-5px);
        }

        .icon-placeholder {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, var(--card-bg), var(--card-hover));
            border-radius: 45px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: bold;
            color: var(--primary);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        .slide-number {
            position: absolute;
            bottom: 15px;
            right: 25px;
            font-size: 14px;
            color: var(--text-light);
            font-weight: 500;
            background-color: var(--card-bg);
            padding: 4px 10px;
            border-radius: 20px;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: 100%;
        }

        .column {
            display: flex;
            flex-direction: column;
        }

        .accent-text {
            color: var(--accent);
            font-weight: 600;
        }

        .primary-text {
            color: var(--primary);
            font-weight: 600;
        }

        .secondary-text {
            color: var(--secondary);
            font-weight: 600;
        }

        .contact-info {
            margin-top: 30px;
            text-align: center;
            font-size: 18px;
            background-color: var(--card-bg);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .contact-info p {
            margin: 8px 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .contact-info p::before {
            content: '•';
            color: var(--primary);
            margin-right: 8px;
            font-size: 20px;
        }

        /* Additional modern design elements */
        .glass-morphism {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .highlight-box {
            background: linear-gradient(to right, rgba(124, 93, 250, 0.1), rgba(14, 165, 233, 0.1));
            border-left: 4px solid var(--primary);
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .pill-badge {
            display: inline-block;
            padding: 5px 12px;
            background-color: rgba(124, 93, 250, 0.1);
            color: var(--primary);
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-right: 8px;
        }

        .feature-intro {
            font-size: 18px;
            color: var(--text-light);
            margin-bottom: 25px;
            max-width: 90%;
        }

        /* Enhance the title slide */
        .animated-gradient {
            background: linear-gradient(-45deg, #7C5DFA, #6366F1, #0EA5E9, #14B8A6);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* Additional hover effects */
        .bounce-on-hover:hover {
            animation: bounce 0.5s;
        }

        @keyframes bounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Title Slide -->
        <div class="slide title-slide animated-gradient">
            <div class="logo-placeholder bounce-on-hover">IL</div>
            <h1>INFILABS CRM</h1>
            <h2>Next-Generation Customer Relationship Management</h2>
            <h3>Streamline Your Business Relationships</h3>
            <div class="slide-number">1</div>
        </div>

        <!-- Slide 2: Project Overview -->
        <div class="slide">
            <div class="slide-content">
                <h2>Project Overview</h2>
                <h3>What is CRM-SASS?</h3>

                <p class="feature-intro">A cloud-native customer relationship management platform designed to transform how businesses connect with their customers.</p>

                <div class="highlight-box">
                    <ul>
                        <li><span class="primary-text">Comprehensive</span> customer relationship management platform</li>
                        <li><span class="secondary-text">Cloud-based</span> SaaS solution with multi-workspace support</li>
                        <li><span class="accent-text">Scalable</span> architecture for businesses of all sizes</li>
                        <li><span class="primary-text">Secure</span> data handling with modern authentication</li>
                    </ul>
                </div>

                <div class="image-placeholder">
                    Overview Diagram
                </div>

                <div class="slide-number">2</div>
            </div>
        </div>

        <!-- Slide 3: Key Features -->
        <div class="slide">
            <div class="slide-content">
                <h2>Key Features</h2>
                <h3>Core Capabilities</h3>

                <div class="feature-intro">
                    <span class="pill-badge">All-in-One</span>
                    <span class="pill-badge">Customizable</span>
                    <span class="pill-badge">Scalable</span>
                </div>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>Lead Management</h4>
                        <p>Capture, track, and nurture leads through your sales pipeline</p>
                    </div>
                    <div class="feature-card">
                        <h4>Contact Management</h4>
                        <p>Organize and maintain customer relationships in one place</p>
                    </div>
                    <div class="feature-card">
                        <h4>Workspace Collaboration</h4>
                        <p>Team-based workspaces with role management</p>
                    </div>
                    <div class="feature-card">
                        <h4>Marketing Campaigns</h4>
                        <p>Create and track marketing initiatives for better ROI</p>
                    </div>
                    <div class="feature-card">
                        <h4>Analytics Dashboard</h4>
                        <p>Data-driven insights and reporting for informed decisions</p>
                    </div>
                    <div class="feature-card">
                        <h4>Form Builder</h4>
                        <p>Custom lead capture forms for your website and campaigns</p>
                    </div>
                </div>

                <div class="slide-number">3</div>
            </div>
        </div>

        <!-- Slide 4: Technical Architecture -->
        <div class="slide">
            <div class="slide-content">
                <h2>Technical Architecture</h2>
                <h3>Built with Modern Technologies</h3>

                <div class="tech-icons">
                    <div class="tech-icon bounce-on-hover">
                        <div class="icon-placeholder">Next.js</div>
                        <p>React Framework</p>
                    </div>
                    <div class="tech-icon bounce-on-hover">
                        <div class="icon-placeholder">TS</div>
                        <p>TypeScript</p>
                    </div>
                    <div class="tech-icon bounce-on-hover">
                        <div class="icon-placeholder">Supabase</div>
                        <p>Backend</p>
                    </div>
                    <div class="tech-icon bounce-on-hover">
                        <div class="icon-placeholder">Tailwind</div>
                        <p>CSS Framework</p>
                    </div>
                    <div class="tech-icon bounce-on-hover">
                        <div class="icon-placeholder">Redux</div>
                        <p>State Management</p>
                    </div>
                </div>

                <div class="image-placeholder">
                    Architecture Diagram
                </div>

                <div class="slide-number">4</div>
            </div>
        </div>

        <!-- Slide 5: User Interface -->
        <div class="slide">
            <div class="slide-content">
                <h2>User Interface</h2>
                <h3>Intuitive and Responsive Design</h3>

                <div class="image-placeholder">
                    Dashboard Screenshot
                </div>

                <div class="highlight-box">
                    <ul>
                        <li><span class="primary-text">Clean, modern interface</span> with dark/light mode support</li>
                        <li><span class="secondary-text">Responsive design</span> for desktop and mobile devices</li>
                        <li><span class="accent-text">Customizable dashboard</span> with key metrics</li>
                        <li><span class="primary-text">Accessible UI</span> following best practices</li>
                    </ul>
                </div>

                <div class="slide-number">5</div>
            </div>
        </div>

        <!-- Slide 6: Lead Management -->
        <div class="slide">
            <div class="slide-content">
                <h2>Lead Management</h2>
                <h3>Comprehensive Lead Handling</h3>

                <div class="image-placeholder">
                    Leads Interface Screenshot
                </div>

                <div class="two-column">
                    <div class="column">
                        <div class="feature-card">
                            <h4>Capture & Qualify</h4>
                            <p>Multi-channel lead capture with automated scoring and qualification</p>
                        </div>
                        <div class="feature-card">
                            <h4>Pipeline Management</h4>
                            <p>Custom lead stages and visual pipeline tracking</p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="feature-card">
                            <h4>Lead Assignment</h4>
                            <p>Automated and rule-based lead assignment to team members</p>
                        </div>
                        <div class="feature-card">
                            <h4>Lead Nurturing</h4>
                            <p>Automated follow-ups and engagement tracking</p>
                        </div>
                    </div>
                </div>

                <div class="slide-number">6</div>
            </div>
        </div>

        <!-- Slide 7: Workspace Management -->
        <div class="slide">
            <div class="slide-content">
                <h2>Workspace Management</h2>
                <h3>Collaborative Team Environments</h3>

                <div class="image-placeholder">
                    Workspace Management Screenshot
                </div>

                <div class="two-column">
                    <div class="column">
                        <div class="feature-card">
                            <h4>Multi-workspace Support</h4>
                            <p>Create and manage multiple isolated workspaces for teams or departments</p>
                        </div>
                        <div class="feature-card">
                            <h4>Role-based Access</h4>
                            <p>Granular permission control for team members</p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="feature-card">
                            <h4>Team Collaboration</h4>
                            <p>Real-time updates and notifications for team activities</p>
                        </div>
                        <div class="feature-card">
                            <h4>Secure Invitations</h4>
                            <p>Email invitations with secure onboarding flow</p>
                        </div>
                    </div>
                </div>

                <div class="slide-number">7</div>
            </div>
        </div>

        <!-- Slide 8: API & Integrations -->
        <div class="slide">
            <div class="slide-content">
                <h2>API & Integrations</h2>
                <h3>Extensible Platform</h3>

                <div class="image-placeholder">
                    API & Integrations Diagram
                </div>

                <div class="highlight-box">
                    <ul>
                        <li><span class="primary-text">RESTful API</span> for third-party integrations</li>
                        <li><span class="secondary-text">Webhook support</span> for event-driven architecture</li>
                        <li><span class="accent-text">Integration with popular services</span>:
                            <ul>
                                <li>Email marketing platforms</li>
                                <li>Payment processors</li>
                                <li>AI services (OpenAI, Together API)</li>
                            </ul>
                        </li>
                    </ul>
                </div>

                <div class="slide-number">8</div>
            </div>
        </div>

        <!-- Slide 9: Security Features -->
        <div class="slide">
            <div class="slide-content">
                <h2>Security Features</h2>
                <h3>Enterprise-Grade Security</h3>

                <p class="feature-intro">Security is at the core of our platform design, ensuring your customer data remains protected at all times.</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>Authentication</h4>
                        <p>OAuth 2.0 authentication with secure sessions</p>
                    </div>
                    <div class="feature-card">
                        <h4>Access Control</h4>
                        <p>Role-based permissions for data protection</p>
                    </div>
                    <div class="feature-card">
                        <h4>Data Encryption</h4>
                        <p>Encryption at rest and in transit</p>
                    </div>
                    <div class="feature-card">
                        <h4>API Key Management</h4>
                        <p>Secure storage of third-party service keys</p>
                    </div>
                </div>

                <div class="image-placeholder">
                    Security Architecture Diagram
                </div>

                <div class="slide-number">9</div>
            </div>
        </div>

        <!-- Slide 10: Analytics & Reporting -->
        <div class="slide">
            <div class="slide-content">
                <h2>Analytics & Reporting</h2>
                <h3>Data-Driven Insights</h3>

                <div class="image-placeholder">
                    Analytics Dashboard Screenshot
                </div>

                <div class="two-column">
                    <div class="column">
                        <div class="feature-card">
                            <h4>Real-time Dashboard</h4>
                            <p>Live data visualization with customizable KPIs and metrics</p>
                        </div>
                        <div class="feature-card">
                            <h4>Custom Reports</h4>
                            <p>Intuitive report builder with flexible parameters</p>
                        </div>
                    </div>
                    <div class="column">
                        <div class="feature-card">
                            <h4>Export Options</h4>
                            <p>Export data in multiple formats including CSV, PDF, and Excel</p>
                        </div>
                        <div class="feature-card">
                            <h4>Visual Analytics</h4>
                            <p>Interactive charts and graphs for better data understanding</p>
                        </div>
                    </div>
                </div>

                <div class="slide-number">10</div>
            </div>
        </div>

        <!-- Slide 11: Future Roadmap -->
        <div class="slide">
            <div class="slide-content">
                <h2>Future Roadmap</h2>
                <h3>Upcoming Features</h3>

                <p class="feature-intro">Our commitment to innovation continues with these planned enhancements:</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>AI-powered Lead Scoring</h4>
                        <p>Intelligent lead prioritization based on behavior and engagement patterns</p>
                    </div>
                    <div class="feature-card">
                        <h4>Marketing Automation</h4>
                        <p>Advanced workflow automation for sophisticated marketing campaigns</p>
                    </div>
                    <div class="feature-card">
                        <h4>Mobile Application</h4>
                        <p>Native apps for iOS and Android with offline capabilities</p>
                    </div>
                    <div class="feature-card">
                        <h4>Integration Marketplace</h4>
                        <p>Expanded ecosystem of plug-and-play third-party integrations</p>
                    </div>
                </div>

                <div class="slide-number">11</div>
            </div>
        </div>

        <!-- Slide 12: Competitive Advantages -->
        <div class="slide">
            <div class="slide-content">
                <h2>Competitive Advantages</h2>
                <h3>Why Choose CRM-SASS?</h3>

                <div class="highlight-box">
                    <ul>
                        <li><span class="primary-text">Modern architecture</span> built for the cloud era</li>
                        <li><span class="secondary-text">User-centric design</span> with minimal learning curve</li>
                        <li><span class="accent-text">Flexible pricing</span> model for businesses of all sizes</li>
                        <li><span class="primary-text">Continuous updates</span> and feature enhancements</li>
                        <li><span class="secondary-text">Dedicated support</span> and comprehensive documentation</li>
                    </ul>
                </div>

                <div class="image-placeholder">
                    Comparison Chart
                </div>

                <div class="slide-number">12</div>
            </div>
        </div>

        <!-- Slide 13: Demo & Questions -->
        <div class="slide">
            <div class="slide-content">
                <h2>Demo & Questions</h2>
                <h3>Live Demonstration</h3>

                <div class="image-placeholder">
                    Thank You
                </div>

                <div class="contact-info glass-morphism">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Website:</strong> www.crm-sass.com</p>
                    <p><strong>Support:</strong> <EMAIL></p>
                </div>

                <div class="slide-number">13</div>
            </div>
        </div>
    </div>
</body>
</html>