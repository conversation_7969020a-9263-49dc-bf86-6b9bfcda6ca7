import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Play, CheckCircle, Zap, BarChart3, Users, Target } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export const Hero = () => {
  return (
    <section className="relative py-20 md:py-28 lg:py-36 overflow-hidden">
      {/* Enhanced background with more dynamic elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-background to-background dark:from-primary/10 dark:via-background/70 dark:to-background" />
      <div className="absolute top-20 right-0 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '8s' }} />
      <div className="absolute bottom-0 left-10 w-72 h-72 bg-accent/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '12s' }} />
      <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-purple/10 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '10s' }} />

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="flex flex-col lg:flex-row items-center gap-10 lg:gap-16">
          {/* Left Content - Enhanced */}
          <div className="flex-1 text-left lg:pr-6">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 dark:from-primary/30 dark:to-accent/30 text-primary dark:text-primary/90 text-sm font-medium mb-8 shadow-sm">
              <Zap className="h-4 w-4 mr-2 animate-pulse" />
              New: AI-Powered Lead Insights
            </div>

            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-8 tracking-tight leading-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary via-accent to-purple">
                Convert More Leads,
              </span>
              <br />
              <span className="text-foreground relative">
                Close More Deals
                <span className="absolute -bottom-2 left-0 w-24 h-1 bg-accent rounded-full"></span>
              </span>
            </h1>

            <p className="text-lg text-muted-foreground mb-10 max-w-xl leading-relaxed">
              Elevate your sales performance with our intelligent CRM platform that helps you track, nurture, and convert leads with less effort and greater precision.
            </p>

            <div className="flex flex-col sm:flex-row gap-5 mb-10">
              <Link href="/login" className="w-full sm:w-auto">
                <Button size="lg" className="w-full sm:w-auto gap-2 font-medium shadow-lg hover:shadow-xl hover:-translate-y-1 transition-all duration-300 bg-gradient-to-r from-primary to-accent text-white border-0">
                  Start Free 14-Day Trial <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="w-full sm:w-auto gap-2 font-medium border-2 hover:bg-primary/5 dark:hover:bg-primary/10">
                <Play className="h-4 w-4 fill-current" /> Watch Demo
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 text-sm">
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-success/10 p-1">
                  <CheckCircle className="h-4 w-4 text-success" />
                </div>
                <span>No credit card required</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-success/10 p-1">
                  <CheckCircle className="h-4 w-4 text-success" />
                </div>
                <span>Full-featured trial</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-success/10 p-1">
                  <CheckCircle className="h-4 w-4 text-success" />
                </div>
                <span>Cancel anytime</span>
              </div>
            </div>
          </div>

          {/* Right stats card - Enhanced with better visuals */}
          <div className="flex-1 w-full max-w-md mt-12 lg:mt-0">
            <div className="relative bg-card/80 backdrop-blur-sm rounded-xl shadow-2xl p-8 border border-border/30 hover:shadow-2xl transition-all duration-300 overflow-hidden group">
              {/* Decorative elements */}
              <div className="absolute -top-10 -right-10 w-40 h-40 bg-primary/10 rounded-full blur-xl group-hover:bg-primary/20 transition-all duration-500"></div>
              <div className="absolute -bottom-10 -left-10 w-40 h-40 bg-accent/10 rounded-full blur-xl group-hover:bg-accent/20 transition-all duration-500"></div>

              <div className="relative space-y-8">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-primary/10 dark:bg-primary/20 p-2">
                      <Users className="h-5 w-5 text-primary" />
                    </div>
                    <p className="font-medium">Active Users</p>
                  </div>
                  <div className="text-right">
                    <p className="text-3xl font-bold text-primary">10k+</p>
                    <p className="text-xs text-muted-foreground flex items-center justify-end">
                      <span className="inline-block w-3 h-3 border-t-2 border-r-2 border-success rotate-45 mr-1"></span>
                      +23% from last month
                    </p>
                  </div>
                </div>

                <div className="w-full h-px bg-gradient-to-r from-transparent via-border/70 to-transparent"></div>

                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-accent/10 dark:bg-accent/20 p-2">
                      <Target className="h-5 w-5 text-accent" />
                    </div>
                    <p className="font-medium">Leads Managed</p>
                  </div>
                  <div className="text-right">
                    <p className="text-3xl font-bold text-accent">1M+</p>
                    <p className="text-xs text-muted-foreground flex items-center justify-end">
                      <span className="inline-block w-3 h-3 border-t-2 border-r-2 border-success rotate-45 mr-1"></span>
                      +67% YoY growth
                    </p>
                  </div>
                </div>

                <div className="w-full h-px bg-gradient-to-r from-transparent via-border/70 to-transparent"></div>

                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-purple/10 dark:bg-purple/20 p-2">
                      <BarChart3 className="h-5 w-5 text-purple" />
                    </div>
                    <p className="font-medium">Satisfaction</p>
                  </div>
                  <div className="text-right">
                    <p className="text-3xl font-bold text-purple">99%</p>
                    <p className="text-xs text-muted-foreground">Based on 5,000+ reviews</p>
                  </div>
                </div>
              </div>

              <div className="mt-10 pt-6 border-t border-border/30">
                <div className="flex gap-2 flex-wrap">
                  <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-gradient-to-r from-primary/10 to-primary/20 dark:from-primary/20 dark:to-primary/30 text-xs font-medium text-primary shadow-sm">
                    Salesforce Alternative
                  </div>
                  <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-gradient-to-r from-accent/10 to-accent/20 dark:from-accent/20 dark:to-accent/30 text-xs font-medium text-accent shadow-sm">
                    Easy Integration
                  </div>
                  <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-gradient-to-r from-purple/10 to-purple/20 dark:from-purple/20 dark:to-purple/30 text-xs font-medium text-purple shadow-sm">
                    AI-Powered
                  </div>
                </div>
              </div>

              {/* Floating decorative elements */}
              <div className="absolute top-1/4 right-4 w-2 h-2 bg-primary rounded-full animate-ping" style={{ animationDuration: '3s' }}></div>
              <div className="absolute bottom-1/4 left-4 w-2 h-2 bg-accent rounded-full animate-ping" style={{ animationDuration: '4s' }}></div>
            </div>
          </div>
        </div>

        {/* Trusted by section */}
        <div className="mt-20 text-center">
          <p className="text-sm text-muted-foreground mb-6">TRUSTED BY INNOVATIVE COMPANIES</p>
          <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12 opacity-70 grayscale">
            <div className="h-8 w-24 bg-foreground/20 rounded-md"></div>
            <div className="h-8 w-32 bg-foreground/20 rounded-md"></div>
            <div className="h-8 w-28 bg-foreground/20 rounded-md"></div>
            <div className="h-8 w-20 bg-foreground/20 rounded-md"></div>
            <div className="h-8 w-24 bg-foreground/20 rounded-md"></div>
          </div>
        </div>
      </div>
    </section>
  );
};