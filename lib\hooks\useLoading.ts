"use client";

import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/lib/store/store';
import { startLoading, stopLoading, setLoadingProgress } from '@/lib/store/slices/loadingSlice';

/**
 * A hook that provides access to the global loading state and actions
 * This replaces the Context API implementation with Redux
 */
export function useLoading() {
  const dispatch = useDispatch();
  const { isLoading, loadingMessage, loadingProgress } = useSelector(
    (state: RootState) => state.loading
  );

  return {
    isLoading,
    loadingMessage,
    loadingProgress,
    startLoading: (message?: string) => dispatch(startLoading(message)),
    stopLoading: () => dispatch(stopLoading()),
    setLoadingProgress: (progress: number) => dispatch(setLoadingProgress(progress)),
  };
}
