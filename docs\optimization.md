# Performance Optimization Guide

This document outlines the performance optimizations implemented in the CRM application to address slowdowns and storage bloat issues.

## Overview of Optimizations

The following optimizations have been implemented:

1. **RTK Query Caching**: Optimized caching settings for RTK Query APIs
2. **Supabase Client Management**: Improved subscription tracking and cleanup
3. **Middleware Optimization**: Improved authentication and authorization flow
4. **Scheduled Cleanup**: Regular cleanup of unused resources

## Configuration Options

### RTK Query Cache Settings

RTK Query cache settings can be adjusted in the API base files:

```typescript
// Example from lib/store/base/leadsapi.ts
export const leadsApi = createApi({
  // ...
  keepUnusedDataFor: 120, // 2 minutes - balance between performance and memory usage
  refetchOnReconnect: true,
  refetchOnMountOrArgChange: 300, // Refetch after 5 minutes if component remounts
  // ...
});
```

### Cleanup Intervals

Cleanup intervals can be adjusted in `lib/cleanup.ts`:

```typescript
const CLEANUP_INTERVALS = {
  SUPABASE_SUBSCRIPTIONS: 30 * 60 * 1000, // 30 minutes
  STORAGE: 24 * 60 * 60 * 1000, // 24 hours
};
```

## Maintenance API

A maintenance API endpoint has been added to manually trigger cleanup operations:

```
POST /api/maintenance/cleanup
```

This endpoint requires authorization with the `MAINTENANCE_API_KEY` environment variable.

### Query Parameters

- `operation`: The type of cleanup operation to perform
  - `all`: Run all cleanup operations (default)
  - `subscriptions`: Clean up inactive Supabase subscriptions

### Example Usage

```bash
curl -X POST https://your-domain.com/api/maintenance/cleanup?operation=all \
  -H "Authorization: Bearer your-maintenance-api-key"
```

## Scheduled Cleanup

Cleanup operations are automatically scheduled in production:

1. **Browser-side**: Cleanup is initialized in `_app.tsx` when the application starts
2. **Random Cleanup**: A small percentage of API requests trigger cleanup operations
3. **Cron Job**: Set up a cron job to call the maintenance API regularly

### Setting Up a Cron Job

Add the following to your server's crontab:

```
# Run cleanup every 6 hours
0 */6 * * * curl -X POST https://your-domain.com/api/maintenance/cleanup -H "Authorization: Bearer your-maintenance-api-key"
```

## Monitoring

### Supabase Subscription Monitoring

High numbers of active subscriptions are automatically logged:

```
High number of active Supabase subscriptions: {count}
```

## Troubleshooting

If you continue to experience performance issues:

1. Check the server logs for warnings about high subscription counts
2. Monitor Redis cache statistics for high miss rates
3. Verify that cleanup operations are running successfully
4. Consider adjusting TTL settings for frequently accessed data
5. Review database query patterns for optimization opportunities

## Environment Variables

Add the following environment variable:

```
MAINTENANCE_API_KEY=your-secure-random-key
```
