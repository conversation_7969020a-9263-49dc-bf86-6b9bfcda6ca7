import { Status, WorkspaceMember } from '../types/leads';

/**
 * Utility functions for handling lead data with ID-based references
 */

/**
 * Find status by ID and return status object with name and color
 */
export const getStatusById = (statusId: number | undefined, statusData: Status[]): Status | null => {
  if (!statusId || !statusData) return null;
  
  const status = statusData.find(s => s.id === statusId);
  return status || null;
};

/**
 * Find member by ID and return member object
 */
export const getMemberById = (memberId: number | undefined, membersData: WorkspaceMember[]): WorkspaceMember | null => {
  if (!memberId || !membersData) return null;
  
  const member = membersData.find(m => m.id === memberId);
  return member || null;
};

/**
 * Get status display data (name and color) for a lead
 * Falls back to legacy status object if status_id is not available
 */
export const getLeadStatusDisplay = (lead: any, statusData: Status[]) => {
  // Try to get status by ID first (new approach)
  if (lead.status_id) {
    const status = getStatusById(lead.status_id, statusData);
    if (status) {
      return {
        name: status.name,
        color: status.color
      };
    }
  }
  
  // Fall back to legacy status object
  if (lead.status && typeof lead.status === 'object') {
    return {
      name: lead.status.name || 'Pending',
      color: lead.status.color || '#ea1212'
    };
  }
  
  // Default fallback
  return {
    name: 'Pending',
    color: '#ea1212'
  };
};

/**
 * Get member display data (name and role) for a lead
 * Falls back to legacy assign_to object if assigned_to_id is not available
 */
export const getLeadAssignmentDisplay = (lead: any, membersData: WorkspaceMember[]) => {
  // Try to get member by ID first (new approach)
  if (lead.assigned_to_id) {
    const member = getMemberById(lead.assigned_to_id, membersData);
    if (member) {
      return {
        name: member.name,
        role: member.role
      };
    }
  }
  
  // Fall back to legacy assign_to object
  if (lead.assign_to && typeof lead.assign_to === 'object') {
    return {
      name: lead.assign_to.name || 'Not Assigned',
      role: lead.assign_to.role || ''
    };
  }
  
  // Fall back to string assign_to
  if (typeof lead.assign_to === 'string' && lead.assign_to !== 'Not Assigned') {
    return {
      name: lead.assign_to,
      role: ''
    };
  }
  
  // Default fallback
  return {
    name: 'Not Assigned',
    role: ''
  };
};

/**
 * Create status value for select component (backward compatible)
 */
export const createStatusSelectValue = (statusId: number | undefined, statusData: Status[], legacyStatus?: any) => {
  const statusDisplay = getLeadStatusDisplay({ status_id: statusId, status: legacyStatus }, statusData);
  return JSON.stringify({
    id: statusId,
    name: statusDisplay.name,
    color: statusDisplay.color
  });
};

/**
 * Create member value for select component (backward compatible)
 */
export const createMemberSelectValue = (memberId: number | undefined, membersData: WorkspaceMember[], legacyAssignTo?: any) => {
  const memberDisplay = getLeadAssignmentDisplay({ assigned_to_id: memberId, assign_to: legacyAssignTo }, membersData);
  return JSON.stringify({
    id: memberId,
    name: memberDisplay.name,
    role: memberDisplay.role
  });
};

/**
 * Parse status value from select component and extract ID
 */
export const parseStatusSelectValue = (value: string): { id?: number; name: string; color: string } => {
  try {
    const parsed = JSON.parse(value);
    return {
      id: parsed.id,
      name: parsed.name,
      color: parsed.color
    };
  } catch {
    return { name: 'Pending', color: '#ea1212' };
  }
};

/**
 * Parse member value from select component and extract ID
 */
export const parseMemberSelectValue = (value: string): { id?: number; name: string; role: string } => {
  try {
    const parsed = JSON.parse(value);
    return {
      id: parsed.id,
      name: parsed.name,
      role: parsed.role
    };
  } catch {
    return { name: 'Not Assigned', role: '' };
  }
};
