import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../utils/auth";

/**
 * Creates a new webhook
 * @route POST /api/webhooks/create
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    // Parse request body
    const body = await request.json();
    const { status, type, name, webhook_url, workspace_id } = body;

    // Validate request body
    if (status === undefined || !type || !name || !webhook_url || !workspace_id) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if user has permission to create a webhook in this workspace
    const permission = await checkWorkspacePermission(user.id, workspace_id);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }

    // Insert webhook with user ID
    const { data, error } = await supabase.from("webhooks").insert({
      status,
      type,
      name,
      webhook_url,
      user_id: user.id,
      description: " ", // Default empty description
      workspace_id,
    }).select();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating webhook:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
