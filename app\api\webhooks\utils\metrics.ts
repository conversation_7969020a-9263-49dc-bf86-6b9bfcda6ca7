import { supabase } from "@/lib/supabaseServer";

/**
 * Calculate metrics for webhooks based on leads data
 * @param webhooks Array of webhook objects
 * @param workspaceId The workspace ID to get leads for
 * @returns Array of webhooks with added metrics
 */
export async function calculateWebhookMetrics(webhooks: any[], workspaceId: string) {
  try {
    // Fetch leads for the workspace
    const { data: leads, error: leadsError } = await supabase
      .from("leads")
      .select("id, source, status")
      .eq("work_id", workspaceId);
      
    if (leadsError) {
      console.error("Error fetching leads for metrics:", leadsError);
      return webhooks; // Return webhooks without metrics if there's an error
    }
    
    // Initialize metrics object
    const leadMetrics = {};
    
    // Calculate metrics for each lead
    leads.forEach(lead => {
      if (!leadMetrics[lead.source]) {
        leadMetrics[lead.source] = {
          totalLeads: 0,
          qualifiedLeads: 0,
          processingLeads: 0
        };
      }
      
      leadMetrics[lead.source].totalLeads++;
      
      // Check if status indicates "qualified"
      const status = typeof lead.status === 'string' ? JSON.parse(lead.status) : lead.status;
      if (status && status.name === "Qualified") {
        leadMetrics[lead.source].qualifiedLeads++;
      }
      
      // Count as "processing" if it has any status other than default/empty
      if (status && status.name && status.name !== "Not Reachable/Responding") {
        leadMetrics[lead.source].processingLeads++;
      }
    });
    
    // Add metrics to webhooks
    const webhooksWithMetrics = webhooks.map(webhook => {
      const source = webhook.type; // Assuming the webhook type corresponds to the lead source
      const metrics = leadMetrics[source] || { totalLeads: 0, qualifiedLeads: 0, processingLeads: 0 };
      
      const qualificationRate = metrics.totalLeads > 0 
        ? (metrics.qualifiedLeads / metrics.totalLeads * 100).toFixed(2) 
        : 0;
        
      const processingRate = metrics.totalLeads > 0 
        ? (metrics.processingLeads / metrics.totalLeads * 100).toFixed(2) 
        : 0;

      return {
        ...webhook,
        metrics: {
          totalLeads: metrics.totalLeads,
          qualificationRate: `${qualificationRate}%`,
          processingRate: `${processingRate}%`,
          qualifiedLeads: metrics.qualifiedLeads,
          processingLeads: metrics.processingLeads
        }
      };
    });
    
    return webhooksWithMetrics;
  } catch (error) {
    console.error("Error calculating webhook metrics:", error);
    return webhooks; // Return webhooks without metrics if there's an error
  }
}
