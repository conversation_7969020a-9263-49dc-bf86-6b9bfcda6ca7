import React from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface DocumentationSkeletonProps {
  isCollapsed?: boolean;
}

export const DocumentationSkeleton: React.FC<DocumentationSkeletonProps> = ({ isCollapsed = false }) => {
  return (
    <div className={`p-2 sm:p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[8px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800 shadow-sm">
        {/* Header Skeleton */}
        <CardHeader className="p-3 sm:p-4 md:p-6 pb-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-black/10 dark:bg-white/10 p-1.5 sm:p-2 rounded-full flex-shrink-0">
                <Skeleton className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
              <div className="min-w-0">
                <Skeleton className="h-6 sm:h-7 md:h-8 w-32 sm:w-48 md:w-64 mb-1 sm:mb-2" />
                <Skeleton className="h-3 sm:h-4 w-48 sm:w-64 md:w-80 hidden sm:block" />
              </div>
            </div>
          </div>

          {/* Tabs Skeleton */}
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-900 p-1 rounded-lg overflow-x-auto">
            {Array.from({ length: 5 }).map((_, index) => (
              <Skeleton key={index} className="h-8 sm:h-9 w-16 sm:w-20 md:w-24 rounded-md flex-shrink-0" />
            ))}
          </div>
        </CardHeader>

        <CardContent className="p-3 sm:p-4 md:p-6">
          {/* Overview Section Skeleton */}
          <div className="space-y-4 sm:space-y-6">
            {/* Feature Cards Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
              {Array.from({ length: 4 }).map((_, index) => (
                <Card key={index} className="border border-gray-200 dark:border-gray-800">
                  <CardContent className="p-3 sm:p-4 md:p-6">
                    <div className="flex items-center space-x-2 sm:space-x-3 mb-2 sm:mb-3">
                      <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded-full flex-shrink-0" />
                      <Skeleton className="h-4 sm:h-5 w-24 sm:w-32" />
                    </div>
                    <Skeleton className="h-3 sm:h-4 w-full mb-1 sm:mb-2" />
                    <Skeleton className="h-3 sm:h-4 w-3/4" />
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Content Sections */}
            <div className="space-y-6 sm:space-y-8">
              {/* Section 1 */}
              <div>
                <Skeleton className="h-5 sm:h-6 md:h-7 w-32 sm:w-40 md:w-48 mb-3 sm:mb-4" />
                <div className="space-y-2 sm:space-y-3">
                  <Skeleton className="h-3 sm:h-4 w-full" />
                  <Skeleton className="h-3 sm:h-4 w-5/6" />
                  <Skeleton className="h-3 sm:h-4 w-4/5" />
                </div>
              </div>

              {/* Code Block Skeleton */}
              <Card className="bg-gray-50 dark:bg-gray-900">
                <CardContent className="p-3 sm:p-4 md:p-6">
                  <div className="flex justify-between items-center mb-3 sm:mb-4">
                    <Skeleton className="h-4 sm:h-5 w-24 sm:w-32" />
                    <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded" />
                  </div>
                  <div className="space-y-1 sm:space-y-2">
                    {Array.from({ length: 4 }).map((_, index) => (
                      <Skeleton key={index} className="h-3 sm:h-4 w-full" style={{ width: `${Math.random() * 40 + 60}%` }} />
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Table Skeleton */}
              <Card>
                <CardHeader className="p-3 sm:p-4 md:p-6">
                  <Skeleton className="h-5 sm:h-6 w-32 sm:w-40" />
                  <Skeleton className="h-3 sm:h-4 w-48 sm:w-64" />
                </CardHeader>
                <CardContent className="p-3 sm:p-4 md:p-6">
                  <div className="hidden md:block">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead><Skeleton className="h-4 w-16 sm:w-20" /></TableHead>
                          <TableHead><Skeleton className="h-4 w-12 sm:w-16" /></TableHead>
                          <TableHead><Skeleton className="h-4 w-20 sm:w-24" /></TableHead>
                          <TableHead><Skeleton className="h-4 w-14 sm:w-18" /></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Array.from({ length: 3 }).map((_, index) => (
                          <TableRow key={index}>
                            <TableCell><Skeleton className="h-4 w-24 sm:w-32" /></TableCell>
                            <TableCell><Skeleton className="h-5 sm:h-6 w-12 sm:w-16 rounded-full" /></TableCell>
                            <TableCell><Skeleton className="h-4 w-32 sm:w-40" /></TableCell>
                            <TableCell><Skeleton className="h-4 w-20 sm:w-24" /></TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Mobile Table Skeleton */}
                  <div className="block md:hidden space-y-3 sm:space-y-4">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-3 sm:p-4">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <Skeleton className="h-4 w-24 sm:w-32" />
                            <Skeleton className="h-5 sm:h-6 w-12 sm:w-16 rounded-full" />
                          </div>
                          <Skeleton className="h-3 sm:h-4 w-full" />
                          <Skeleton className="h-3 sm:h-4 w-20 sm:w-24" />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Button Grid Skeleton */}
              <div>
                <Skeleton className="h-5 sm:h-6 w-32 sm:w-40 mb-3 sm:mb-4" />
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 sm:gap-3 md:gap-4">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <Skeleton key={index} className="h-8 sm:h-9 md:h-10 w-full rounded-md" />
                  ))}
                </div>
              </div>

              {/* Text Gradient Examples */}
              <div>
                <Skeleton className="h-5 sm:h-6 w-36 sm:w-48 mb-3 sm:mb-4" />
                <div className="grid grid-cols-1 gap-3 sm:gap-4 md:gap-6">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-3 sm:p-4">
                      <Skeleton className="h-4 sm:h-5 w-28 sm:w-36 mb-1 sm:mb-2" />
                      <Skeleton className="h-3 sm:h-4 w-full" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Loader Examples */}
              <Card>
                <CardHeader className="p-3 sm:p-4 md:p-6">
                  <Skeleton className="h-5 sm:h-6 w-24 sm:w-32" />
                </CardHeader>
                <CardContent className="p-3 sm:p-4 md:p-6">
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                    {Array.from({ length: 3 }).map((_, index) => (
                      <div key={index} className="flex flex-col items-center space-y-1 sm:space-y-2">
                        <Skeleton className="h-6 w-6 sm:h-8 sm:w-8 rounded-full" />
                        <Skeleton className="h-3 sm:h-4 w-16 sm:w-24" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Card Examples */}
              <div>
                <Skeleton className="h-5 sm:h-6 w-24 sm:w-32 mb-3 sm:mb-4" />
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <Card key={index} className="border border-gray-200 dark:border-gray-800">
                      <CardHeader className="p-3 sm:p-4">
                        <Skeleton className="h-4 sm:h-5 w-20 sm:w-28" />
                      </CardHeader>
                      <CardContent className="p-3 sm:p-4">
                        <Skeleton className="h-3 sm:h-4 w-full" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Mobile Documentation Skeleton
export const DocumentationMobileSkeleton: React.FC = () => {
  return (
    <div className="p-3 sm:p-4 space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2 sm:gap-3">
        <Skeleton className="h-5 w-5 sm:h-6 sm:w-6 rounded-full flex-shrink-0" />
        <div className="min-w-0">
          <Skeleton className="h-5 sm:h-6 w-32 sm:w-48 mb-1" />
          <Skeleton className="h-3 sm:h-4 w-40 sm:w-64" />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 sm:space-x-2 overflow-x-auto">
        {Array.from({ length: 4 }).map((_, index) => (
          <Skeleton key={index} className="h-7 sm:h-8 w-16 sm:w-20 rounded-md flex-shrink-0" />
        ))}
      </div>

      {/* Content */}
      <div className="space-y-4 sm:space-y-6">
        {/* Feature Cards */}
        <div className="grid grid-cols-1 gap-3 sm:gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="p-3 sm:p-4 border border-gray-200 dark:border-gray-800">
              <div className="flex items-center space-x-2 sm:space-x-3 mb-2 sm:mb-3">
                <Skeleton className="h-5 w-5 sm:h-6 sm:w-6 rounded-full flex-shrink-0" />
                <Skeleton className="h-3 sm:h-4 w-24 sm:w-32" />
              </div>
              <Skeleton className="h-3 sm:h-4 w-full mb-1 sm:mb-2" />
              <Skeleton className="h-3 sm:h-4 w-3/4" />
            </Card>
          ))}
        </div>

        {/* Code Block */}
        <Card className="p-3 sm:p-4 border border-gray-200 dark:border-gray-800">
          <div className="flex justify-between items-center mb-2 sm:mb-3">
            <Skeleton className="h-3 sm:h-4 w-20 sm:w-24" />
            <Skeleton className="h-5 w-5 sm:h-6 sm:w-6 rounded" />
          </div>
          <div className="space-y-1 sm:space-y-2">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="h-2 sm:h-3 w-full" style={{ width: `${Math.random() * 30 + 70}%` }} />
            ))}
          </div>
        </Card>

        {/* Button Examples */}
        <div>
          <Skeleton className="h-4 sm:h-5 w-24 sm:w-32 mb-2 sm:mb-3" />
          <div className="grid grid-cols-2 gap-2 sm:gap-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <Skeleton key={index} className="h-8 sm:h-9 w-full rounded-md" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
