import { WorkspaceSettings, Status, Tags, NewStatus, NewTags } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';

/**
 * Validate workspace settings
 * @param settings - Workspace settings to validate
 * @returns Object with isValid boolean and error message
 */
export function validateWorkspaceSettings(settings: WorkspaceSettings): { isValid: boolean; error?: string } {
  if (!settings.name || settings.name.trim().length < WORKSPACE_CONSTANTS.VALIDATION.MIN_NAME_LENGTH) {
    return {
      isValid: false,
      error: 'Workspace name is required'
    };
  }

  if (settings.name.length > WORKSPACE_CONSTANTS.VALIDATION.MAX_NAME_LENGTH) {
    return {
      isValid: false,
      error: `Workspace name must be less than ${WORKSPACE_CONSTANTS.VALIDATION.MAX_NAME_LENGTH} characters`
    };
  }

  return { isValid: true };
}

/**
 * Validate status data
 * @param status - Status to validate
 * @returns Object with isValid boolean and error message
 */
export function validateStatus(status: Status | NewStatus): { isValid: boolean; error?: string } {
  if (!status.name || status.name.trim().length < WORKSPACE_CONSTANTS.VALIDATION.MIN_NAME_LENGTH) {
    return {
      isValid: false,
      error: WORKSPACE_CONSTANTS.MESSAGES.STATUS_NAME_REQUIRED
    };
  }

  if (status.name.length > WORKSPACE_CONSTANTS.VALIDATION.MAX_NAME_LENGTH) {
    return {
      isValid: false,
      error: `Status name must be less than ${WORKSPACE_CONSTANTS.VALIDATION.MAX_NAME_LENGTH} characters`
    };
  }

  if (!WORKSPACE_CONSTANTS.VALIDATION.COLOR_REGEX.test(status.color)) {
    return {
      isValid: false,
      error: 'Please select a valid color'
    };
  }

  return { isValid: true };
}

/**
 * Validate tags data
 * @param tags - Tags to validate
 * @returns Object with isValid boolean and error message
 */
export function validateTags(tags: Tags | NewTags): { isValid: boolean; error?: string } {
  if (!tags.name || tags.name.trim().length < WORKSPACE_CONSTANTS.VALIDATION.MIN_NAME_LENGTH) {
    return {
      isValid: false,
      error: WORKSPACE_CONSTANTS.MESSAGES.TAGS_NAME_REQUIRED
    };
  }

  if (tags.name.length > WORKSPACE_CONSTANTS.VALIDATION.MAX_NAME_LENGTH) {
    return {
      isValid: false,
      error: `Tags name must be less than ${WORKSPACE_CONSTANTS.VALIDATION.MAX_NAME_LENGTH} characters`
    };
  }

  if (!WORKSPACE_CONSTANTS.VALIDATION.COLOR_REGEX.test(tags.color)) {
    return {
      isValid: false,
      error: 'Please select a valid color'
    };
  }

  return { isValid: true };
}

/**
 * Get stored active tab from localStorage
 * @returns Active tab ID or default tab
 */
export function getStoredActiveTab(): string {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(WORKSPACE_CONSTANTS.STORAGE_KEYS.ACTIVE_TAB) || WORKSPACE_CONSTANTS.DEFAULTS.ACTIVE_TAB;
  }
  return WORKSPACE_CONSTANTS.DEFAULTS.ACTIVE_TAB;
}

/**
 * Store active tab in localStorage
 * @param tabId - Tab ID to store
 */
export function storeActiveTab(tabId: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem(WORKSPACE_CONSTANTS.STORAGE_KEYS.ACTIVE_TAB, tabId);
  }
}

/**
 * Format workspace data for API
 * @param settings - Workspace settings
 * @returns Formatted data for API
 */
export function formatWorkspaceDataForAPI(settings: WorkspaceSettings): any {
  return {
    name: settings.name.trim(),
    industry: settings.industry,
    company_size: settings.company_size,
    timezone: settings.timezone,
    notifications: settings.notifications,
    security: settings.security,
  };
}

/**
 * Format status data for API
 * @param status - Status data
 * @returns Formatted status for API
 */
export function formatStatusForAPI(status: Status | NewStatus): any {
  return {
    name: status.name.trim(),
    color: status.color,
    count_statistics: 'count_statistics' in status ? status.count_statistics : false,
    workspace_show: 'showInWorkspace' in status ? status.showInWorkspace : false,
  };
}

/**
 * Format tags data for API
 * @param tags - Tags data
 * @returns Formatted tags for API
 */
export function formatTagsForAPI(tags: Tags | NewTags): any {
  return {
    name: tags.name.trim(),
    color: tags.color,
  };
}

/**
 * Parse workspace data from API response
 * @param data - API response data
 * @returns Parsed workspace settings
 */
export function parseWorkspaceDataFromAPI(data: any): WorkspaceSettings {
  return {
    name: data.name || '',
    industry: data.industry || '',
    company_size: data.company_size || '',
    timezone: data.timezone || '',
    notifications: {
      email: data.notifications?.email || false,
      sms: data.notifications?.sms || false,
      inApp: data.notifications?.inApp || false,
    },
    security: {
      twoFactor: data.security?.twoFactor || false,
      ipRestriction: data.security?.ipRestriction || false,
    },
  };
}

/**
 * Generate delete confirmation message
 * @param itemName - Name of item to delete
 * @param itemType - Type of item (status, tags, etc.)
 * @returns Formatted confirmation message
 */
export function getDeleteConfirmationMessage(itemName: string, itemType: 'status' | 'tags'): string {
  const template = itemType === 'status' 
    ? WORKSPACE_CONSTANTS.UI_TEXT.DELETE_STATUS_DESCRIPTION
    : WORKSPACE_CONSTANTS.UI_TEXT.DELETE_TAGS_DESCRIPTION;
  
  return template.replace('{name}', itemName);
}

/**
 * Handle API error and return user-friendly message
 * @param error - API error object
 * @param defaultMessage - Default error message
 * @returns User-friendly error message
 */
export function handleAPIError(error: any, defaultMessage: string): string {
  if (error.status === 409) {
    return WORKSPACE_CONSTANTS.MESSAGES.STATUS_IN_USE_ERROR;
  }
  
  if (error.data?.message) {
    return error.data.message;
  }
  
  if (error.data?.error) {
    return error.data.error;
  }
  
  if (error.error) {
    return error.error;
  }
  
  if (typeof error === 'string') {
    return error;
  }
  
  return defaultMessage;
}

/**
 * Check if tab is valid
 * @param tabId - Tab ID to check
 * @returns Boolean indicating if tab is valid
 */
export function isValidTab(tabId: string): boolean {
  return Object.values(WORKSPACE_CONSTANTS.TABS).includes(tabId as any);
}

/**
 * Reset form data to defaults
 * @param type - Type of form to reset
 * @returns Default form data
 */
export function getDefaultFormData(type: 'status' | 'tags'): NewStatus | NewTags {
  if (type === 'status') {
    return { ...WORKSPACE_CONSTANTS.DEFAULTS.NEW_STATUS };
  }
  return { ...WORKSPACE_CONSTANTS.DEFAULTS.NEW_TAGS };
}

/**
 * Check if settings have been modified
 * @param current - Current settings
 * @param original - Original settings
 * @returns Boolean indicating if settings were modified
 */
export function hasSettingsChanged(current: WorkspaceSettings, original: WorkspaceSettings): boolean {
  return JSON.stringify(current) !== JSON.stringify(original);
}
