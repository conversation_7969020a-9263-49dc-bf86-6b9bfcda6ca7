import { useState } from 'react';
import { toast } from 'sonner';
import { WorkspaceMember, ResendingState } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';
import { validateEmail, createNewMember } from '../utils/member';

interface UseMemberManagementProps {
  onMemberAdd: (member: WorkspaceMember) => void;
  onMemberDelete: (memberId: string) => void;
  onMemberUpdate: (member: WorkspaceMember) => void;
  onInviteResend?: (member: WorkspaceMember) => void;
  isAdding: boolean;
  isDeleting: boolean;
}

export function useMemberManagement({
  onMemberAdd,
  onMemberDelete,
  onMemberUpdate,
  onInviteResend,
  isAdding,
  isDeleting,
}: UseMemberManagementProps) {
  const [newInviteEmail, setNewInviteEmail] = useState('');
  const [newInviteRole, setNewInviteRole] = useState<string>(MEMBER_CONSTANTS.DEFAULTS.ROLE);
  const [resendingMembers, setResendingMembers] = useState<ResendingState>({});
  const [memberToDelete, setMemberToDelete] = useState<WorkspaceMember | null>(null);

  // Handle member invitation using new secure system
  const handleInviteMember = async () => {
    // Validate email
    if (!newInviteEmail.trim()) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.EMAIL_REQUIRED);
      return;
    }

    if (!validateEmail(newInviteEmail)) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.INVALID_EMAIL);
      return;
    }

    try {
      const newMember = createNewMember(newInviteEmail, newInviteRole);
      await onMemberAdd(newMember);

      // Reset form
      setNewInviteEmail('');
      setNewInviteRole(MEMBER_CONSTANTS.DEFAULTS.ROLE);

      toast.success('Secure invitation sent successfully');
    } catch (error) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.INVITE_ERROR);
    }
  };

  // Handle member deletion
  const handleDeleteMember = (member: WorkspaceMember) => {
    setMemberToDelete(member);
  };

  const confirmDeleteMember = async () => {
    if (!memberToDelete?.id) return;

    try {
      await onMemberDelete(memberToDelete.id);
      setMemberToDelete(null);
      toast.success(MEMBER_CONSTANTS.MESSAGES.DELETE_SUCCESS);
    } catch (error) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.DELETE_ERROR);
    }
  };

  const cancelDeleteMember = () => {
    setMemberToDelete(null);
  };

  // Handle invite resend
  const handleResendInvite = async (member: WorkspaceMember) => {
    if (!member.id || !onInviteResend) return;

    // Set loading state for this specific member
    setResendingMembers(prev => ({ ...prev, [member.id!]: true }));

    try {
      await onInviteResend(member);
      toast.success(MEMBER_CONSTANTS.MESSAGES.RESEND_SUCCESS);
    } catch (error) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.RESEND_ERROR);
    } finally {
      // Clear loading state for this member regardless of success/failure
      setResendingMembers(prev => ({ ...prev, [member.id!]: false }));
    }
  };

  // Check if invite form is valid
  const isInviteFormValid = (): boolean => {
    return Boolean(newInviteEmail.trim() && validateEmail(newInviteEmail) && !isAdding);
  };

  return {
    // Form state
    newInviteEmail,
    setNewInviteEmail,
    newInviteRole,
    setNewInviteRole,

    // Loading states
    resendingMembers,
    memberToDelete,

    // Actions
    handleInviteMember,
    handleDeleteMember,
    confirmDeleteMember,
    cancelDeleteMember,
    handleResendInvite,

    // Validation
    isInviteFormValid,
  };
}
