import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../utils/auth";

/**
 * Get all workspaces for the current user
 * @route GET /api/workspace/list
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    try {
      // Get workspaces owned by the user
      const { data: ownedWorkspaces, error: ownedError } = await supabase
        .from("workspaces")
        .select("*")
        .eq("owner_id", user.id);
      
      if (ownedError) {
        return NextResponse.json({ error: ownedError.message }, { status: 500 });
      }
      
      // Get workspaces where the user is a member
      const { data: memberWorkspaces, error: memberError } = await supabase
        .from("workspace_members")
        .select("workspace_id")
        .eq("email", user.email);
      
      if (memberError) {
        return NextResponse.json({ error: memberError.message }, { status: 500 });
      }
      
      // Combine and deduplicate workspace IDs
      const workspaceIds = [
        ...new Set([
          ...ownedWorkspaces.map((ws) => ws.id),
          ...memberWorkspaces.map((ws) => ws.workspace_id),
        ]),
      ].filter((id) => id !== null && id !== undefined);
      
      // Get all workspaces by IDs
      const { data: allWorkspaces, error: allWorkspacesError } = await supabase
        .from("workspaces")
        .select("*")
        .in("id", workspaceIds);
      
      if (allWorkspacesError) {
        return NextResponse.json({ error: allWorkspacesError.message }, { status: 500 });
      }
      
      return NextResponse.json({ 
        message: "Workspaces fetched", 
        data: allWorkspaces 
      }, { status: 200 });
    } catch (error) {
      console.error("Error fetching workspaces:", error);
      return NextResponse.json({ error: "An error occurred" }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching workspaces:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
