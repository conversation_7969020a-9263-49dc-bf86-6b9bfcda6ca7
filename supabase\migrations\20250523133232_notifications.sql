-- Migration for table: notifications
CREATE TABLE IF NOT EXISTS "notifications" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "lead_id" text,
  "action_type" text,
  "user_id" uuid NOT NULL,
  "workspace_id" uuid,
  "details" jsonb,
  "read" boolean,
  "lead_source_id" uuid,
  "related_user_id" uuid
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "notifications_user_id_idx" ON "notifications" ("user_id");

-- Create index on workspace_id
CREATE INDEX IF NOT EXISTS "notifications_workspace_id_idx" ON "notifications" ("workspace_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "notifications" ADD CONSTRAINT "fk_notifications_user_id"
--   FOREIGN KEY ("user_id") REFERENCES "auth.users" (id);

-- Add foreign key constraint for workspace_id (commented out, uncomment after verifying)
-- <PERSON><PERSON><PERSON> TABLE "notifications" ADD CONSTRAINT "fk_notifications_workspace_id"
--   FOREI<PERSON><PERSON> KEY ("workspace_id") REFERENCES "workspaces" (id);

-- Enable Row Level Security
ALTER TABLE "notifications" ENABLE ROW LEVEL SECURITY;

