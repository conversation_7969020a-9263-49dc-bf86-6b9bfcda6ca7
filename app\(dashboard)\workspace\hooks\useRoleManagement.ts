import { useState } from 'react';
import { toast } from 'sonner';
import { useUpdateMemberMutation } from '@/lib/store/services/members';
import { WorkspaceMember, MemberFormData } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';

interface UseRoleManagementProps {
  workspaceId: string;
  members: WorkspaceMember[];
  onMemberUpdate: (member: WorkspaceMember) => void;
}

export function useRoleManagement({ workspaceId, members, onMemberUpdate }: UseRoleManagementProps) {
  const [selectedMember, setSelectedMember] = useState<WorkspaceMember | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [updateMember] = useUpdateMemberMutation();

  const openEditDialog = (member: WorkspaceMember) => {
    setSelectedMember(member);
    setIsEditDialogOpen(true);
  };

  const closeEditDialog = () => {
    setIsEditDialogOpen(false);
    setSelectedMember(null);
  };

  const handleRoleUpdate = async (data: MemberFormData) => {
    if (!selectedMember) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.MEMBER_NOT_FOUND);
      return;
    }

    const updatedMember = members.find((m) => m.email === data.email);
    if (!updatedMember) {
      toast.error(MEMBER_CONSTANTS.MESSAGES.MEMBER_NOT_FOUND);
      return;
    }

    try {
      // Call the API to update the member role
      await updateMember({
        workspaceId,
        id: updatedMember.id ?? '',
        updates: { role: data.role },
      }).unwrap();

      // Update local state
      await onMemberUpdate({ ...updatedMember, role: data.role });
      
      toast.success(MEMBER_CONSTANTS.MESSAGES.UPDATE_SUCCESS);
      closeEditDialog();
    } catch (error) {
      console.error('Error updating member role:', error);
      toast.error(MEMBER_CONSTANTS.MESSAGES.UPDATE_ERROR);
    }
  };

  return {
    selectedMember,
    isEditDialogOpen,
    openEditDialog,
    closeEditDialog,
    handleRoleUpdate,
  };
}
