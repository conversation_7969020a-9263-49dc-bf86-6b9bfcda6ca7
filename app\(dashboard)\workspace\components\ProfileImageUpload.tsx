"use client";

import React, { useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { ProfileImageUploadProps } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';

export function ProfileImageUpload({ member, onImageUpdate }: ProfileImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !member.id) return;

    // In a real app, you would upload to your server here
    const imageUrl = MEMBER_CONSTANTS.DEFAULTS.PROFILE_IMAGE;
    onImageUpdate(member.id, imageUrl);

    // Clear the input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <>
      <input
        type="file"
        accept={MEMBER_CONSTANTS.FILE_UPLOAD.ACCEPTED_TYPES}
        className="hidden"
        ref={fileInputRef}
        onChange={handleFileChange}
      />
      <Button
        variant="ghost"
        size="icon"
        className="absolute -bottom-1 -right-1 md:w-6 md:h-6 w-5 h-5 p-0 rounded-full bg-primary hover:bg-primary/90 shadow-sm"
        onClick={triggerFileInput}
        type="button"
      >
        <Upload className="w-3 h-3 text-white" />
      </Button>
    </>
  );
}
