"use client";

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { PaymentGateway } from '@/lib/services/payment';
import { PAYMENT_GATEWAY_CONFIG } from '../constants/subscription';

interface PaymentMethodSelectorProps {
  selectedGateway: PaymentGateway;
  onSelect: (gateway: PaymentGateway) => void;
  selectedPlan: string;
}

interface PaymentMethodCardProps {
  gateway: PaymentGateway;
  selectedGateway: PaymentGateway;
  onSelect: (gateway: PaymentGateway) => void;
}

function PaymentMethodCard({ gateway, selectedGateway, onSelect }: PaymentMethodCardProps) {
  const config = PAYMENT_GATEWAY_CONFIG[gateway];
  const isSelected = selectedGateway === gateway;

  const renderIcon = () => {
    switch (gateway) {
      case 'stripe':
        return (
          <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.5 12.5C12.5 13.3284 11.8284 14 11 14C10.1716 14 9.5 13.3284 9.5 12.5C9.5 11.6716 10.1716 11 11 11C11.8284 11 12.5 11.6716 12.5 12.5Z" fill={config.color} />
            <path d="M16.5 12.5C16.5 13.3284 15.8284 14 15 14C14.1716 14 13.5 13.3284 13.5 12.5C13.5 11.6716 14.1716 11 15 11C15.8284 11 16.5 11.6716 16.5 12.5Z" fill={config.color} />
            <path d="M8.5 12.5C8.5 13.3284 7.82843 14 7 14C6.17157 14 5.5 13.3284 5.5 12.5C5.5 11.6716 6.17157 11 7 11C7.82843 11 8.5 11.6716 8.5 12.5Z" fill={config.color} />
            <path fillRule="evenodd" clipRule="evenodd" d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12Z" fill={config.color} />
          </svg>
        );
      case 'paypal':
        return (
          <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.02 5H16.4C17.8368 5 19 6.11929 19 7.5C19 7.58728 18.9952 7.67385 18.986 7.75932C18.4733 11.2206 15.4739 13 12.5 13H9.5L8 19H4L7.02 5Z" fill={config.color} />
            <path d="M9 7H16.4C16.9601 7 17.3333 7.40415 17.3333 7.8C17.3333 7.83517 17.3316 7.87003 17.3282 7.90449C17.0216 9.71258 15.4108 11 13.5 11H10.5L9 17H7L9 7Z" fill="#003087" />
          </svg>
        );
      case 'razorpay':
        return (
          <svg width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8.5 16.5L12.5 12.5M12.5 12.5L16.5 8.5M12.5 12.5L8.5 8.5M12.5 12.5L16.5 16.5" stroke={config.color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke={config.color} strokeWidth="2" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <Card
      className={`cursor-pointer transition-all duration-300 hover:shadow-md overflow-hidden group ${
        isSelected 
          ? 'border-primary bg-primary/5 shadow-sm' 
          : 'hover:border-primary/30'
      }`}
      onClick={() => onSelect(gateway)}
    >
      <div 
        className="absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300"
        style={{ 
          background: `linear-gradient(to bottom right, ${config.color}10, transparent)` 
        }}
      ></div>
      <CardContent className="p-4 sm:p-5 flex flex-col items-center justify-center relative z-10">
        <div 
          className="w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center mb-3 sm:mb-4 group-hover:scale-110 transition-transform duration-300"
          style={{ backgroundColor: `${config.color}10` }}
        >
          {renderIcon()}
        </div>
        <span className="font-medium text-base sm:text-lg">{config.name}</span>
        <span className="text-xs sm:text-sm text-muted-foreground mt-1 sm:mt-1.5">{config.description}</span>

        {isSelected ? (
          <div className="mt-3 flex items-center gap-1.5 text-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 12L11 14L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
            </svg>
            <span className="font-medium text-sm">Selected</span>
          </div>
        ) : (
          <div className="mt-3 h-6"></div>
        )}
      </CardContent>
    </Card>
  );
}

export function PaymentMethodSelector({ selectedGateway, onSelect, selectedPlan }: PaymentMethodSelectorProps) {
  if (selectedPlan === 'starter') return null;

  return (
    <div className="mt-12 md:mt-16 w-full max-w-3xl mx-auto bg-gradient-to-r from-muted/30 to-transparent p-6 md:p-8 rounded-2xl border border-muted/40 shadow-sm">
      <div className="text-center mb-6 md:mb-8">
        <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 mb-4">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-primary">
            <path d="M21 4H3C1.89543 4 1 4.89543 1 6V18C1 19.1046 1.89543 20 3 20H21C22.1046 20 23 19.1046 23 18V6C23 4.89543 22.1046 4 21 4Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M1 10H23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <h2 className="text-xl md:text-2xl font-semibold mb-2">Select Payment Method</h2>
        <p className="text-sm md:text-base text-muted-foreground max-w-md mx-auto">
          Choose your preferred payment gateway to complete your subscription
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 md:gap-5 w-full">
        {(['stripe', 'paypal', 'razorpay'] as PaymentGateway[]).map((gateway) => (
          <PaymentMethodCard
            key={gateway}
            gateway={gateway}
            selectedGateway={selectedGateway}
            onSelect={onSelect}
          />
        ))}
      </div>
      
      <div className="mt-6 text-center text-xs sm:text-sm text-muted-foreground">
        <p className="flex items-center justify-center gap-1.5">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
            <path d="M12 8V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
            <path d="M12 16H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          </svg>
          All payment information is securely processed and encrypted
        </p>
      </div>
    </div>
  );
}
