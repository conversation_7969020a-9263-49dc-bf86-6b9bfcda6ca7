import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { supabase } from "../../supabaseClient";

// Define base API
export const leadsApiNew = createApi({
  reducerPath: "leadsApiNew",
  baseQuery: fetchBaseQuery({
    baseUrl: "/api/leads",
    prepareHeaders: async (headers) => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session?.access_token) {
        headers.set("authorization", `Bearer ${session.access_token}`);
      }
      return headers;
    },
  }),
  tagTypes: [
    'Lead',
    'LeadNotification',
    'LeadByWorkspace',
    'LeadByUser',
    'LeadNotes'
  ],
  endpoints: (builder) => ({
    // GET endpoints
    getLeads: builder.query({
      query: ({ userId, sourceId }) => ({
        url: `/source/${sourceId}`,
      }),
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map(({ id }) => ({ type: 'Lead', id })),
              { type: 'Lead', id: 'LIST' },
            ]
          : [{ type: 'Lead', id: 'LIST' }],
    }),
    
    getLeadsByUser: builder.query({
      query: ({ userId }) => ({
        url: `/user/${userId}`,
      }),
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map(({ id }) => ({ type: 'LeadByUser', id })),
              { type: 'LeadByUser', id: 'LIST' },
            ]
          : [{ type: 'LeadByUser', id: 'LIST' }],
    }),
    
    getLeadsByWorkspace: builder.query({
      query: ({ workspaceId, limit = 12, offset = 0, sortBy = 'created_at', sortOrder = 'desc' }) => ({
        url: `/workspace/${workspaceId}?limit=${limit}&offset=${offset}&sortBy=${sortBy}&sortOrder=${sortOrder}`,
      }),
      providesTags: (result, error, arg) => [
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        ...(result?.data?.map(lead => ({ type: 'Lead', id: lead.id })) || [])
      ],
    }),
    
    getLeadById: builder.query({
      query: ({ id }) => ({
        url: `/${id}`,
      }),
      providesTags: (result, error, arg) => [{ type: 'Lead', id: arg.id }],
    }),
    
    getNotes: builder.query({
      query: ({ id }) => ({
        url: `/${id}/notes`,
        method: "GET",
      }),
      providesTags: (result, error, arg) => [
        { type: 'LeadNotes', id: arg.id },
        { type: 'LeadNotes', id: 'LIST' }
      ],
    }),
    
    leadNotification: builder.query({
      query: ({ workspaceId }) => ({
        url: `/notifications/${workspaceId}`,
      }),
      providesTags: (result, error, arg) => [
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
    
    // POST endpoints
    createLead: builder.mutation({
      query: ({ workspaceId, body }) => ({
        url: `/create?workspaceId=${workspaceId}`,
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' },
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
    
    createManyLead: builder.mutation({
      query: ({ workspaceId, body }) => ({
        url: `/bulk-create?workspaceId=${workspaceId}`,
        method: "POST",
        body,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' },
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
    
    addNotes: builder.mutation({
      query: ({ id, Note, workspaceId }) => ({
        url: `/${id}/notes?workspaceId=${workspaceId}`,
        method: "POST",
        body: Note,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Lead', id: arg.id },
        { type: 'LeadNotes', id: arg.id },
        { type: 'LeadNotes', id: 'LIST' },
        { type: 'LeadByWorkspace', id: arg.workspaceId }
      ],
    }),
    
    // PUT endpoints
    updateLead: builder.mutation({
      query: ({ id, leads, workspaceId }) => ({
        url: `/${id}?workspaceId=${workspaceId}`,
        method: "PUT",
        body: leads,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Lead', id: arg.id },
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' },
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
    
    updateLeadData: builder.mutation({
      query: ({ id, leads, workspaceId }) => ({
        url: `/${id}?workspaceId=${workspaceId}`,
        method: "PUT",
        body: leads,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Lead', id: arg.id },
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' }
      ],
    }),
    
    assignRole: builder.mutation({
      query: ({ id, body, workspaceId }) => ({
        url: `/${id}/assign?workspaceId=${workspaceId}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Lead', id: arg.id },
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' },
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
    
    // DELETE endpoints
    deleteLead: builder.mutation({
      query: ({ id, workspaceId }) => ({
        url: `/${id}?workspaceId=${workspaceId}`,
        method: "DELETE",
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'Lead', id: arg.id },
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' },
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
    
    bulkDeleteLeads: builder.mutation({
      query: ({ id, workspaceId }) => ({
        url: `/workspace/${workspaceId}`,
        method: "DELETE",
        body: { id },
      }),
      invalidatesTags: (result, error, arg) => [
        { type: 'LeadByWorkspace', id: arg.workspaceId },
        { type: 'LeadByWorkspace', id: 'LIST' },
        { type: 'Lead', id: 'LIST' },
        { type: 'LeadNotification', id: arg.workspaceId },
        { type: 'LeadNotification', id: 'LIST' }
      ],
    }),
  }),
});

// Export the hooks
export const {
  useGetLeadsQuery,
  useGetLeadsByUserQuery,
  useGetLeadsByWorkspaceQuery,
  useGetLeadByIdQuery,
  useAddNotesMutation,
  useGetNotesQuery,
  useCreateLeadMutation,
  useCreateManyLeadMutation,
  useUpdateLeadMutation,
  useDeleteLeadMutation,
  useUpdateLeadDataMutation,
  useAssignRoleMutation,
  useBulkDeleteLeadsMutation,
  useLeadNotificationQuery,
} = leadsApiNew;

// Export the API for direct access to util methods
export { leadsApiNew as leadsApiNewExtended };
