import { useMemo } from "react";
import { RecentLead, RecentSale, TeamPerformance } from "../types/dashboard";
import { formatRecentLeads, calculateTeamPerformance } from "../utils/dashboardUtils";

interface UseRecentSalesProps {
  recentLeads: RecentLead[];
}

export const useRecentSales = ({ recentLeads }: UseRecentSalesProps) => {
  const recentSales: RecentSale[] = useMemo(() => {
    return formatRecentLeads(recentLeads);
  }, [recentLeads]);

  const teamPerformance: TeamPerformance[] = useMemo(() => {
    return calculateTeamPerformance(recentSales);
  }, [recentSales]);

  const hasRecentSales = useMemo(() => {
    return recentSales.length > 0;
  }, [recentSales]);

  const hasTeamPerformance = useMemo(() => {
    return teamPerformance.length > 0;
  }, [teamPerformance]);

  return {
    recentSales,
    teamPerformance,
    hasRecentSales,
    hasTeamPerformance,
  };
};
