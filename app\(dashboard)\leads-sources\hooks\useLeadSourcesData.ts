import { useState, useEffect } from "react";
import { useGetActiveWorkspaceQuery } from "@/lib/store/services/workspace";
import { useGetWebhooksQuery } from "@/lib/store/services/webhooks";
import { LeadSource, LeadSourceLoadingStates } from "../types/leadSources";

export const useLeadSourcesData = () => {
  // Get active workspace
  const {
    data: workspaceData,
    isLoading: isWorkspaceLoading,
    error: workspaceError,
  } = useGetActiveWorkspaceQuery();

  // Get webhooks/lead sources
  const {
    data: webhooksResponse,
    isLoading: isSourcesLoading,
    isError: isSourcesError,
    error: sourcesError,
  } = useGetWebhooksQuery(
    { id: workspaceData?.data?.id },
    { skip: !workspaceData?.data?.id }
  );

  // Local state for sources
  const [sources, setSources] = useState<LeadSource[]>([]);

  // Update sources when webhooks data changes
  useEffect(() => {
    if (webhooksResponse?.data) {
      setSources(webhooksResponse.data);
    }
  }, [webhooksResponse]);

  // Loading states
  const loadingStates: LeadSourceLoadingStates = {
    isWorkspaceLoading,
    isSourcesLoading,
    isWebhookAdding: false,
    isDeleting: false,
    isUpdating: false,
    isStatusChanging: false,
  };

  // Check if any critical data is loading
  const isInitialLoading = isWorkspaceLoading || isSourcesLoading;

  // Check if workspace is available
  const hasWorkspace = !!workspaceData?.data?.id;

  return {
    // Data
    workspaceData,
    sources,
    setSources,
    
    // Loading states
    loadingStates,
    isInitialLoading,
    
    // Status
    hasWorkspace,
    isSourcesError,
    
    // Errors
    workspaceError,
    sourcesError,
  };
};
