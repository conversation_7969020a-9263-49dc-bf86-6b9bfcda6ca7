import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../utils/auth";

/**
 * Get the active workspace for the current user
 * @route GET /api/workspace/active
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    try {
      // First check if there's already an active workspace
      let { data: activeWorkspace, error: activeError } = await supabase
        .from("workspace_members")
        .select(`
          workspace_id,
          role,
          is_active,
          workspaces (*)
        `)
        .eq("user_id", user.id)
        .eq("is_active", true)
        .eq("status", "accepted")
        .single();
      
      // If no active workspace found, set the first available one as active
      if (!activeWorkspace || activeError) {
        // Get the first workspace where user is a member
        const { data: firstWorkspace, error: firstError } = await supabase
          .from("workspace_members")
          .select(`
            id,
            workspace_id,
            role,
            workspaces (*)
          `)
          .eq("user_id", user.id)
          .eq("status", "accepted")
          .order("created_at", { ascending: true })
          .limit(1)
          .single();
        
        if (firstError) {
          console.error("Error getting first workspace:", firstError);
          return NextResponse.json({ 
            error: "No workspaces found for user" 
          }, { status: 404 });
        }
        
        if (firstWorkspace) {
          // Deactivate all workspaces first
          await supabase
            .from("workspace_members")
            .update({ is_active: false })
            .eq("user_id", user.id);
          
          // Set the first workspace as active
          const { error: setActiveError } = await supabase
            .from("workspace_members")
            .update({ is_active: true })
            .eq("id", firstWorkspace.id);
          
          if (setActiveError) {
            throw setActiveError;
          }
          
          // Return the newly activated workspace
          return NextResponse.json({
            data: {
              ...firstWorkspace.workspaces,
              role: firstWorkspace.role,
              is_active: true,
            }
          }, { status: 200 });
        }
        
        return NextResponse.json({ error: "No workspaces found" }, { status: 404 });
      }
      
      // Return the existing active workspace
      return NextResponse.json({
        data: {
          ...activeWorkspace.workspaces,
          role: activeWorkspace.role,
          is_active: true,
        }
      }, { status: 200 });
    } catch (error) {
      console.error("Error getting active workspace:", error);
      return NextResponse.json({ 
        error: "Failed to get active workspace" 
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error getting active workspace:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
