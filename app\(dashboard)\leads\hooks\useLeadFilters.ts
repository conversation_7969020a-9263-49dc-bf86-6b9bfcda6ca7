import { useState, useMemo } from "react";
import { Lead, LeadFilters, LeadSource } from "../types/leads";
import { initialFilters } from "../constants/leads";

interface UseLeadFiltersProps {
  leads: Lead[];
  leadSources?: { data: LeadSource[] };
  searchQuery: string;
}

export const useLeadFilters = ({ leads, leadSources, searchQuery }: UseLeadFiltersProps) => {
  const [filters, setFilters] = useState<LeadFilters>(initialFilters);
  const [showFilters, setShowFilters] = useState(false);

  const filteredLeads = useMemo(() => {
    return leads.filter((lead) => {
      // Search query filter
      if (searchQuery) {
        const searchText = searchQuery.toLowerCase();
        const searchableFields = [
          lead.Name,
          lead.email,
          lead.phone,
          lead.company,
          lead.position,
          lead.status?.name,
          lead.assign_to?.name,
        ];

        const matchesSearch = searchableFields.some(
          (field) =>
            field && field.toString().toLowerCase().includes(searchText)
        );

        if (!matchesSearch) return false;
      }

      // Owner filter
      if (filters.owner && !lead.assign_to?.name?.includes(filters.owner))
        return false;

      // Lead source filter
      if (filters.leadSource && filters.leadSource !== "all") {
        // Step 1: Find the leadSourceId
        let leadSourceId = leadSources?.data.find(
          (source: LeadSource) => source?.name === filters?.leadSource
        )?.id;

        // Step 2: Find the webhook_url in workspaceData based on leadSourceId
        let webhook_url = leadSources?.data.find(
          (entry: LeadSource) => entry?.id === leadSourceId
        )?.webhook_url;

        // Step 3: Extract sourceId from webhook_url
        let sourceId: string | null = null;
        if (webhook_url) {
          const urlParams = new URLSearchParams(webhook_url.split("?")[1]);
          sourceId = urlParams.get("sourceId");
        }

        if (leadSourceId && lead.sourceId !== sourceId) return false;
      }

      // Status filter
      if (filters.status && lead.status?.name !== filters.status) return false;

      // Contact Method filter
      if (
        filters.contact_method &&
        lead.contact_method !== filters.contact_method
      )
        return false;

      // Contact Type filter
      if (filters.contactType) {
        if (filters.contactType === "phone" && !lead.phone) return false;
        if (filters.contactType === "email" && !lead.email) return false;
        if (filters.contactType === "id" && !lead.id) return false;
      }

      // Date range filter
      if (
        filters.startDate &&
        new Date(lead.createdAt) < new Date(filters.startDate)
      )
        return false;
      if (
        filters.endDate &&
        new Date(lead.createdAt) > new Date(filters.endDate)
      )
        return false;

      // Duplicate check
      if (filters.showDuplicates) {
        const duplicates = leads.filter(
          (l) => l.email === lead.email || l.phone === lead.phone
        );
        if (duplicates.length <= 1) return false;
      }

      return true;
    });
  }, [leads, filters, searchQuery, leadSources]);

  const handleFilterChange = (newFilters: LeadFilters) => {
    setFilters(newFilters);
  };

  const handleFilterReset = () => {
    setFilters(initialFilters);
    setShowFilters(false);
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return {
    filters,
    filteredLeads,
    showFilters,
    handleFilterChange,
    handleFilterReset,
    toggleFilters,
    setFilters,
  };
};
