import React from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface LeadSourcesSkeletonProps {
  isCollapsed?: boolean;
}

export const LeadSourcesSkeleton: React.FC<LeadSourcesSkeletonProps> = ({ isCollapsed = false }) => {
  return (
    <div className={`transition-all duration-500 ease-in-out md:px-4 md:py-6 py-2 px-2 ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} w-auto overflow-hidden`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        {/* Header Skeleton */}
        <CardHeader className="flex flex-row justify-between items-center bg-white dark:bg-black">
          <div className="flex gap-6 items-center">
            <Skeleton className="md:hidden lg:hidden h-4 w-4" />
            <Skeleton className="h-6 w-32 md:h-8 md:w-40" />
          </div>
          <Skeleton className="h-9 w-28 md:w-32" />
        </CardHeader>

        <CardContent>
          {/* Desktop Table Skeleton */}
          <div className="hidden md:block overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800">
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-16" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-12" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-20" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-14" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-24" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-28" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-16" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-14" /></TableHead>
                  <TableHead className="py-3 px-4"><Skeleton className="h-4 w-16" /></TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {Array.from({ length: 6 }).map((_, index) => (
                  <TableRow key={index} className="border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                    <TableCell className="py-3 px-4"><Skeleton className="h-4 w-24" /></TableCell>
                    <TableCell className="py-3 px-4"><Skeleton className="h-4 w-16" /></TableCell>
                    <TableCell className="py-3 px-4"><Skeleton className="h-4 w-32" /></TableCell>
                    <TableCell className="py-3 px-4"><Skeleton className="h-4 w-8" /></TableCell>
                    <TableCell className="py-3 px-4"><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell className="py-3 px-4"><Skeleton className="h-4 w-12" /></TableCell>
                    <TableCell className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-4 w-40" />
                        <Skeleton className="h-8 w-8" />
                      </div>
                    </TableCell>
                    <TableCell className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <Skeleton className="h-5 w-9 rounded-full" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </TableCell>
                    <TableCell className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Skeleton className="h-8 w-8" />
                        <Skeleton className="h-8 w-8" />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Mobile View Skeleton */}
          <div className="block md:hidden space-y-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="flex items-center justify-between border-b border-gray-200 dark:border-gray-800 p-2 last:border-none hover:bg-gray-50 dark:hover:bg-gray-800/50">
                <div className="flex flex-col gap-1">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-3 w-20" />
                </div>
                <Skeleton className="h-8 w-8 rounded-md" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Mobile Skeleton Component
export const LeadSourcesMobileSkeleton: React.FC = () => {
  return (
    <div className="p-4 space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <Skeleton className="h-6 w-6" />
          <Skeleton className="h-6 w-32" />
        </div>
        <Skeleton className="h-9 w-24" />
      </div>

      {/* Mobile Cards */}
      {Array.from({ length: 5 }).map((_, index) => (
        <Card key={index} className="p-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
          <div className="flex justify-between items-start mb-3">
            <div className="flex-1">
              <Skeleton className="h-5 w-32 mb-2" />
              <Skeleton className="h-4 w-20 mb-2" />
              <Skeleton className="h-3 w-40" />
            </div>
            <Skeleton className="h-8 w-8 rounded-md" />
          </div>

          <div className="space-y-2">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-4 w-8" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-12" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-20" />
              <div className="flex items-center space-x-2">
                <Skeleton className="h-5 w-9 rounded-full" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
            <div className="flex justify-between items-center pt-2">
              <Skeleton className="h-4 w-16" />
              <div className="flex space-x-2">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Empty State Skeleton
export const EmptyStateSkeleton: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <Skeleton className="h-16 w-16 rounded-full mb-4" />
      <Skeleton className="h-6 w-48 mb-2" />
      <Skeleton className="h-4 w-64 mb-6" />
      <Skeleton className="h-10 w-32" />
    </div>
  );
};
