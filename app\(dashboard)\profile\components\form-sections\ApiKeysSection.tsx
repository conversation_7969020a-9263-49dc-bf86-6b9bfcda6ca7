"use client";

import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ProfileFormData } from '../../types/profile';

interface ApiKeysSectionProps {
  formData: ProfileFormData;
  updateFormData: (field: keyof ProfileFormData, value: string) => void;
}

export function ApiKeysSection({
  formData,
  updateFormData,
}: ApiKeysSectionProps) {
  return (
    <>
      {/* OpenAI API Key */}
      <div className="space-y-2">
        <Label htmlFor="openAI">OpenAI API Key</Label>
        <Input
          id="openAI"
          type="password"
          value={formData.openAI}
          onChange={(e) => updateFormData('openAI', e.target.value)}
          placeholder="sk-..."
        />
      </div>

      {/* Together API Key */}
      <div className="space-y-2">
        <Label htmlFor="togetherAPI">Together API Key</Label>
        <Input
          id="togetherAPI"
          type="password"
          value={formData.togetherAPI}
          onChange={(e) => updateFormData('togetherAPI', e.target.value)}
          placeholder="Enter Together API key"
        />
      </div>

      {/* Reoon Email API Key */}
      <div className="space-y-2">
        <Label htmlFor="reoonEmail">Reoon Email API Key</Label>
        <Input
          id="reoonEmail"
          type="password"
          value={formData.reoonEmail}
          onChange={(e) => updateFormData('reoonEmail', e.target.value)}
          placeholder="Enter Reoon Email API key"
        />
      </div>

      {/* Big Data Cloud API Key */}
      <div className="space-y-2">
        <Label htmlFor="bigDataCloud">Big Data Cloud API Key</Label>
        <Input
          id="bigDataCloud"
          type="password"
          value={formData.bigDataCloud}
          onChange={(e) => updateFormData('bigDataCloud', e.target.value)}
          placeholder="Enter Big Data Cloud API key"
        />
      </div>
    </>
  );
}
