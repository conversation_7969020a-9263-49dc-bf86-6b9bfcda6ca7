import React, { forwardRef } from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Check,
  X,
  Send,
  Phone,
  MessageCircle,
  Pencil,
  Eye,
  UserIcon,
} from "lucide-react";
import { Lead, WorkspaceMember, Status } from "../types/leads";
import { formatDate } from "@/utils/date";
import {
  getLeadStatusDisplay,
  getLeadAssignmentDisplay,
  createStatusSelectValue,
  createMemberSelectValue
} from "../utils/leadDataUtils";

interface LeadTableRowProps {
  lead: Lead;
  isSelected: boolean;
  workspaceMembers?: { data: WorkspaceMember[] };
  statusData?: { data: Status[] };
  onToggleSelection: () => void;
  onEdit: () => void;
  onView: () => void;
  onStatusChange: (value: string) => void;
  onAssignChange: (value: string) => void;
  onInitiateContact: (method: string) => void;
}

export const LeadTableRow = forwardRef<HTMLTableRowElement, LeadTableRowProps>(
  (
    {
      lead,
      isSelected,
      workspaceMembers,
      statusData,
      onToggleSelection,
      onEdit,
      onView,
      onStatusChange,
      onAssignChange,
      onInitiateContact,
    },
    ref
  ) => {
    const getContactIcon = (method: string) => {
      switch (method) {
        case "WhatsApp":
          return <Send className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />;
        case "Call":
          return <Phone className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />;
        case "SMS":
          return <MessageCircle className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />;
        default:
          return <MessageCircle className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />;
      }
    };

    return (
      <TableRow
        ref={ref}
        className="hidden md:table-row border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
      >
        <TableCell className="py-3 px-4">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onToggleSelection}
            className="h-4 w-4"
          />
        </TableCell>

        <TableCell className="py-3 px-4">
          <div className="flex flex-col">
            <div className="font-medium text-gray-900 dark:text-gray-100">
              {lead.Name}
            </div>
            {lead.company && (
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                {lead.company} {lead.position && `• ${lead.position}`}
              </div>
            )}
            {lead.isDuplicate && (
              <span className="inline-flex items-center px-2 py-0.5 mt-1 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 w-fit">
                Duplicate
              </span>
            )}
          </div>
        </TableCell>

        <TableCell className="py-3 px-4">
          <div className="flex items-center">
            {lead.is_email_valid ? (
              <div className="w-5 h-5 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mr-2 flex-shrink-0">
                <Check className="w-3 h-3 text-emerald-600 dark:text-emerald-400" />
              </div>
            ) : (
              <div className="w-5 h-5 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-2 flex-shrink-0">
                <X className="w-3 h-3 text-red-600 dark:text-red-400" />
              </div>
            )}
            <div>
              <span
                className={`text-sm ${
                  lead.is_email_valid
                    ? "text-gray-700 dark:text-gray-300"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {lead.email}
              </span>
              {!lead.is_email_valid && (
                <div className="text-xs text-red-600 dark:text-red-400 mt-0.5">
                  Invalid Email
                </div>
              )}
            </div>
          </div>
        </TableCell>

        <TableCell className="py-3 px-4">
          <div className="flex items-center">
            {lead.is_phone_valid ? (
              <div className="w-5 h-5 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mr-2 flex-shrink-0">
                <Check className="w-3 h-3 text-emerald-600 dark:text-emerald-400" />
              </div>
            ) : (
              <div className="w-5 h-5 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mr-2 flex-shrink-0">
                <X className="w-3 h-3 text-red-600 dark:text-red-400" />
              </div>
            )}
            <div>
              <span
                className={`text-sm ${
                  lead.is_phone_valid
                    ? "text-gray-700 dark:text-gray-300"
                    : "text-red-600 dark:text-red-400"
                }`}
              >
                {lead.phone}
              </span>
              {!lead.is_phone_valid && (
                <div className="text-xs text-red-600 dark:text-red-400 mt-0.5">
                  Invalid Phone
                </div>
              )}
            </div>
          </div>
        </TableCell>

        <TableCell className="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">
          {formatDate(lead.createdAt)}
        </TableCell>

        <TableCell className="py-3 px-4">
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onInitiateContact(lead.contact_method)}
              className="h-8 w-8 p-0 border-gray-200 dark:border-gray-700"
              title={`Contact via ${lead.contact_method}`}
            >
              {getContactIcon(lead.contact_method)}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              className="h-8 w-8 p-0 border-gray-200 dark:border-gray-700"
            >
              <Pencil className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onView}
              className="h-8 w-8 p-0 border-gray-200 dark:border-gray-700"
            >
              <Eye className="h-3.5 w-3.5 text-gray-600 dark:text-gray-400" />
            </Button>
          </div>
        </TableCell>

        <TableCell className="py-3 px-4">
          {(() => {
            const statusDisplay = getLeadStatusDisplay(lead, statusData?.data || []);
            return (
              <Select
                defaultValue={createStatusSelectValue(lead.status_id, statusData?.data || [], lead.status)}
                onValueChange={onStatusChange}
              >
                <SelectTrigger className="w-[160px] h-9 border border-gray-200 dark:border-gray-800 bg-white dark:bg-black rounded-md shadow-sm">
                  <div className="flex items-center gap-2 text-sm">
                    <div
                      className="h-3 w-3 rounded-full flex-shrink-0"
                      style={{
                        backgroundColor: statusDisplay.color,
                      }}
                    />
                    <span className="truncate font-medium text-black dark:text-white">
                      {statusDisplay.name}
                    </span>
                  </div>
                </SelectTrigger>
            );
          })()}

                <SelectContent className="border border-gray-200 dark:border-gray-800 bg-white dark:bg-black rounded-md shadow-md">
                  {statusData?.data.map((status: Status) => (
                    <SelectItem
                      key={status.id || status.name}
                      value={JSON.stringify({
                        id: status.id,
                        name: status.name,
                        color: status.color,
                      })}
                      className="cursor-pointer focus:bg-gray-50 dark:focus:bg-gray-900/50"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="h-3 w-3 rounded-full flex-shrink-0"
                          style={{
                            backgroundColor: status.color,
                          }}
                        />
                        <span className="text-sm font-medium text-black dark:text-white">
                          {status.name}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            );
          })()}
        </TableCell>

        <TableCell className="py-3 px-4">
          {(() => {
            const assignmentDisplay = getLeadAssignmentDisplay(lead, workspaceMembers?.data || []);
            return (
              <Select
                defaultValue={createMemberSelectValue(lead.assigned_to_id, workspaceMembers?.data || [], lead.assign_to)}
                onValueChange={onAssignChange}
              >
                <SelectTrigger className="w-[160px] h-9 border border-gray-200 dark:border-gray-800 bg-white dark:bg-black rounded-md shadow-sm">
                  <div className="flex items-center gap-2 text-sm">
                    <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center flex-shrink-0">
                      <UserIcon className="h-3 w-3 text-black dark:text-white" />
                    </div>
                    <span className="truncate font-medium text-black dark:text-white">
                      {assignmentDisplay.name}
                    </span>
                  </div>
                </SelectTrigger>
            );
          })()}

                <SelectContent className="border border-gray-200 dark:border-gray-800 bg-white dark:bg-black rounded-md shadow-md">
                  <SelectItem
                    key="unassigned"
                    value={JSON.stringify({
                      id: null,
                      name: "Unassigned",
                      role: "none",
                    })}
                    className="cursor-pointer focus:bg-gray-50 dark:focus:bg-gray-900/50"
                  >
                    <div className="flex items-center gap-2">
                      <div className="h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-900 flex items-center justify-center flex-shrink-0">
                        <UserIcon className="h-3 w-3 text-black dark:text-white" />
                      </div>
                      <span className="text-sm font-medium text-black dark:text-white">
                        Unassigned
                      </span>
                    </div>
                  </SelectItem>
                  {workspaceMembers?.data
                    .filter((member: WorkspaceMember) => member.name && member.name !== "null")
                    .map((member: WorkspaceMember) => (
                      <SelectItem
                        key={member.id || member.name}
                        value={JSON.stringify({
                          id: member.id,
                          name: member.name,
                          role: member.role,
                        })}
                        className="cursor-pointer focus:bg-gray-50 dark:focus:bg-gray-700/50"
                      >
                        <div className="flex items-center gap-2">
                          <div className="h-5 w-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                            <UserIcon className="h-3 w-3 text-primary" />
                          </div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {member.name}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            );
          })()}
        </TableCell>
      </TableRow>
    );
  }
);

LeadTableRow.displayName = "LeadTableRow";
