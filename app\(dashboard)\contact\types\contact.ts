export interface Contact {
  id: number;
  Name: string;
  name?: string;
  email: string;
  phone: string;
  company?: string;
  position?: string;
  contact_method?: "WhatsApp" | "SMS" | "Call";
  owner?: string;
  status: {
    name: string;
    color?: string;
  };
  revenue?: number;
  assign_to?: {
    name: string;
    role: string;
  } | string;
  createdAt: string;
  isDuplicate: boolean;
  is_email_valid: boolean;
  is_phone_valid: boolean;
  sourceId?: string | null;
  businessInfo?: string;
  tag?: any;
  address?: string;
  address1?: string;
  address2?: string;
  country?: string;
  zipCode?: string;
  lastContact?: string;
}

export interface ContactFilters {
  search: string;
  statusFilter: string;
  currentPage: number;
  itemsPerPage: number;
}

export interface ContactTableColumn {
  key: string;
  label: string;
  width: number;
  sortable?: boolean;
  editable?: boolean;
}

export interface Tag {
  id?: string;
  name: string;
  color: string;
}

export interface AddressData {
  address1: string;
  address2: string;
  country: string;
  zipCode: string;
}

export interface ContactUpdateData {
  businessInfo?: string;
  tags?: string[];
  address?: string;
  email?: string;
  name?: string;
  phone?: string;
  is_email_valid?: boolean;
}

export interface EditState {
  editNameId: number | null;
  editEmailId: number | null;
  editPhoneId: number | null;
  editInfoId: number | null;
  editEmailValidationId: number | null;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  status: string;
}
