"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Copy, Share, Code, Check } from "lucide-react";
import { toast } from "sonner";

interface EmbedCodeDialogProps {
  formId: string | null;
  formName: string;
  children?: React.ReactNode;
}

export default function EmbedCodeDialog({ formId, formName, children }: EmbedCodeDialogProps) {
  const [width, setWidth] = useState(100);
  const [height, setHeight] = useState(500);
  const [isResponsive, setIsResponsive] = useState(true);
  const [showBorder, setShowBorder] = useState(false);
  const [copied, setCopied] = useState(false);
  const [activeTab, setActiveTab] = useState("iframe");

  // Generate the embed code based on current settings
  const generateEmbedCode = () => {
    if (!formId) {
      return `<!-- Please save the form first to get the embed code -->`;
    }

    const origin = typeof window !== "undefined" ? window.location.origin : "";
    const safeFormId = formId; // formId is already validated to be non-null at this point

    if (activeTab === "iframe") {
      const responsiveStyles = isResponsive
        ? `style="width: 100%; max-width: ${width}%; height: ${height}px;"`
        : `width="${width}%" height="${height}"`;

      const borderStyle = showBorder ? "" : 'frameborder="0" style="border: none;"';

      return `<iframe
  src="${origin}/embed/forms/${safeFormId}"
  ${responsiveStyles}
  ${borderStyle}
  title="${formName || 'Contact Form'}"
  loading="lazy"
  allowtransparency="true"
></iframe>`;
    } else if (activeTab === "script") {
      return `<script>
  (function(w,d,s,o,f,js,fjs){
    w['CRM-Form-Widget']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
    js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
  }(window,document,'script','crmForm','${origin}/embed/forms/loader.js'));

  crmForm('init', {
    formId: '${safeFormId}',
    container: 'crm-form-container',
    width: '${width}%',
    height: ${height},
    responsive: ${isResponsive},
    showBorder: ${showBorder}
  });
</script>
<div id="crm-form-container"></div>`;
    } else {
      return `<div data-crm-form="${safeFormId}"
  data-width="${width}%"
  data-height="${height}"
  data-responsive="${isResponsive}"
  data-show-border="${showBorder}">
</div>
<script src="${origin}/embed/forms/loader.js" async></script>`;
    }
  };

  // Copy the embed code to clipboard
  const copyEmbedCode = () => {
    if (!formId) {
      toast.error("Please save the form first to get the embed code");
      return;
    }

    const code = generateEmbedCode();
    navigator.clipboard.writeText(code);
    setCopied(true);
    toast.success("Embed code copied to clipboard");

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm" className="gap-2">
            <Code className="h-4 w-4" />
            Embed
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Embed Form</DialogTitle>
          <DialogDescription>
            Copy the embed code to add this form to your website.
          </DialogDescription>
        </DialogHeader>

        {!formId ? (
          <div className="py-6 text-center">
            <p className="text-muted-foreground mb-4">
              Please save the form first to get the embed code.
            </p>
            <Button variant="outline" onClick={() => document.querySelector('[role="dialog"] button[data-state="open"]')?.click()}>
              Close
            </Button>
          </div>
        ) : (
          <>
            <Tabs defaultValue="iframe" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="iframe">iFrame</TabsTrigger>
                <TabsTrigger value="script">JavaScript</TabsTrigger>
                <TabsTrigger value="div">Div Element</TabsTrigger>
              </TabsList>

              <div className="space-y-4 mb-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="width">Width: {width}%</Label>
                    <Slider
                      id="width"
                      min={10}
                      max={100}
                      step={1}
                      value={[width]}
                      onValueChange={(value) => setWidth(value[0])}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="height">Height: {height}px</Label>
                    <Slider
                      id="height"
                      min={200}
                      max={800}
                      step={10}
                      value={[height]}
                      onValueChange={(value) => setHeight(value[0])}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="responsive"
                      checked={isResponsive}
                      onCheckedChange={setIsResponsive}
                    />
                    <Label htmlFor="responsive">Responsive</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="border"
                      checked={showBorder}
                      onCheckedChange={setShowBorder}
                    />
                    <Label htmlFor="border">Show Border</Label>
                  </div>
                </div>
              </div>

              <TabsContent value="iframe" className="mt-0">
                <div className="bg-muted p-4 rounded-md font-mono text-xs overflow-x-auto whitespace-pre-wrap">
                  {generateEmbedCode()}
                </div>
              </TabsContent>

              <TabsContent value="script" className="mt-0">
                <div className="bg-muted p-4 rounded-md font-mono text-xs overflow-x-auto whitespace-pre-wrap">
                  {generateEmbedCode()}
                </div>
              </TabsContent>

              <TabsContent value="div" className="mt-0">
                <div className="bg-muted p-4 rounded-md font-mono text-xs overflow-x-auto whitespace-pre-wrap">
                  {generateEmbedCode()}
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-muted-foreground">
                This code will embed your form on any website.
              </div>
              <Button onClick={copyEmbedCode} className="gap-2">
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                {copied ? "Copied!" : "Copy Code"}
              </Button>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
