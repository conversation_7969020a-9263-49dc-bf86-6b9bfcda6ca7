"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface WorkspaceSkeletonProps {
  isCollapsed: boolean;
}

export function WorkspaceSkeleton({ isCollapsed }: WorkspaceSkeletonProps) {
  return (
    <div
      className={`transition-all duration-500 ease-in-out px-1 py-2 md:px-4 md:py-6 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden`}
    >
      <div className="container mx-auto p-2 md:p-6 space-y-6">
        {/* Header Skeleton */}
        <div className="flex flex-col space-y-4">
          <Skeleton className="h-8 w-64" />
          
          {/* Tab Navigation Skeleton */}
          <div className="grid grid-cols-3 text-[15px] md:flex md:flex-row md:gap-2 gap-1 overflow-x-auto">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Skeleton key={i} className="h-10 w-24 md:w-32" />
            ))}
          </div>
        </div>

        {/* Content Skeleton */}
        <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
          <CardHeader className="p-4 md:p-6">
            <div className="space-y-2">
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
          </CardHeader>
          
          <CardContent className="p-4 md:p-6 pt-0 space-y-6">
            {/* Form Fields Skeleton */}
            <div className="space-y-4">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-10 w-full" />
                </div>
              ))}
            </div>

            {/* Action Buttons Skeleton */}
            <div className="flex gap-2 justify-end">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </CardContent>
        </Card>

        {/* Additional Content Skeleton */}
        <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
          <CardHeader className="p-4 md:p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
          </CardHeader>
          
          <CardContent className="p-4 md:p-6 pt-0 space-y-4">
            {/* List Items Skeleton */}
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="flex items-center justify-between p-3 bg-secondary rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <Skeleton className="w-8 h-8 rounded-full" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-8 rounded" />
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Mobile-specific skeleton
export function WorkspaceMobileSkeleton({ isCollapsed }: WorkspaceSkeletonProps) {
  return (
    <div
      className={`transition-all duration-500 ease-in-out px-2 py-4 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden`}
    >
      <div className="container mx-auto space-y-4">
        {/* Mobile Header */}
        <div className="space-y-3">
          <Skeleton className="h-6 w-48" />
          
          {/* Mobile Tab Navigation */}
          <div className="grid grid-cols-3 gap-1">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Skeleton key={i} className="h-8 w-full" />
            ))}
          </div>
        </div>

        {/* Mobile Content */}
        <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
          <CardHeader className="p-3">
            <div className="space-y-2">
              <Skeleton className="h-5 w-36" />
              <Skeleton className="h-3 w-full" />
            </div>
          </CardHeader>
          
          <CardContent className="p-3 pt-0 space-y-4">
            {/* Mobile Form Fields */}
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="space-y-1">
                  <Skeleton className="h-3 w-24" />
                  <Skeleton className="h-9 w-full" />
                </div>
              ))}
            </div>

            {/* Mobile Actions */}
            <div className="flex gap-2">
              <Skeleton className="h-9 flex-1" />
              <Skeleton className="h-9 flex-1" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
