import { RecentLead, RecentSale, TeamPerformance, SalesData } from "../types/dashboard";
import { DUMMY_MONTHS } from "../constants/dashboard";

/**
 * Safely converts workspace ID to string
 */
export const getWorkspaceId = (activeWorkspace: any): string | undefined => {
  const id = activeWorkspace?.data?.id;
  if (!id) return undefined;
  
  return typeof id === 'string' ? id : id.toString();
};

/**
 * Safely parses and formats revenue
 */
export const formatRevenue = (revenue: any): string => {
  if (revenue === undefined || revenue === null) return "0.00";
  
  const revenueStr = String(revenue || '0');
  const parsedRevenue = typeof revenue === 'number' ? revenue : parseFloat(revenueStr);
  
  return isNaN(parsedRevenue) ? "0.00" : parsedRevenue.toFixed(2);
};

/**
 * Safely parses and formats conversion rate
 */
export const formatConversionRate = (rate: any): string => {
  if (rate === undefined || rate === null) return "0.0%";
  
  const rateStr = String(rate || '0');
  const parsedRate = typeof rate === 'number' ? rate : parseFloat(rateStr);
  
  return isNaN(parsedRate) ? "0.0%" : `${parsedRate.toFixed(1)}%`;
};

/**
 * Transforms monthly stats for chart display
 */
export const transformMonthlyStats = (monthlyStats: any[]): SalesData[] => {
  if (!Array.isArray(monthlyStats) || monthlyStats.length === 0) {
    // Create dummy data for current year
    const currentYear = new Date().getFullYear();
    return DUMMY_MONTHS.map(month => ({
      month: `${month} ${currentYear}`,
      sales: 0,
      leads: 0
    }));
  }

  return monthlyStats.map((stat: any) => ({
    month: stat.month || 'Unknown',
    sales: typeof stat.convertedLeads === 'number' 
      ? stat.convertedLeads 
      : (stat.convertedLeads ? parseInt(stat.convertedLeads) : 0),
    leads: typeof stat.totalLeads === 'number' 
      ? stat.totalLeads 
      : (stat.totalLeads ? parseInt(stat.totalLeads) : 0)
  }));
};

/**
 * Formats recent leads for display
 */
export const formatRecentLeads = (leads: RecentLead[]): RecentSale[] => {
  return leads.map(lead => {
    const initials = lead.name
      ? lead.name.split(' ').map((n: string) => n[0]).join('').toUpperCase().substring(0, 2)
      : 'NA';

    const assignedUser = lead.users || null;
    const assignedUserName = assignedUser?.user_metadata?.name || assignedUser?.email || 'Unassigned';
    const assignedUserId = assignedUser?.id || 'unknown';

    return {
      id: initials,
      name: lead.name || 'Unknown',
      email: lead.email || 'No email',
      amount: `+$${lead.revenue ? lead.revenue.toFixed(2) : '0.00'}`,
      revenue: lead.revenue || 0,
      assignedTo: assignedUserName,
      assignedUserId: assignedUserId,
      date: lead.created_at ? new Date(lead.created_at).toLocaleDateString() : 'Unknown date'
    };
  });
};

/**
 * Calculates team performance from recent sales
 */
export const calculateTeamPerformance = (recentSales: RecentSale[]): TeamPerformance[] => {
  const teamPerformance = recentSales.reduce((acc: Record<string, TeamPerformance>, sale) => {
    const { assignedUserId, assignedTo, revenue } = sale;

    if (!acc[assignedUserId]) {
      acc[assignedUserId] = {
        name: assignedTo,
        totalSales: 0,
        totalRevenue: 0
      };
    }

    acc[assignedUserId].totalSales += 1;
    acc[assignedUserId].totalRevenue += revenue;

    return acc;
  }, {});

  return Object.values(teamPerformance).sort((a, b) => b.totalRevenue - a.totalRevenue);
};

/**
 * Checks if revenue data exists and is valid
 */
export const hasValidRevenue = (revenue: string): boolean => {
  return parseFloat(revenue) > 0;
};

/**
 * Checks if conversion rate is valid
 */
export const hasValidConversionRate = (rate: any): boolean => {
  return rate !== undefined && rate !== null && parseFloat(String(rate)) > 0;
};

/**
 * Generates initials from a name
 */
export const generateInitials = (name: string): string => {
  if (!name) return 'NA';
  return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
};

/**
 * Formats number with locale string
 */
export const formatNumber = (value: number): string => {
  return Number(value).toLocaleString();
};

/**
 * Calculates conversion percentage
 */
export const calculateConversionPercentage = (converted: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((converted / total) * 100);
};
