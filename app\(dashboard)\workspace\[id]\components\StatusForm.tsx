"use client";

import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { StatusFormProps } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';

export function StatusForm({ status, onSubmit }: StatusFormProps) {
  return (
    <div className="grid gap-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder={WORKSPACE_CONSTANTS.UI_TEXT.STATUS_NAME_PLACEHOLDER}
          value={status.name}
          onChange={(e) => onSubmit({ ...status, name: e.target.value })}
          className="flex-1"
        />
        <div className="flex items-center gap-2">
          <Label htmlFor="color" className="whitespace-nowrap">
            {WORKSPACE_CONSTANTS.UI_TEXT.PICK_COLOR_LABEL}
          </Label>
          <Input
            id="color"
            type="color"
            value={status.color}
            onChange={(e) => onSubmit({ ...status, color: e.target.value })}
            className="w-20 h-10 p-1 bg-transparent"
          />
        </div>
      </div>

      {'count_statistics' in status && (
        <div className="flex items-center gap-2">
          <Checkbox
            id="countInStatistics"
            checked={status.count_statistics}
            onCheckedChange={(checked) =>
              onSubmit({
                ...status,
                count_statistics: checked as boolean,
              })
            }
          />
          <Label htmlFor="countInStatistics" className="text-sm">
            {WORKSPACE_CONSTANTS.UI_TEXT.COUNT_QUALIFIED_LABEL}
          </Label>
        </div>
      )}
    </div>
  );
}
