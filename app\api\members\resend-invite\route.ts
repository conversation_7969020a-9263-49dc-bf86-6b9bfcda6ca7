import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspaceAdminPermission } from "../utils/auth";
import { sendInvitationEmail } from "../utils/email";

/**
 * Resend invitation to a member
 * @route POST /api/members/resend-invite
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get parameters from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    const email = searchParams.get("email");
    const status = searchParams.get("status");
    
    if (!workspaceId || !email) {
      return NextResponse.json({ 
        error: "Workspace ID and email are required" 
      }, { status: 400 });
    }
    
    // Check if user has permission to resend invitations in this workspace
    const permission = await checkWorkspaceAdminPermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Check if member exists
    const { data: existingMember, error: existingError } = await supabase
      .from("workspace_members")
      .select("status")
      .eq("workspace_id", workspaceId)
      .eq("email", email)
      .single();
    
    if (existingError || !existingMember) {
      return NextResponse.json({
        error: "Member not found in this workspace"
      }, { status: 404 });
    }
    
    // Send invitation email
    const emailSent = await sendInvitationEmail(
      email, 
      workspaceId, 
      status || existingMember.status
    );
    
    if (!emailSent) {
      return NextResponse.json({ 
        error: "Failed to send invitation email" 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      message: "Invitation email sent successfully" 
    }, { status: 200 });
  } catch (error) {
    console.error("Error resending invitation:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
