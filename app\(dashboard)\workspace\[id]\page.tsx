"use client";

import React, { useState } from "react";
import { useParams } from "next/navigation";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";
import {
  useGetWorkspaceMembersQuery,
  useGetWorkspacesByIdQuery,
} from "@/lib/store/services/workspace";
import { useGetStatusQuery } from "@/lib/store/services/status";
import { useGetTagsQuery } from "@/lib/store/services/tags";

// Types and constants
import { WORKSPACE_CONSTANTS, TAB_CONFIG } from "./constants/workspace";

// Custom hooks
import { useWorkspaceSettings } from "./hooks/useWorkspaceSettings";
import { useStatusManagement } from "./hooks/useStatusManagement";
import { useTagsManagement } from "./hooks/useTagsManagement";
import { useMemberManagement } from "./hooks/useMemberManagement";

// Components
import { WorkspaceSkeleton } from "./components/WorkspaceSkeleton";
import { TabNavigation } from "./components/TabNavigation";
import { GeneralSettings } from "./components/GeneralSettings";
import MemberManagement from "../inviteMember";

export default function WorkspaceSettingsPage() {
  const isCollapsed = useSelector(
    (state: RootState) => state.sidebar.isCollapsed
  );

  const searchParams = useParams();
  const { id: workspaceId }: any = searchParams;

  // Tab management
  const [activeTab, setActiveTab] = useState(() => {
    return localStorage.getItem("activeTab") || "general";
  });

  const handleTabClick = (id: string) => {
    setActiveTab(id);
    localStorage.setItem("activeTab", id);
  };

  // Get workspace data
  const { data: workspaceData, isLoading: isLoadingWorkspace } = useGetWorkspacesByIdQuery(workspaceId);
  const { data: workspaceMembers, isLoading: isLoadingMembers } = useGetWorkspaceMembersQuery(workspaceId);
  const { data: statusData, isLoading: isLoadingStatus } = useGetStatusQuery(workspaceId);
  const { data: tagsData, isLoading: isLoadingTags } = useGetTagsQuery(workspaceId);

  // Custom hooks for workspace management
  const {
    settings,
    setSettings,
    isEditMode,
    isSaving,
    handleEditClick,
    handleCancelEdit,
    handleSave,
  } = useWorkspaceSettings({
    workspaceId,
    workspaceData,
  });

  const {
    statuses,
    newStatus,
    setNewStatus,
    isAddingStatus,
    setIsAddingStatus,
    statusToEdit,
    setStatusToEdit,
    statusToDelete,
    setStatusToDelete,
    isAddingStat,
    handleAddStatus,
    handleEditStatus,
    handleUpdateStatus,
    handleDeleteStatus,
    confirmDeleteStatus,
    cancelDeleteStatus,
  } = useStatusManagement({
    workspaceId,
    statusData,
  });

  const {
    tags,
    newTags,
    setNewTags,
    isAddingTags,
    setIsAddingTags,
    tagsToEdit,
    setTagsToEdit,
    tagsToDelete,
    setTagsToDelete,
    isAddingTag,
    handleAddTags,
    handleEditTags,
    handleUpdateTags,
    handleDeleteTags,
    confirmDeleteTags,
    cancelDeleteTags,
  } = useTagsManagement({
    workspaceId,
    tagsData,
  });

  const {
    members,
    isAdding,
    isDeleting,
    isResending,
    handleMemberAdd,
    handleMemberDelete,
    handleMemberUpdate,
    resendInviteToMember,
  } = useMemberManagement({
    workspaceId,
    workspaceMembers,
  });

  // Loading state
  if (isLoadingWorkspace || isLoadingMembers || isLoadingStatus || isLoadingTags) {
    return <WorkspaceSkeleton isCollapsed={isCollapsed} />;
  }

  return (
    <div
      className={`transition-all duration-500 ease-in-out px-1 py-2 md:px-4 md:py-6 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden`}
    >
      <div className="container mx-auto p-2 md:p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col space-y-4">
          <h1 className="text-2xl md:text-3xl font-bold">
            {WORKSPACE_CONSTANTS.UI_TEXT.PAGE_TITLE}
          </h1>

          {/* Tab Navigation */}
          <TabNavigation activeTab={activeTab} onTabClick={handleTabClick} />
        </div>

        {/* Content */}
        <div className="grid gap-6">
          {/* General Settings */}
          {activeTab === "general" && (
            <GeneralSettings
              settings={settings}
              setSettings={setSettings}
              isEditMode={isEditMode}
              isSaving={isSaving}
              onEditClick={handleEditClick}
              onCancelEdit={handleCancelEdit}
              onSave={handleSave}
            />
          )}

          {/* Members Tab */}
          {activeTab === "members" && (
            <MemberManagement
              members={members}
              isAdding={isAdding}
              isDeleting={isDeleting}
              isResending={isResending}
              isLoading={isLoadingMembers}
              onMemberAdd={handleMemberAdd}
              onMemberDelete={handleMemberDelete}
              onMemberUpdate={handleMemberUpdate}
              onInviteResend={resendInviteToMember}
            />
          )}

          {/* Status Management */}
          {activeTab === "status" && (
            <div>
              {/* Status management content will be added here */}
              <p>Status Management - Coming Soon</p>
            </div>
          )}

          {/* Tags Management */}
          {activeTab === "tags" && (
            <div>
              {/* Tags management content will be added here */}
              <p>Tags Management - Coming Soon</p>
            </div>
          )}

          {/* Notifications */}
          {activeTab === "notifications" && (
            <div>
              {/* Notifications content will be added here */}
              <p>Notifications - Coming Soon</p>
            </div>
          )}

          {/* Security */}
          {activeTab === "security" && (
            <div>
              {/* Security content will be added here */}
              <p>Security Settings - Coming Soon</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
