"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { CurrentSubscriptionProps, SUBSCRIPTION_PLANS } from '../types/subscription';
import { getSubscriptionStatusText } from '../utils/subscription';
import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

export function CurrentSubscription({ 
  subscription, 
  daysRemaining, 
  isDataLoading, 
  onManage 
}: CurrentSubscriptionProps) {
  const router = useRouter();

  if (isDataLoading) {
    return (
      <div className="mb-8 w-full max-w-4xl mx-auto">
        <div className="animate-pulse">
          <div className="h-28 bg-muted/50 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!subscription) return null;

  const statusText = getSubscriptionStatusText(subscription, daysRemaining);
  const isExpired = daysRemaining === 0;

  return (
    <Card className="mb-10 border border-primary/20 bg-gradient-to-r from-primary/5 to-blue-500/5 w-full max-w-4xl mx-auto shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
      {/* Decorative background elements */}
      <div className="absolute top-0 right-0 w-32 h-32 -mt-8 -mr-8 bg-primary/10 rounded-full blur-2xl opacity-70"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 -mb-6 -ml-6 bg-blue-500/10 rounded-full blur-2xl opacity-70"></div>
      
      <CardContent className="pt-6 relative z-10 p-4 md:p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-5">
          <div className="space-y-2 flex-1">
            <div className="flex flex-wrap items-center gap-2.5">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 6H4V8H20V6Z" fill="currentColor" />
                  <path d="M20 10H4V12H20V10Z" fill="currentColor" />
                  <path d="M12 14H4V16H12V14Z" fill="currentColor" />
                  <path fillRule="evenodd" clipRule="evenodd" d="M2 4C2 2.89543 2.89543 2 4 2H20C21.1046 2 22 2.89543 22 4V20C22 21.1046 21.1046 22 20 22H4C2.89543 22 2 21.1046 2 20V4ZM4 4H20V20H4V4Z" fill="currentColor" />
                </svg>
              </div>
              <h3 className="text-lg sm:text-xl font-semibold">
                Current Plan: 
                <Badge 
                  variant="outline" 
                  className="ml-1.5 font-semibold text-primary border-primary/30 bg-primary/5 text-sm"
                >
                  {SUBSCRIPTION_PLANS[subscription.planId].name}
                </Badge>
              </h3>
            </div>
            
            <p className="text-sm sm:text-base text-muted-foreground" suppressHydrationWarning>
              {daysRemaining !== null ? (
                daysRemaining > 0 ? (
                  <div className="flex items-center gap-1.5">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V12L15 15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                    </svg>
                    Your subscription renews in <span className="font-medium text-primary">{daysRemaining} days</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1.5 text-destructive">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 8V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      <path d="M12 16H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                    </svg>
                    Your subscription has <span className="font-medium">expired</span>
                  </div>
                )
              ) : (
                <div className="flex items-center gap-1.5">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                  </svg>
                  Subscription status: <span className="font-medium capitalize">{subscription.status}</span>
                </div>
              )}
            </p>
          </div>
          
          <div className="flex flex-wrap items-center gap-3 w-full md:w-auto">
            {subscription.paymentMethod && (
              <Badge variant="secondary" className="capitalize px-3 py-1.5 text-sm">
                <svg className="w-4 h-4 mr-1.5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M21 5H3C1.89543 5 1 5.89543 1 7V17C1 18.1046 1.89543 19 3 19H21C22.1046 19 23 18.1046 23 17V7C23 5.89543 22.1046 5 21 5Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M1 10H23" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                {subscription.paymentMethod}
              </Badge>
            )}
            
            {subscription.planId !== 'starter' && (
              <Button
                variant="outline"
                size="sm"
                onClick={onManage}
                className="ml-auto md:ml-0 group relative overflow-hidden border-primary/30 hover:border-primary transition-colors"
              >
                <span className="absolute inset-0 bg-primary/10 transform group-hover:translate-y-0 translate-y-full transition-transform duration-300"></span>
                <span className="relative flex items-center gap-1.5">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4V20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M4 12H20" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                  {SUBSCRIPTION_CONSTANTS.BUTTON_TEXTS.MANAGE_SUBSCRIPTION}
                </span>
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
