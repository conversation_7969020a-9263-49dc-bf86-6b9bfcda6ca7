import React from "react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface LeadsSkeletonProps {
  isCollapsed: boolean;
  showFilters?: boolean;
}

export const LeadsSkeleton: React.FC<LeadsSkeletonProps> = ({ isCollapsed, showFilters = false }) => {
  return (
    <div className={`p-2 sm:p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} w-full max-w-full overflow-x-hidden`}>
      <Card className="w-full rounded-[8px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800 shadow-sm">
        <CardHeader className="p-3 sm:p-4 md:p-6 pb-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Header Left Side */}
            <div className="flex items-center gap-3">
              <div className="bg-black/10 dark:bg-white/10 p-2 rounded-full">
                <Skeleton className="h-6 w-6" />
              </div>
              <div>
                <Skeleton className="h-7 w-32 md:w-48 mb-2" />
                <Skeleton className="h-4 w-48 md:w-64" />
              </div>
            </div>

            {/* Header Right Side */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-9 w-full sm:w-64" />
                <Skeleton className="h-9 w-20 sm:w-28" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-9 w-12 sm:w-16" />
                <Skeleton className="h-9 w-16 sm:w-20" />
                <Skeleton className="h-9 w-20 sm:w-24" />
                <Skeleton className="h-9 w-24 sm:w-28" />
              </div>
            </div>
          </div>

          {/* Filters Skeleton */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-800">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          )}
        </CardHeader>

      <CardContent className="p-3 sm:p-4 md:p-6">
        {/* Bulk Actions Skeleton */}
        <div className="mb-4">
          <Skeleton className="h-8 w-48" />
        </div>

        {/* Desktop Table Skeleton */}
        <div className="hidden md:block space-y-3">
          {/* Table Header */}
          <div className="grid grid-cols-6 gap-4 py-3 px-4 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800">
            <Skeleton className="h-4 w-4" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-4 w-16" />
          </div>

          {/* Table Rows */}
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="grid grid-cols-6 gap-4 py-3 px-4 border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              <Skeleton className="h-4 w-4" />
              <div className="space-y-1">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-40" />
              </div>
              <div className="space-y-1">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-3 w-24" />
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
              <Skeleton className="h-6 w-20 rounded-full" />
              <div className="flex gap-1">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </div>
          ))}
        </div>

        {/* Mobile Cards Skeleton */}
        <div className="md:hidden space-y-3">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-4 w-4" />
                  <div>
                    <Skeleton className="h-5 w-28 mb-1" />
                    <Skeleton className="h-4 w-36" />
                  </div>
                </div>
                <Skeleton className="h-6 w-16 rounded-full" />
              </div>

              <div className="space-y-2 mb-3">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-24" />
              </div>

              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-20 rounded-full" />
                <div className="flex gap-1">
                  <Skeleton className="h-8 w-8 rounded" />
                  <Skeleton className="h-8 w-8 rounded" />
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination Skeleton */}
        <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-4">
          <Skeleton className="h-5 w-32" />
          <div className="flex items-center space-x-2">
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-16" />
            <Skeleton className="h-9 w-16" />
          </div>
        </div>
      </CardContent>
      </Card>
    </div>
  );
};

// Mobile Skeleton Component
export const LeadsMobileSkeleton: React.FC = () => {
  return (
    <div className="space-y-4">
      {/* Header Skeleton */}
      <Card className="p-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        <div className="flex items-center gap-3 mb-4">
          <Skeleton className="h-8 w-8 rounded-full" />
          <Skeleton className="h-6 w-32" />
        </div>
        <div className="space-y-3">
          <Skeleton className="h-10 w-full" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-20" />
            <Skeleton className="h-9 w-24" />
          </div>
        </div>
      </Card>

      {/* Mobile Cards Skeleton */}
      {Array.from({ length: 6 }).map((_, index) => (
        <Card key={index} className="p-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-3">
              <Skeleton className="h-4 w-4" />
              <div>
                <Skeleton className="h-5 w-28 mb-1" />
                <Skeleton className="h-4 w-36" />
              </div>
            </div>
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>

          <div className="space-y-2 mb-3">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>

          <div className="flex justify-between items-center">
            <Skeleton className="h-6 w-20 rounded-full" />
            <div className="flex gap-1">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};

// Loading More Skeleton (for infinite scroll)
export const LoadingMoreSkeleton: React.FC = () => {
  return (
    <div className="space-y-3 mt-4">
      {/* Desktop Loading More */}
      <div className="hidden md:block">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="grid grid-cols-6 gap-4 py-3 px-4 border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
            <Skeleton className="h-4 w-4" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-3 w-40" />
            </div>
            <div className="space-y-1">
              <Skeleton className="h-4 w-28" />
              <Skeleton className="h-3 w-24" />
            </div>
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
            <div className="flex gap-1">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>
        ))}
      </div>

      {/* Mobile Loading More */}
      <div className="md:hidden">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 mb-3">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <Skeleton className="h-4 w-4" />
                <div>
                  <Skeleton className="h-5 w-28 mb-1" />
                  <Skeleton className="h-4 w-36" />
                </div>
              </div>
              <Skeleton className="h-6 w-16 rounded-full" />
            </div>

            <div className="space-y-2 mb-3">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-24" />
            </div>

            <div className="flex justify-between items-center">
              <Skeleton className="h-6 w-20 rounded-full" />
              <div className="flex gap-1">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
