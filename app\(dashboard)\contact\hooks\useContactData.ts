import { useState, useEffect, useCallback } from 'react';
import { Contact, ContactFilters } from '../types/contact';
import { CONTACT_CONSTANTS } from '../constants/contact';
import {
  transformLeadToContact,
  markDuplicateContacts,
  sortContactsByDate,
  filterQualifiedContacts,
  filterContacts,
  paginateContacts,
} from '../utils/contactUtils';
import { useGetLeadsByWorkspaceQuery } from '@/lib/store/services/leadsApi';
import { useGetActiveWorkspaceQuery } from '@/lib/store/services/workspace';
import { useGetStatusQuery } from '@/lib/store/services/status';

export function useContactData() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filters, setFilters] = useState<ContactFilters>({
    search: '',
    statusFilter: CONTACT_CONSTANTS.STATUS_OPTIONS.ALL,
    currentPage: 1,
    itemsPerPage: CONTACT_CONSTANTS.PAGINATION.DEFAULT_ITEMS_PER_PAGE,
  });

  // API Queries
  const { data: activeWorkspace, isLoading: isLoadingWorkspace } = useGetActiveWorkspaceQuery();
  const workspaceId = activeWorkspace?.data?.id;

  const { data: workspaceData, isLoading: isLoadingLeads } = useGetLeadsByWorkspaceQuery(
    workspaceId
      ? { workspaceId: workspaceId.toString() }
      : {},
    {
      skip: !workspaceId || isLoadingWorkspace,
      pollingInterval: CONTACT_CONSTANTS.POLLING.INTERVAL,
    }
  );

  const { data: statusData, isLoading: isLoadingStatus } = useGetStatusQuery(workspaceId);

  // Contact statuses for filtering qualified contacts
  const contactStatuses = new Set(
    Array.isArray(statusData?.data)
      ? statusData.data
          .filter((status: any) => status.count_statistics)
          .map((status: any) => status.name)
      : []
  );

  // Process leads data into contacts
  const processLeadsData = useCallback(() => {
    if (!isLoadingLeads && workspaceData?.data) {
      // Transform leads to contacts
      let fetchedContacts = workspaceData.data.map(transformLeadToContact);

      // Mark duplicates
      const contactsWithDuplicates = markDuplicateContacts(fetchedContacts);

      // Sort by most recent
      const sortedContacts = sortContactsByDate(contactsWithDuplicates);

      // Filter for qualified contacts only
      const qualifiedContacts = filterQualifiedContacts(sortedContacts, contactStatuses);

      setContacts(qualifiedContacts);
    }
  }, [workspaceData, isLoadingLeads, contactStatuses]);

  // Effect to process data when it changes
  useEffect(() => {
    processLeadsData();
  }, [processLeadsData]);

  // Filter and paginate contacts
  const filteredContacts = filterContacts(contacts, filters);
  const { paginatedContacts, totalPages, startIndex } = paginateContacts(
    filteredContacts,
    filters.currentPage,
    filters.itemsPerPage
  );

  // Filter update functions
  const updateSearch = useCallback((search: string) => {
    setFilters(prev => ({ ...prev, search, currentPage: 1 }));
  }, []);

  const updateStatusFilter = useCallback((statusFilter: string) => {
    setFilters(prev => ({ ...prev, statusFilter, currentPage: 1 }));
  }, []);

  const updateCurrentPage = useCallback((currentPage: number) => {
    setFilters(prev => ({ ...prev, currentPage }));
  }, []);

  const updateItemsPerPage = useCallback((itemsPerPage: number) => {
    setFilters(prev => ({ ...prev, itemsPerPage, currentPage: 1 }));
  }, []);

  // Contact operations
  const addContact = useCallback((newContact: Contact) => {
    setContacts(prev => [newContact, ...prev]);
  }, []);

  const updateContact = useCallback((contactId: number, updates: Partial<Contact>) => {
    setContacts(prev =>
      prev.map(contact =>
        contact.id === contactId ? { ...contact, ...updates } : contact
      )
    );
  }, []);

  const removeContact = useCallback((contactId: number) => {
    setContacts(prev => prev.filter(contact => contact.id !== contactId));
  }, []);

  // Get contact by ID
  const getContactById = useCallback((contactId: number) => {
    return contacts.find(contact => contact.id === contactId);
  }, [contacts]);

  // Refresh data
  const refreshContacts = useCallback(() => {
    processLeadsData();
  }, [processLeadsData]);

  return {
    // Data
    contacts,
    filteredContacts,
    paginatedContacts,
    contactStatuses,
    workspaceId,

    // Pagination
    currentPage: filters.currentPage,
    itemsPerPage: filters.itemsPerPage,
    totalPages,
    startIndex,
    totalContacts: filteredContacts.length,

    // Filters
    search: filters.search,
    statusFilter: filters.statusFilter,

    // Loading states
    isLoading: isLoadingWorkspace || isLoadingLeads || isLoadingStatus,
    isLoadingWorkspace,
    isLoadingLeads,
    isLoadingStatus,

    // Filter actions
    updateSearch,
    updateStatusFilter,
    updateCurrentPage,
    updateItemsPerPage,

    // Contact actions
    addContact,
    updateContact,
    removeContact,
    getContactById,
    refreshContacts,

    // Utilities
    processLeadsData,
  };
}
