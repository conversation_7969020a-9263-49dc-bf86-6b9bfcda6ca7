import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { LeadSource } from "../types/leadSources";
import { LeadSourceFormData } from "../schemas/leadSourceSchema";
import { SOURCE_TYPES, DIALOG_TITLES, BUTTON_LABELS } from "../constants/leadSources";

interface LeadSourceFormDialogProps {
  isOpen: boolean;
  mode: "create" | "edit";
  form: UseFormReturn<LeadSourceFormData>;
  isLoading: boolean;
  onClose: () => void;
  onSubmit: (data: LeadSourceFormData) => void;
}

export const LeadSourceFormDialog: React.FC<LeadSourceFormDialogProps> = ({
  isOpen,
  mode,
  form,
  isLoading,
  onClose,
  onSubmit,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] max-w-md bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-gray-900 dark:text-white">
            {mode === "create" ? DIALOG_TITLES.CREATE : DIALOG_TITLES.EDIT}
          </DialogTitle>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Source Name */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 dark:text-gray-300">
                    Source Name
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter source name"
                      className="border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Source Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 dark:text-gray-300">
                    Source Type
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
                        <SelectValue placeholder="Select a source type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700">
                      {SOURCE_TYPES.map((type) => (
                        <SelectItem
                          key={type.id}
                          value={type.id}
                          className="hover:bg-gray-100 dark:hover:bg-gray-800"
                        >
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 dark:text-gray-300">
                    Description (Optional)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter source description"
                      className="resize-none border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full sm:w-auto border-gray-200 dark:border-gray-700"
                  disabled={isLoading}
                >
                  {BUTTON_LABELS.CANCEL}
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="w-full sm:w-auto bg-black hover:bg-gray-800 dark:bg-white dark:hover:bg-gray-200 text-white dark:text-black"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    {mode === "create" ? BUTTON_LABELS.ADDING : BUTTON_LABELS.UPDATING}
                  </>
                ) : (
                  mode === "create" ? BUTTON_LABELS.ADD_SOURCE : BUTTON_LABELS.UPDATE_SOURCE
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

interface DeleteConfirmationDialogProps {
  isOpen: boolean;
  source: LeadSource | null;
  isLoading: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const DeleteConfirmationDialog: React.FC<DeleteConfirmationDialogProps> = ({
  isOpen,
  source,
  isLoading,
  onClose,
  onConfirm,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] max-w-md bg-white dark:bg-black border border-gray-200 dark:border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-gray-900 dark:text-white">
            {DIALOG_TITLES.DELETE}
          </DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-gray-700 dark:text-gray-300">
            Are you sure you want to delete the lead source{" "}
            <span className="font-semibold text-gray-900 dark:text-white">
              "{source?.name}"
            </span>
            ? This action cannot be undone.
          </p>
        </div>

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2">
          <DialogClose asChild>
            <Button
              type="button"
              variant="outline"
              className="w-full sm:w-auto border-gray-200 dark:border-gray-700"
              disabled={isLoading}
            >
              {BUTTON_LABELS.CANCEL}
            </Button>
          </DialogClose>
          <Button
            variant="destructive"
            onClick={onConfirm}
            className="w-full sm:w-auto"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                {BUTTON_LABELS.DELETING}
              </>
            ) : (
              BUTTON_LABELS.DELETE_SOURCE
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
