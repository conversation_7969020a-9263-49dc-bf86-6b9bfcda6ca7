import { useState } from 'react';
import { calculatePrice } from '../utils/pricing';

export function useBillingCycle() {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const toggleBillingCycle = () => {
    setBillingCycle(prev => prev === 'monthly' ? 'yearly' : 'monthly');
  };

  const setBillingCycleValue = (cycle: 'monthly' | 'yearly') => {
    setBillingCycle(cycle);
  };

  // Get price with current billing cycle
  const getPrice = (basePrice: number) => {
    return calculatePrice(basePrice, billingCycle);
  };

  return {
    billingCycle,
    toggleBillingCycle,
    setBillingCycle: setBillingCycleValue,
    getPrice,
  };
}
