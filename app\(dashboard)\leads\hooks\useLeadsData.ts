import { useState, useEffect, useRef, useCallback } from "react";
import { useDispatch } from "react-redux";
import { skipToken } from "@reduxjs/toolkit/query";
import {
  useGetLeadsByWorkspaceQuery,
  invalidateLeadsCacheOnWorkspaceChange,
  leadsApiExtended
} from "@/lib/store/services/leadsApi";
import { useGetActiveWorkspaceQuery } from "@/lib/store/services/workspace";
import { Lead, SortField, SortDirection } from "../types/leads";
import { LEADS_PER_PAGE } from "../constants/leads";

export const useLeadsData = () => {
  const dispatch = useDispatch();

  // State declarations
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [sortField, setSortField] = useState<SortField>("created_at");
  const [sortDirection, setSortDirection] = useState<SortDirection>("desc");
  const [leads, setLeads] = useState<Lead[]>([]);
  const [totalLeads, setTotalLeads] = useState(0);

  // Get active workspace
  const { data: activeWorkspace, isLoading: isLoadingWorkspace } =
    useGetActiveWorkspaceQuery();
  const workspaceId = activeWorkspace?.data?.id;

  // Fetch leads with proper pagination
  const {
    data: workspaceData,
    isLoading: isLoadingLeads,
    isFetching: isFetchingLeads,
    refetch: refetchLeads
  } = useGetLeadsByWorkspaceQuery(
    workspaceId
      ? {
        workspaceId: workspaceId.toString(),
        limit: LEADS_PER_PAGE,
        offset: currentPage * LEADS_PER_PAGE,
        sortBy: sortField,
        sortOrder: sortDirection,
      }
      : skipToken,
    {
      skip: !workspaceId || isLoadingWorkspace,
      refetchOnMountOrArgChange: true
    }
  );

  // Handle workspace changes by invalidating cache and refetching data
  const prevWorkspaceIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (workspaceId) {
      // Check if workspace has changed or it's the initial load
      if (prevWorkspaceIdRef.current !== workspaceId) {
        console.log('Workspace changed from', prevWorkspaceIdRef.current, 'to', workspaceId);

        // Reset pagination and sorting to defaults
        setCurrentPage(0);
        setLeads([]);
        setSortField('created_at');
        setSortDirection('desc');
        setHasMore(true);
        setTotalLeads(0);

        // Force refetch by dispatching invalidation action
        if (prevWorkspaceIdRef.current) {
          // Only do this if we're changing workspaces, not on initial load
          dispatch(invalidateLeadsCacheOnWorkspaceChange(workspaceId) as any);
        }

        // Force refetch of leads data
        refetchLeads();
      }

      // Update ref with current workspace ID
      prevWorkspaceIdRef.current = workspaceId;
    }
  }, [workspaceId, dispatch, refetchLeads]);

  // Update leads state when workspace data changes
  useEffect(() => {
    if (workspaceData) {
      console.log('Workspace data updated:', workspaceData);

      // Only replace leads array on first page or workspace change
      if (currentPage === 0) {
        setLeads(workspaceData.data as Lead[] || []);
      } else {
        // For subsequent pages, append new leads without duplicates
        const newLeads = [...leads];

        // Add only unique leads
        workspaceData.data?.forEach((lead: any) => {
          if (!newLeads.some(existingLead => existingLead.id === lead.id)) {
            newLeads.push(lead);
          }
        });

        setLeads(newLeads);
      }

      // Update total count and hasMore flag
      setTotalLeads(workspaceData.pagination?.total || 0);
      setHasMore((currentPage + 1) * LEADS_PER_PAGE < (workspaceData.pagination?.total || 0));
    }
  }, [workspaceData, currentPage]);

  // Process leads data when it changes
  useEffect(() => {
    if (!isLoadingLeads && workspaceData?.data) {
      console.log('Processing leads data, page:', currentPage, 'total leads:', workspaceData.pagination?.total);

      let fetchedLeads = workspaceData?.data.map(
        (lead: any, index: number) => ({
          id: lead.id || index + 1,
          Name: lead.name || "",
          email: lead.email || "",
          phone: lead.phone || "",
          company: lead.company || "",
          position: lead.position || "",
          contact_method: lead.contact_method,
          owner: lead.owner || "Unknown",
          // New ID-based fields
          status_id: lead.status_id,
          assigned_to_id: lead.assigned_to_id,
          // Legacy fields for backward compatibility
          status: lead.status || "New",
          assign_to: lead.assign_to || "Not Assigned",
          revenue: lead.revenue || 0,
          createdAt: lead.created_at
            ? new Date(lead.created_at).toISOString()
            : new Date().toISOString(),
          isDuplicate: false,
          is_email_valid: lead.is_email_valid,
          is_phone_valid: lead.is_phone_valid,
          sourceId: lead.lead_source_id || null,
        })
      );

      const duplicates = new Set();
      fetchedLeads.forEach((lead: Lead) => {
        const duplicate = fetchedLeads.find(
          (l: Lead) =>
            l.id !== lead.id &&
            (l.email === lead.email || l.phone === lead.phone)
        );
        if (duplicate) {
          duplicates.add(lead.id);
          duplicates.add(duplicate.id);
        }
      });

      // Mark duplicates
      const processedLeads = fetchedLeads.map((lead: Lead) => ({
        ...lead,
        isDuplicate: duplicates.has(lead.id),
      }));

      // Update leads based on current page
      if (currentPage === 0) {
        // If it's the first page, replace the leads array
        console.log('Setting initial leads:', processedLeads.length);
        setLeads(processedLeads);
      } else {
        // Otherwise, append to the existing leads without duplicates
        const existingIds = new Set(leads.map(lead => lead.id));
        const uniqueNewLeads = processedLeads.filter(lead => !existingIds.has(lead.id));

        console.log('Appending new leads:', uniqueNewLeads.length);
        if (uniqueNewLeads.length > 0) {
          setLeads(prev => [...prev, ...uniqueNewLeads]);
        }
      }

      // Update pagination info
      if (workspaceData.pagination) {
        setTotalLeads(workspaceData.pagination.total);
        setHasMore(workspaceData.pagination.hasMore);
      }
    }
  }, [workspaceData, isLoadingLeads, currentPage]);

  // Function to load more leads
  const loadMoreLeads = useCallback(() => {
    if (hasMore && !isLoadingLeads && !isFetchingLeads) {
      console.log('Loading more leads, current page:', currentPage);
      setCurrentPage(prev => prev + 1);
    }
  }, [hasMore, isLoadingLeads, isFetchingLeads, currentPage]);

  // Handle sorting changes
  const handleSortChange = (field: SortField) => {
    if (field === sortField) {
      // Toggle sort direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to descending
      setSortField(field);
      setSortDirection('desc');
    }
    // Reset to first page when sorting changes
    setCurrentPage(0);
    setLeads([]);
  };

  const handleNextPage = () => {
    if (!isLoadingLeads && !isFetchingLeads && hasMore) {
      console.log('Moving to next page');
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (!isLoadingLeads && !isFetchingLeads && currentPage > 0) {
      console.log('Moving to previous page');
      setCurrentPage(prev => Math.max(0, prev - 1));

      // When going back, we need to ensure we have the correct leads
      if (currentPage > 0) {
        // Force a refetch for the previous page
        dispatch(leadsApiExtended.util.invalidateTags([
          { type: 'LeadByWorkspace', id: workspaceId }
        ]));
      }
    }
  };

  return {
    // Data
    leads,
    totalLeads,
    workspaceId,

    // Loading states
    isLoadingLeads,
    isFetchingLeads,
    isLoadingWorkspace,

    // Pagination
    currentPage,
    hasMore,

    // Sorting
    sortField,
    sortDirection,

    // Actions
    loadMoreLeads,
    handleSortChange,
    handleNextPage,
    handlePreviousPage,
    refetchLeads,
    setLeads,
  };
};
