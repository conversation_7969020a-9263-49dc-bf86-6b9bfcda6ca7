"use client";

import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/lib/store/store';
import { ProfileSkeleton, ProfileMobileSkeleton } from './ProfileSkeleton';

export function ProfileLoading() {
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);

  return (
    <>
      {/* Desktop/Tablet Skeleton */}
      <div className="hidden sm:block">
        <ProfileSkeleton isCollapsed={isCollapsed} />
      </div>

      {/* Mobile Skeleton */}
      <div className="block sm:hidden">
        <ProfileMobileSkeleton isCollapsed={isCollapsed} />
      </div>
    </>
  );
}
