import { closeRealtimeSubscriptions, cleanupInactiveSubscriptions } from './supabaseServer';
import { runStorageCleanup } from './storageCleanup';

// Schedule for cleanup operations
const CLEANUP_INTERVALS = {
  SUPABASE_SUBSCRIPTIONS: 30 * 60 * 1000, // 30 minutes
  STORAGE: 24 * 60 * 60 * 1000, // 24 hours
};

// Track last cleanup times
const lastCleanupTimes = {
  supabaseSubscriptions: 0,
  storage: 0,
};

// Cleanup Supabase subscriptions
export const cleanupSupabaseSubscriptions = async (): Promise<boolean> => {
  try {
    console.log('Running Supabase subscriptions cleanup...');

    // Cleanup inactive subscriptions older than 15 minutes
    const count = cleanupInactiveSubscriptions(15 * 60 * 1000);
    console.log(`Cleaned up ${count} inactive Supabase subscriptions`);

    lastCleanupTimes.supabaseSubscriptions = Date.now();
    return true;
  } catch (error) {
    console.error('Error during Supabase subscriptions cleanup:', error);
    return false;
  }
};

// Run all cleanup operations
export const runAllCleanupOperations = async (): Promise<boolean> => {
  try {
    console.log('Running all cleanup operations...');

    const now = Date.now();

    // Check if Supabase subscriptions cleanup is due
    if (now - lastCleanupTimes.supabaseSubscriptions >= CLEANUP_INTERVALS.SUPABASE_SUBSCRIPTIONS) {
      await cleanupSupabaseSubscriptions();
    }

    // Check if storage cleanup is due
    if (now - lastCleanupTimes.storage >= CLEANUP_INTERVALS.STORAGE) {
      await runStorageCleanup();
      lastCleanupTimes.storage = now;
    }

    return true;
  } catch (error) {
    console.error('Error during cleanup operations:', error);
    return false;
  }
};

// Initialize cleanup schedule
export const initCleanupSchedule = (): NodeJS.Timeout => {
  console.log('Initializing cleanup schedule...');

  // Run cleanup operations every 15 minutes
  return setInterval(() => {
    runAllCleanupOperations().catch(error => {
      console.error('Error in scheduled cleanup:', error);
    });
  }, 15 * 60 * 1000); // 15 minutes
};

// Cleanup before server shutdown
export const cleanupBeforeShutdown = async (): Promise<void> => {
  console.log('Running cleanup before shutdown...');

  try {
    // Close Supabase realtime subscriptions
    closeRealtimeSubscriptions();

    console.log('Cleanup before shutdown completed');
  } catch (error) {
    console.error('Error during cleanup before shutdown:', error);
  }
};
