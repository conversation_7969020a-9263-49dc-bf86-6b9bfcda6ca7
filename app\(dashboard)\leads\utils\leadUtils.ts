import { Lead, LeadSource } from "../types/leads";

/**
 * Process raw lead data and mark duplicates
 */
export const processLeadsData = (rawLeads: any[]): Lead[] => {
  const leads = rawLeads.map((lead: any, index: number) => ({
    id: lead.id || index + 1,
    Name: lead.name || "",
    email: lead.email || "",
    phone: lead.phone || "",
    company: lead.company || "",
    position: lead.position || "",
    contact_method: lead.contact_method,
    owner: lead.owner || "Unknown",
    status: lead.status || "New",
    revenue: lead.revenue || 0,
    assign_to: lead.assign_to || "Not Assigned",
    createdAt: lead.created_at
      ? new Date(lead.created_at).toISOString()
      : new Date().toISOString(),
    isDuplicate: false,
    is_email_valid: lead.is_email_valid,
    is_phone_valid: lead.is_phone_valid,
    sourceId: lead.lead_source_id || null,
  }));

  // Find duplicates
  const duplicates = new Set<number>();
  leads.forEach((lead: Lead) => {
    const duplicate = leads.find(
      (l: Lead) =>
        l.id !== lead.id &&
        (l.email === lead.email || l.phone === lead.phone)
    );
    if (duplicate) {
      duplicates.add(lead.id);
      duplicates.add(duplicate.id);
    }
  });

  // Mark duplicates
  return leads.map((lead: Lead) => ({
    ...lead,
    isDuplicate: duplicates.has(lead.id),
  }));
};

/**
 * Format lead data for CSV export
 */
export const formatLeadsForExport = (leads: Lead[], leadSources?: { data: LeadSource[] }) => {
  return leads.map((lead) => {
    // Find the matching lead source based on sourceId
    const matchedSource = leadSources?.data.find((source: LeadSource) =>
      source.webhook_url.includes(lead.sourceId || "")
    );

    const formattedSourceDate = matchedSource
      ? new Date(matchedSource.created_at)
        .toLocaleString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(",", "")
      : "";

    return {
      Name: lead.Name,
      email: lead.email.toLowerCase(),
      phone: lead.phone,
      company: lead.company,
      position: lead.position,
      contact_method: lead.contact_method,
      owner: lead.owner,
      status: lead.status ? String(lead?.status?.name) : "Unknown",
      revenue: lead.revenue,
      assign_to: lead.assign_to,
      createdAt: new Date(lead.createdAt)
        .toLocaleString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        })
        .replace(",", ""),
      isDuplicate: lead.isDuplicate,
      is_email_valid: lead.is_email_valid,
      is_phone_valid: lead.is_phone_valid,
      source: matchedSource
        ? `${matchedSource.name}-${formattedSourceDate}`
        : "No Source",
    };
  });
};

/**
 * Normalize imported CSV data
 */
export const normalizeImportedData = (rawData: any[]): any[] => {
  return rawData.map((lead: any) => ({
    id: lead.id,
    name: lead.Name?.trim() || "",
    email: lead.email,
    phone: String(lead.phone)
      .replace(/[^\d+]/g, "")
      .replace(/^([^+])/, "+$1")
      .trim(),
    company: lead.company || "",
    position: lead.position || "",
    contact_method: lead.contact_method || "",
    owner: lead.owner || "Unknown",
    status: lead.status || "Pending",
    revenue: Number(lead.revenue) || 0,
    assign_to: lead.assign_to || "",
    createdAt: lead.createdAt
      ? new Date(lead.createdAt).toISOString()
      : new Date().toISOString(),
    isDuplicate: lead.isDuplicate === "TRUE",
    is_email_valid: lead.is_email_valid === "TRUE",
    is_phone_valid: lead.is_phone_valid === "TRUE",
    sourceId: lead.sourceId,
  }));
};

/**
 * Get contact method icon name
 */
export const getContactMethodIcon = (method: string): string => {
  switch (method) {
    case "WhatsApp":
      return "Send";
    case "Call":
      return "Phone";
    case "SMS":
      return "MessageCircle";
    default:
      return "MessageCircle";
  }
};

/**
 * Sanitize phone number for external links
 */
export const sanitizePhoneNumber = (phone: string): string => {
  return phone.replace(/\D/g, "");
};

/**
 * Check if leads array has duplicates
 */
export const hasDuplicates = (leads: Lead[]): boolean => {
  const emails = new Set<string>();
  const phones = new Set<string>();
  
  for (const lead of leads) {
    if (lead.email && emails.has(lead.email)) return true;
    if (lead.phone && phones.has(lead.phone)) return true;
    
    if (lead.email) emails.add(lead.email);
    if (lead.phone) phones.add(lead.phone);
  }
  
  return false;
};

/**
 * Get unique leads by removing duplicates
 */
export const getUniqueLeads = (leads: Lead[]): Lead[] => {
  const seen = new Set<string>();
  return leads.filter(lead => {
    const key = `${lead.email}-${lead.phone}`;
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });
};
