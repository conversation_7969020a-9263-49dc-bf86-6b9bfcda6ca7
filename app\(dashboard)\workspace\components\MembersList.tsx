"use client";

import React from 'react';
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { MemberCard } from './MemberCard';
import { MembersListProps } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';

interface MembersListComponentProps extends MembersListProps {
  onEditRole?: (member: any) => void;
  resendingMembers?: { [key: string]: boolean };
}

export function MembersList({
  members,
  isLoading,
  onMemberUpdate,
  onMemberDelete,
  onInviteResend,
  isDeleting,
  isResending,
  onEditRole,
  resendingMembers,
}: MembersListComponentProps) {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Label className="text-sm font-medium">{MEMBER_CONSTANTS.UI_TEXT.MEMBERS_LABEL}</Label>
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">{MEMBER_CONSTANTS.UI_TEXT.MEMBERS_LABEL}</Label>
      <div className="space-y-2">
        {members.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No members found. Invite someone to get started!</p>
          </div>
        ) : (
          members.map((member) => (
            <MemberCard
              key={member.id || member.email}
              member={member}
              onUpdate={onMemberUpdate}
              onDelete={onMemberDelete}
              onResendInvite={onInviteResend}
              onEditRole={onEditRole}
              isDeleting={isDeleting}
              isResending={isResending}
              resendingMembers={resendingMembers}
            />
          ))
        )}
      </div>
    </div>
  );
}
