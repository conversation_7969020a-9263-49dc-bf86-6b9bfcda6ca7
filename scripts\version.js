#!/usr/bin/env node

/**
 * This script helps with version management for the INFILABS CRM application.
 * It can be used to bump versions and update the CHANGELOG.md file.
 * 
 * Usage:
 *   node scripts/version.js [major|minor|patch] [--no-git]
 * 
 * Examples:
 *   node scripts/version.js patch        # Bump patch version and create git commit/tag
 *   node scripts/version.js minor        # Bump minor version and create git commit/tag
 *   node scripts/version.js major        # Bump major version and create git commit/tag
 *   node scripts/version.js patch --no-git  # Bump patch version without git operations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Parse command line arguments
const args = process.argv.slice(2);
const versionType = args[0] || 'patch';
const noGit = args.includes('--no-git');

if (!['major', 'minor', 'patch'].includes(versionType)) {
  console.error('Error: Version type must be one of: major, minor, patch');
  process.exit(1);
}

// Read package.json
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
const currentVersion = packageJson.version;

// Calculate new version
const [major, minor, patch] = currentVersion.split('.').map(Number);
let newVersion;

switch (versionType) {
  case 'major':
    newVersion = `${major + 1}.0.0`;
    break;
  case 'minor':
    newVersion = `${major}.${minor + 1}.0`;
    break;
  case 'patch':
    newVersion = `${major}.${minor}.${patch + 1}`;
    break;
}

// Update package.json
packageJson.version = newVersion;
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');

// Update health check API if it exists
const healthCheckPath = path.join(__dirname, '..', 'app', 'api', 'health', 'route.ts');
if (fs.existsSync(healthCheckPath)) {
  let healthCheckContent = fs.readFileSync(healthCheckPath, 'utf8');
  healthCheckContent = healthCheckContent.replace(
    /version: process\.env\.NEXT_PUBLIC_APP_VERSION \|\| "[0-9]*\.[0-9]*\.[0-9]*"/,
    `version: process.env.NEXT_PUBLIC_APP_VERSION || "${newVersion}"`
  );
  fs.writeFileSync(healthCheckPath, healthCheckContent);
}

// Update CHANGELOG.md
const changelogPath = path.join(__dirname, '..', 'CHANGELOG.md');
if (fs.existsSync(changelogPath)) {
  const changelogContent = fs.readFileSync(changelogPath, 'utf8');
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  
  const newEntry = `## [${newVersion}] - ${today}

### Added
- 

### Changed
- 

### Fixed
- 

`;
  
  // Insert new entry after the header section
  const updatedChangelog = changelogContent.replace(
    /(# Changelog.*?and this project adheres to \[Semantic Versioning\]\(.*?\)\.)\n\n/s,
    `$1\n\n${newEntry}`
  );
  
  fs.writeFileSync(changelogPath, updatedChangelog);
}

console.log(`Version bumped from ${currentVersion} to ${newVersion}`);

// Git operations
if (!noGit) {
  try {
    // Commit changes
    execSync('git add package.json package-lock.json CHANGELOG.md app/api/health/route.ts', { stdio: 'inherit' });
    execSync(`git commit -m "Bump version to ${newVersion}"`, { stdio: 'inherit' });
    
    // Create tag
    execSync(`git tag -a v${newVersion} -m "Version ${newVersion}"`, { stdio: 'inherit' });
    
    console.log(`Git commit and tag v${newVersion} created. Use 'git push && git push --tags' to push to remote.`);
  } catch (error) {
    console.error('Error performing git operations:', error.message);
  }
}
