import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspaceAdminPermission } from "../utils/auth";

/**
 * Delete a member from a workspace
 * @route DELETE /api/members/[id]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const id = params.id;
    
    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Check if user has permission to remove members from this workspace
    const permission = await checkWorkspaceAdminPermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }
    
    // Get the member to be removed
    const { data: memberToRemove, error: memberError } = await supabase
      .from("workspace_members")
      .select("*")
      .eq("id", id)
      .eq("workspace_id", workspaceId)
      .single();
    
    if (memberError) {
      return NextResponse.json({ error: memberError.message }, { status: 400 });
    }
    
    // Check if the workspace owner is being removed
    const { data: workspace, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();
    
    if (workspaceError) {
      return NextResponse.json({ error: workspaceError.message }, { status: 400 });
    }
    
    // Prevent deletion of workspace owner
    if (workspace.owner_id === memberToRemove.user_id) {
      return NextResponse.json({
        error: "Cannot remove workspace owner"
      }, { status: 403 });
    }
    
    // If user is admin (not SuperAdmin), prevent removing SuperAdmin members
    if (
      permission.role === "admin" && 
      memberToRemove.role === "SuperAdmin"
    ) {
      return NextResponse.json({
        error: "Admins cannot remove SuperAdmin members"
      }, { status: 403 });
    }
    
    // Delete the member
    const { data, error } = await supabase
      .from("workspace_members")
      .delete()
      .eq("id", id)
      .eq("workspace_id", workspaceId);
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ 
      message: "Member removed successfully",
      data 
    }, { status: 200 });
  } catch (error) {
    console.error("Error removing member:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
