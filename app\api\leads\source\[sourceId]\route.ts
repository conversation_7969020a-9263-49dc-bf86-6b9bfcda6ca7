import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser } from "../../utils";

export async function GET(
  request: NextRequest,
  { params }: { params: { sourceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    const sourceId = params.sourceId;
    
    if (!sourceId) {
      return NextResponse.json({ error: "Source ID is required" }, { status: 400 });
    }
    
    // Fetch leads by source ID
    const { data, error } = await supabase
      .from("leads")
      .select("*")
      .eq("source_id", sourceId);
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching leads by source:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
