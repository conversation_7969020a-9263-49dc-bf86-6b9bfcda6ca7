import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { FEATURE_CARDS } from "../constants/documentation";

export const OverviewTab: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Introduction */}
      <div className="prose dark:prose-invert max-w-none">
        <h2 className="text-2xl font-bold mb-4 text-gradient-primary-accent">
          Lead Source Manager Documentation
        </h2>
        <p className="text-base text-gray-700 dark:text-gray-300 mb-6">
          The Lead Source Manager is a comprehensive React component designed for managing lead sources 
          in your CRM application. It provides a complete interface for creating, editing, and managing 
          webhook-based lead sources with real-time status control.
        </p>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {FEATURE_CARDS.map((feature, index) => {
          const IconComponent = feature.icon;
          return (
            <Card key={index} className="feature-card border border-gray-200 dark:border-gray-800 hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full">
                    <IconComponent className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Key Features */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gradient-primary">Key Features</CardTitle>
          <CardDescription>
            Comprehensive functionality for lead source management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">
                Core Functionality
              </h4>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Create and manage lead sources
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Generate unique webhook URLs
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Real-time status toggling
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Form validation with Zod
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Toast notifications
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-3 text-gray-900 dark:text-white">
                User Experience
              </h4>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Responsive design (mobile & desktop)
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Dark/light mode support
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Expandable mobile rows
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Copy webhook URLs to clipboard
                </li>
                <li className="flex items-center">
                  <Badge variant="secondary" className="mr-2 text-xs">✓</Badge>
                  Loading states and error handling
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Stack */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gradient-accent">Technical Stack</CardTitle>
          <CardDescription>
            Built with modern React ecosystem tools
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                Framework
              </h4>
              <Badge variant="outline">React 18</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                Styling
              </h4>
              <Badge variant="outline">Tailwind CSS</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                Forms
              </h4>
              <Badge variant="outline">React Hook Form</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                Validation
              </h4>
              <Badge variant="outline">Zod</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                Icons
              </h4>
              <Badge variant="outline">Lucide React</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                Notifications
              </h4>
              <Badge variant="outline">Sonner</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                State
              </h4>
              <Badge variant="outline">RTK Query</Badge>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-gray-900 dark:text-white">
                TypeScript
              </h4>
              <Badge variant="outline">Full Support</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle className="text-gradient-purple">Getting Started</CardTitle>
          <CardDescription>
            Quick steps to integrate the Lead Source Manager
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold">
                1
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">Install Dependencies</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Install the required packages using npm or yarn
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold">
                2
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">Import Component</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Import the LeadSourceManager component in your React application
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold">
                3
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">Configure API</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Set up your backend API endpoints for webhook management
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-semibold">
                4
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white">Start Managing</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Begin creating and managing your lead sources
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
