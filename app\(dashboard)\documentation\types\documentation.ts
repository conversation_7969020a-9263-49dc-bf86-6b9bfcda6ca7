export interface ExampleWebhook {
  id: string;
  name: string;
  type: string;
  status: boolean;
  webhook_url: string;
  description: string;
}

export interface CodeExample {
  id: string;
  title: string;
  code: string;
  language: string;
}

export interface APIEndpoint {
  endpoint: string;
  method: string;
  description: string;
}

export interface ComponentProp {
  prop: string;
  type: string;
  default: string;
  description: string;
}

export interface IntegrationPlatform {
  category: string;
  platforms: string[];
}

export interface DocumentationSection {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

export interface TabContent {
  value: string;
  label: string;
  content: React.ReactNode;
}

export interface DocumentationState {
  activeTab: string;
  copied: string | null;
  expandedRow: string | null;
  loading: boolean;
}

export interface AccordionItem {
  value: string;
  title: string;
  content: React.ReactNode;
}

export interface FeatureCard {
  title: string;
  description: string;
  icon: React.ReactNode;
}

export interface ButtonVariant {
  variant: string;
  label: string;
  className?: string;
}

export interface TextGradient {
  className: string;
  title: string;
  description: string;
}

export interface LoaderExample {
  size: "sm" | "md" | "lg";
  variant: string;
  label: string;
}
