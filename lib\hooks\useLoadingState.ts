"use client";

import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/lib/store/store';
import { startLoading, stopLoading, setLoadingProgress } from '@/lib/store/slices/loadingSlice';

interface LoadingState {
  isLoading?: boolean;
  isFetching?: boolean;
  isError?: boolean;
  error?: any;
}

interface UseLoadingStateOptions {
  message?: string;
  skipLoading?: boolean;
  dependencies?: any[];
}

/**
 * A hook that integrates RTK Query loading states with the global loading provider
 * 
 * @param loadingStates - An array of loading states from RTK Query hooks
 * @param options - Options for customizing the loading behavior
 */
export function useLoadingState(
  loadingStates: LoadingState[],
  options: UseLoadingStateOptions = {}
) {
  const dispatch = useDispatch();
  const loadingState = useSelector((state: RootState) => state.loading);
  const { message, skipLoading = false, dependencies = [] } = options;

  // Determine if any of the loading states are loading or fetching
  const isAnyLoading = loadingStates.some(state => state?.isLoading || state?.isFetching);
  
  // Determine if any of the loading states have errors
  const isAnyError = loadingStates.some(state => state?.isError);
  
  // Collect all errors
  const errors = loadingStates
    .filter(state => state?.error)
    .map(state => state.error);

  useEffect(() => {
    // Skip if skipLoading is true
    if (skipLoading) return;

    // Start loading if any of the states are loading
    if (isAnyLoading) {
      dispatch(startLoading(message));
    } else {
      dispatch(stopLoading());
    }

    // Clean up when component unmounts
    return () => {
      if (isAnyLoading) {
        stopLoading();
      }
    };
  }, [isAnyLoading, message, startLoading, stopLoading, skipLoading, ...dependencies]);

  return {
    isLoading: isAnyLoading,
    isError: isAnyError,
    errors,
  };
}

/**
 * A hook that integrates a single RTK Query loading state with the global loading provider
 */
export function useSingleLoadingState(
  loadingState: LoadingState,
  options: UseLoadingStateOptions = {}
) {
  return useLoadingState([loadingState], options);
}
