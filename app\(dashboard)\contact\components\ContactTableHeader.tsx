import React from 'react';
import { TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Resizable } from 'react-resizable';
import { CONTACT_CONSTANTS } from '../constants/contact';

interface ContactTableHeaderProps {
  selectedHeaders: string[];
  columnWidths: Record<string, number>;
  availableColumns: string[];
  dropdownOpen: boolean;
  dropdownOpenRemove: string | null;
  onResize: (header: string) => (event: React.SyntheticEvent, data: any) => void;
  onToggleDropdown: (header: string) => void;
  onToggleAddDropdown: () => void;
  onAddColumn: (event: React.ChangeEvent<HTMLSelectElement>) => void;
  onRemoveColumn: (header: string) => void;
}

export function ContactTableHeader({
  selectedHeaders,
  columnWidths,
  availableColumns,
  dropdownOpen,
  dropdownOpenRemove,
  onResize,
  onToggleDropdown,
  onToggleAddDropdown,
  onAddColumn,
  onRemoveColumn,
}: ContactTableHeaderProps) {
  return (
    <TableHeader className="hidden md:table-header-group bg-muted/50">
      <TableRow>
        {selectedHeaders.map((header) => (
          <TableHead
            key={header}
            className="relative text-center font-medium text-muted-foreground"
            style={{ width: columnWidths[header] }}
          >
            <Resizable
              width={columnWidths[header]}
              height={30}
              axis="x"
              resizeHandles={['e']}
              onResize={onResize(header)}
            >
              <div
                className="flex justify-center items-center cursor-pointer"
                style={{ width: '100%' }}
              >
                <span onClick={() => onToggleDropdown(header)}>
                  {header}
                </span>
                <span className="w-2 h-full cursor-ew-resize opacity-30"></span>
              </div>
            </Resizable>

            {/* Dropdown menu for removing column */}
            {dropdownOpenRemove === header && (
              <div className="absolute right-0 mt-2 bg-popover border shadow-md rounded-md p-2 w-40 z-50">
                <button
                  className="w-full text-left px-2 py-1 hover:bg-destructive hover:text-destructive-foreground rounded-md text-sm transition-colors"
                  onClick={() => onRemoveColumn(header)}
                >
                  Hide Column
                </button>
              </div>
            )}
          </TableHead>
        ))}
        
        {/* Add Column Button */}
        <TableHead className="text-center">
          <button
            onClick={onToggleAddDropdown}
            className="p-2 rounded-full bg-primary hover:bg-primary/90 text-primary-foreground transition-colors"
          >
            <Plus className="w-4 h-4" />
          </button>
          
          {dropdownOpen && (
            <div className="absolute right-0 mt-2 bg-popover border shadow-md rounded-md p-2 w-40 z-50">
              <select
                className="w-full border p-2 rounded text-sm bg-background"
                onChange={(e) => {
                  onAddColumn(e);
                  onToggleAddDropdown();
                }}
              >
                <option value="">Select Column</option>
                {availableColumns.map((header) => (
                  <option key={header} value={header}>
                    {header}
                  </option>
                ))}
              </select>
            </div>
          )}
        </TableHead>
      </TableRow>
    </TableHeader>
  );
}
