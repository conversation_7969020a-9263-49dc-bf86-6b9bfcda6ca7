import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";
import { useGetActiveWorkspaceQuery } from "@/lib/store/services/workspace";
import { useGetWebhooksQuery } from "@/lib/store/services/webhooks";
import { useGetFormsQuery } from "@/lib/store/services/forms";
import { LeadSource, FormsState } from "../types/forms";

export const useFormsData = () => {
  // Redux state
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);
  
  // API queries
  const { data: activeWorkspace, isLoading: isWorkspaceLoading } = useGetActiveWorkspaceQuery();
  const workspaceId = activeWorkspace?.data?.id;
  
  const { data: webhooksData, isLoading: isWebhooksLoading } = useGetWebhooksQuery(
    { id: workspaceId || '' }, 
    { skip: !workspaceId }
  );
  
  const { data: formsList, isLoading: isFormsLoading } = useGetFormsQuery(
    workspaceId || '', 
    { skip: !workspaceId }
  );

  // Local state
  const [leadSources, setLeadSources] = useState<LeadSource[]>([]);
  const [loading, setLoading] = useState(true);

  // Set lead sources from webhooks data when it's available
  useEffect(() => {
    if (webhooksData?.webhooks) {
      setLeadSources(webhooksData.webhooks);
      setLoading(false);
    } else if (webhooksData?.data) {
      setLeadSources(webhooksData.data);
      setLoading(false);
    }
  }, [webhooksData]);

  // Determine initial loading state
  const isInitialLoading = isWorkspaceLoading || (workspaceId && loading);

  return {
    // Workspace data
    activeWorkspace: activeWorkspace?.data || null,
    workspaceId,
    isCollapsed,

    // Forms data
    formsList: formsList || [],
    isFormsLoading,

    // Lead sources data
    leadSources,
    isWebhooksLoading,

    // Loading states
    isInitialLoading,
    loading,
    setLoading,
  };
};
