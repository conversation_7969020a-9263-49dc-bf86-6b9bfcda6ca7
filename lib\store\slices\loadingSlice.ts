import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface LoadingState {
  isLoading: boolean;
  loadingMessage: string | null;
  loadingProgress: number;
}

const initialState: LoadingState = {
  isLoading: false,
  loadingMessage: null,
  loadingProgress: 0,
};

export const loadingSlice = createSlice({
  name: 'loading',
  initialState,
  reducers: {
    startLoading: (state, action: PayloadAction<string | undefined>) => {
      state.isLoading = true;
      state.loadingMessage = action.payload || null;
      state.loadingProgress = 0;
    },
    stopLoading: (state) => {
      state.isLoading = false;
      state.loadingMessage = null;
      state.loadingProgress = 0;
    },
    setLoadingProgress: (state, action: PayloadAction<number>) => {
      state.loadingProgress = action.payload;
    },
  },
});

export const { startLoading, stopLoading, setLoadingProgress } = loadingSlice.actions;

export default loadingSlice.reducer;
