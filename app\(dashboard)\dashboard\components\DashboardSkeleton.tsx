import React from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface DashboardSkeletonProps {
  isCollapsed?: boolean;
}

export const DashboardSkeleton: React.FC<DashboardSkeletonProps> = ({ isCollapsed = false }) => {
  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <div className="flex flex-col space-y-6 w-full">
        {/* Header Skeleton */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 w-full">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-32" />
          </div>
        </div>

        {/* Tabs Skeleton */}
        <div className="border-b dark:border-gray-700 flex space-x-6 mb-5">
          <Skeleton className="h-6 w-20 mb-2" />
        </div>

        {/* Stats Cards Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="overflow-hidden border rounded-lg">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="flex flex-col space-y-2 flex-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Content Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          {/* Chart Skeleton */}
          <Card className="lg:col-span-2 overflow-hidden border rounded-lg">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-40" />
                <div className="hidden md:flex items-center space-x-4">
                  <div className="flex items-center">
                    <Skeleton className="h-3 w-3 rounded-full mr-2" />
                    <Skeleton className="h-4 w-12" />
                  </div>
                  <div className="flex items-center">
                    <Skeleton className="h-3 w-3 rounded-full mr-2" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </div>
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              {/* Desktop Chart */}
              <div className="hidden md:block w-full h-[280px]">
                <div className="flex items-end justify-between space-x-2 h-full">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <div key={index} className="flex flex-col items-center space-y-2 flex-1 h-full">
                      <Skeleton className="w-full rounded-t-lg"
                               style={{ height: `${60 + (index * 20)}%` }} />
                      <Skeleton className="h-3 w-8" />
                    </div>
                  ))}
                </div>
              </div>
              {/* Mobile Chart */}
              <div className="block md:hidden w-full h-[200px]">
                <div className="flex items-end justify-between space-x-1 h-full">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="flex flex-col items-center space-y-1 flex-1 h-full">
                      <Skeleton className="w-full rounded-t"
                               style={{ height: `${50 + (index * 15)}%` }} />
                      <Skeleton className="h-2 w-6" />
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Sales Skeleton */}
          <Card className="border rounded-lg h-[500px] lg:h-[calc(100vh-13rem)] max-h-[700px] flex flex-col">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent className="flex-1">
              {/* Team Performance Skeleton */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-3">
                  <Skeleton className="h-5 w-36" />
                </div>
                <div className="space-y-2">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="flex items-center justify-between p-2 rounded-md">
                      <div className="flex items-center">
                        <Skeleton className="h-7 w-7 rounded-full mr-2" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                      <div className="flex flex-col items-end space-y-1">
                        <Skeleton className="h-4 w-16" />
                        <Skeleton className="h-3 w-12" />
                      </div>
                    </div>
                  ))}
                </div>
                <Skeleton className="h-px w-full mt-4 mb-2" />
              </div>

              {/* Recent Transactions Skeleton */}
              <Skeleton className="h-5 w-40 mb-3" />
              <div className="space-y-3">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center p-3 rounded-lg border">
                    <Skeleton className="h-9 w-9 rounded-full mr-3" />
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-baseline">
                        <div className="flex-1">
                          <Skeleton className="h-4 w-32 mb-1" />
                          <div className="flex items-center">
                            <Skeleton className="h-3 w-40" />
                            <span className="mx-1.5 text-gray-300 hidden sm:inline">•</span>
                            <Skeleton className="h-3 w-16 hidden sm:block" />
                          </div>
                        </div>
                        <div className="flex flex-col items-end">
                          <Skeleton className="h-4 w-16 mb-1" />
                          <div className="flex items-center">
                            <Skeleton className="h-3 w-6" />
                            <Skeleton className="h-3 w-16 ml-1 hidden sm:block" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Mobile Dashboard Skeleton
export const DashboardMobileSkeleton: React.FC = () => {
  return (
    <div className="p-4 space-y-6">
      {/* Header */}
      <Skeleton className="h-8 w-32" />

      {/* Tabs */}
      <Skeleton className="h-6 w-20" />

      {/* Stats Cards - Mobile Layout */}
      <div className="grid grid-cols-1 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-4 w-20 mb-1" />
                <Skeleton className="h-6 w-16 mb-1" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Chart Card */}
      <Card className="p-4">
        <Skeleton className="h-6 w-32 mb-4" />
        <div className="h-48 flex items-end justify-between space-x-1">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="w-full rounded-t"
                     style={{ height: `${Math.random() * 100 + 30}px` }} />
          ))}
        </div>
      </Card>

      {/* Recent Sales Card */}
      <Card className="p-4">
        <Skeleton className="h-6 w-28 mb-4" />
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-3 w-32" />
              </div>
              <Skeleton className="h-4 w-12" />
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
