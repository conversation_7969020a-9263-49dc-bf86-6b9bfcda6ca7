version: '3.8'

services:
  # Nginx load balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app1
      - app2
      - app3
    restart: always
    networks:
      - app-network

  # Application instances
  app1:
    build:
      context: ..
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY=${NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://redis:6379
      - INSTANCE_ID=1
    depends_on:
      - redis
      - postgres
    restart: always
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  app2:
    build:
      context: ..
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY=${NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://redis:6379
      - INSTANCE_ID=2
    depends_on:
      - redis
      - postgres
    restart: always
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  app3:
    build:
      context: ..
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY=${NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://redis:6379
      - INSTANCE_ID=3
    depends_on:
      - redis
      - postgres
    restart: always
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  # Redis cache
  redis:
    image: redis:alpine
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    restart: always
    networks:
      - app-network

  # PostgreSQL database with optimized settings
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    restart: always
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G

  # PgBouncer connection pooler
  pgbouncer:
    image: edoburu/pgbouncer:latest
    environment:
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=postgres
      - DB_NAME=${POSTGRES_DB}
      - POOL_MODE=transaction
      - MAX_CLIENT_CONN=1000
      - DEFAULT_POOL_SIZE=20
      - RESERVE_POOL_SIZE=10
      - RESERVE_POOL_TIMEOUT=5
    depends_on:
      - postgres
    restart: always
    networks:
      - app-network

volumes:
  redis-data:
  postgres-data:

networks:
  app-network:
    driver: bridge
