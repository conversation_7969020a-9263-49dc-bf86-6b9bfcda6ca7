import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { PlanTier } from '@/lib/types/subscription';

/**
 * Initialize user metadata with default values if they don't exist
 * This ensures all users have the necessary metadata fields
 * 
 * @param userId The user's ID
 * @returns A promise that resolves when the metadata has been initialized
 */
export async function initializeUserMetadata(userId: string): Promise<void> {
  try {
    const supabase = createClientComponentClient();
    
    // Get the current user data
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      throw userError;
    }
    
    if (!userData.user) {
      throw new Error('User not found');
    }
    
    const currentMetadata = userData.user.user_metadata || {};
    
    // Only initialize if subscription data doesn't exist
    if (!currentMetadata.subscription) {
      // Set default subscription to starter plan
      const defaultSubscription = {
        planId: 'starter' as PlanTier,
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14-day trial
        cancelAtPeriodEnd: false,
        paymentMethod: 'none',
        trialEnd: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14-day trial
      };
      
      // Update the user's metadata
      const { error: updateError } = await supabase.auth.updateUser({
        data: {
          ...currentMetadata,
          subscription: defaultSubscription,
        },
      });
      
      if (updateError) {
        throw updateError;
      }
    }
    
    // Initialize other metadata fields if they don't exist
    const metadataToUpdate = {
      ...currentMetadata,
      // Add any other default metadata fields here
      firstName: currentMetadata.firstName || currentMetadata.name || '',
      lastName: currentMetadata.lastName || '',
      role: currentMetadata.role || 'user',
      onboardingCompleted: currentMetadata.onboardingCompleted || false,
      preferences: currentMetadata.preferences || {
        notifications: {
          email: true,
          push: true,
          inApp: true,
        },
        theme: 'system',
      },
    };
    
    // Only update if there are changes
    if (JSON.stringify(currentMetadata) !== JSON.stringify(metadataToUpdate)) {
      const { error: updateError } = await supabase.auth.updateUser({
        data: metadataToUpdate,
      });
      
      if (updateError) {
        throw updateError;
      }
    }
  } catch (error) {
    console.error('Error initializing user metadata:', error);
    throw error;
  }
}

/**
 * Get user subscription data from metadata
 * 
 * @param userId The user's ID (optional, defaults to current user)
 * @returns The user's subscription data
 */
export async function getUserSubscription(userId?: string) {
  try {
    const supabase = createClientComponentClient();
    
    // Get the current user data
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      throw userError;
    }
    
    if (!userData.user) {
      throw new Error('User not found');
    }
    
    const metadata = userData.user.user_metadata || {};
    return metadata.subscription || null;
  } catch (error) {
    console.error('Error getting user subscription:', error);
    return null;
  }
}
