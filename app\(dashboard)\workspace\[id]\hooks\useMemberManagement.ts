import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import {
  useAddMemberMutation,
  useDeleteMemberMutation,
  useResendInviteMutation,
  useSendSecureInviteMutation,
} from '@/lib/store/services/members';
import { WorkspaceMember } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';

interface UseMemberManagementProps {
  workspaceId: string;
  workspaceMembers?: any;
}

export function useMemberManagement({ workspaceId, workspaceMembers }: UseMemberManagementProps) {
  const [addMember, { isLoading: isAdding }] = useAddMemberMutation();
  const [deleteMember, { isLoading: isDeleting }] = useDeleteMemberMutation();
  const [resendInvite, { isLoading: isResending }] = useResendInviteMutation();

  // State management
  const [members, setMembers] = useState<WorkspaceMember[]>([]);
  const [memberToDelete, setMemberToDelete] = useState<WorkspaceMember | null>(null);

  // Initialize members from API data
  useEffect(() => {
    if (workspaceMembers?.data) {
      setMembers(workspaceMembers.data);
    }
  }, [workspaceMembers]);

  // Add new member using new secure invitation system
  const handleMemberAdd = async (newMember: WorkspaceMember) => {
    try {
      // Use the new secure invitation API
      const response = await fetch(`/api/members/invite?workspaceId=${workspaceId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newMember.email,
          role: newMember.role
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to send invitation');
        return;
      }

      const result = await response.json();

      // Add the pending member to local state
      const pendingMember: WorkspaceMember = {
        id: result.data.id,
        email: newMember.email,
        role: newMember.role,
        status: 'pending',
        name: newMember.email.split('@')[0], // Temporary name
      };

      setMembers([...members, pendingMember]);
      toast.success('Invitation sent successfully');
    } catch (error) {
      console.error('Unexpected error:', error);
      toast.error(WORKSPACE_CONSTANTS.MESSAGES.UNEXPECTED_ERROR);
    }
  };

  // Delete member
  const handleMemberDelete = async (memberId: string) => {
    try {
      const result = await deleteMember({ workspaceId, id: memberId });

      if ('error' in result) {
        const errorDetails = (result.error as any).data;
        toast.error(errorDetails.error || WORKSPACE_CONSTANTS.MESSAGES.MEMBER_DELETE_ERROR);
        return;
      }

      // Update local state
      setMembers((prevMembers) =>
        prevMembers.filter((member) => member.id !== memberId)
      );
      toast.success(WORKSPACE_CONSTANTS.MESSAGES.MEMBER_DELETED);
    } catch (error: any) {
      const errorMessage =
        error?.data?.error || WORKSPACE_CONSTANTS.MESSAGES.UNEXPECTED_ERROR;
      toast.error(errorMessage);
      console.error('Delete member error:', error);
    }
  };

  // Resend invite to member using new system
  const resendInviteToMember = async (member: WorkspaceMember) => {
    if (!member.email) {
      toast.error(WORKSPACE_CONSTANTS.MESSAGES.INVALID_MEMBER_INFO);
      return;
    }

    try {
      // For pending members, send a new invitation
      if (member.status === 'pending') {
        const response = await fetch(`/api/members/invite?workspaceId=${workspaceId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: member.email,
            role: member.role
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          toast.error(errorData.error || WORKSPACE_CONSTANTS.MESSAGES.INVITE_RESEND_ERROR);
          return;
        }

        toast.success(WORKSPACE_CONSTANTS.MESSAGES.INVITE_RESENT);
      } else {
        toast.error('Can only resend invitations to pending members');
      }
    } catch (error: any) {
      const errorMessage = error?.message || WORKSPACE_CONSTANTS.MESSAGES.UNEXPECTED_ERROR;
      toast.error(errorMessage);
      console.error('Resend invite error:', error);
    }
  };

  // Update member
  const handleMemberUpdate = (updatedMember: WorkspaceMember) => {
    setMembers(
      members.map((member) =>
        member.id === updatedMember.id ? updatedMember : member
      )
    );
  };

  // Delete member with confirmation
  const handleDeleteMember = (member: WorkspaceMember) => {
    setMemberToDelete(member);
  };

  const confirmDeleteMember = async () => {
    if (memberToDelete?.id) {
      await handleMemberDelete(memberToDelete.id);
      setMemberToDelete(null);
    }
  };

  const cancelDeleteMember = () => {
    setMemberToDelete(null);
  };

  return {
    // State
    members,
    memberToDelete,
    setMemberToDelete,

    // Loading states
    isAdding,
    isDeleting,
    isResending,

    // Actions
    handleMemberAdd,
    handleMemberDelete,
    handleMemberUpdate,
    resendInviteToMember,
    handleDeleteMember,
    confirmDeleteMember,
    cancelDeleteMember,
  };
}
