"use client";

import React from 'react';
import { LoadingButton } from "@/components/ui/loading-button";
import { SubscribeButtonProps } from '../types/subscription';
import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

export function SubscribeButton({ 
  selectedPlan, 
  loading, 
  onSubscribe, 
  isDisabled, 
  buttonText 
}: SubscribeButtonProps) {
  return (
    <>
      {/* Subscribe Button */}
      <div className="mt-12 md:mt-16 flex justify-center w-full">
        <div className="relative w-full max-w-md mx-auto group">
          <div className="absolute -inset-1 bg-gradient-to-r from-primary to-blue-600 rounded-xl blur-lg opacity-70 group-hover:opacity-100 transition duration-300"></div>
          <LoadingButton
            className="relative px-8 md:px-12 py-4 md:py-6 text-base md:text-lg shadow-xl w-full bg-gradient-to-r from-primary to-blue-600 hover:from-primary/90 hover:to-blue-600/90 border-0 transition-all duration-300 transform group-hover:scale-[1.01]"
            onClick={onSubscribe}
            disabled={isDisabled}
            isLoading={loading}
            loadingText="Processing payment..."
            size="lg"
          >
            <span className="flex items-center justify-center gap-2" suppressHydrationWarning>
              {!loading && (
                <svg 
                  width="20" 
                  height="20" 
                  viewBox="0 0 24 24" 
                  fill="none" 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="animate-pulse"
                >
                  <path 
                    d="M20.24 12.24C21.3658 11.1142 21.9983 9.58722 21.9983 7.99504C21.9983 6.40285 21.3658 4.87588 20.24 3.75004C19.1142 2.62419 17.5872 1.9917 15.995 1.9917C14.4028 1.9917 12.8758 2.62419 11.75 3.75004L5 10.5V19H13.5L20.24 12.24Z" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                  />
                  <path d="M16 8L2 22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M17.5 15H9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              )}
              {buttonText}
            </span>
          </LoadingButton>
        </div>
      </div>
      
      {/* Security Note */}
      <div className="mt-4 flex justify-center">
        <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
            <path d="M12 16V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
            <path d="M12 8H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
          </svg>
          {SUBSCRIPTION_CONSTANTS.MESSAGES.NO_CHARGE_UNTIL_CONFIRM}
        </div>
      </div>
    </>
  );
}
