import { SUBSCRIPTION_CONSTANTS } from '../constants/subscription';

/**
 * Calculate price with discount for yearly billing
 * @param basePrice - Monthly base price
 * @param billingCycle - Monthly or yearly billing cycle
 * @returns Formatted price string
 */
export function calculatePrice(basePrice: number, billingCycle: 'monthly' | 'yearly'): string {
  // Pre-calculate both values to avoid conditional logic that might cause hydration issues
  const yearlyPrice = Math.floor(basePrice * 12 * SUBSCRIPTION_CONSTANTS.YEARLY_DISCOUNT);
  const monthlyPrice = basePrice;

  // Create a lookup object instead of conditional logic
  const prices = {
    'monthly': monthlyPrice.toString(),
    'yearly': yearlyPrice.toString()
  };

  // Return the appropriate price based on billing cycle
  return prices[billingCycle];
}

/**
 * Calculate yearly savings percentage
 * @param monthlyPrice - Monthly price
 * @returns Savings percentage
 */
export function calculateYearlySavings(monthlyPrice: number): number {
  const yearlyPrice = monthlyPrice * 12 * SUBSCRIPTION_CONSTANTS.YEARLY_DISCOUNT;
  const monthlyCost = monthlyPrice * 12;
  return Math.round(((monthlyCost - yearlyPrice) / monthlyCost) * 100);
}

/**
 * Format price for display
 * @param price - Price amount
 * @param billingCycle - Billing cycle
 * @returns Formatted price string with currency and period
 */
export function formatPrice(price: string, billingCycle: 'monthly' | 'yearly'): string {
  const suffix = billingCycle === 'monthly' ? '/mo' : '/yr';
  return `$${price}${suffix}`;
}

/**
 * Get price display for plan
 * @param basePrice - Base monthly price
 * @param billingCycle - Billing cycle
 * @returns Complete formatted price string
 */
export function getPlanPrice(basePrice: number, billingCycle: 'monthly' | 'yearly'): string {
  if (basePrice === 0) return 'Free';
  
  const calculatedPrice = calculatePrice(basePrice, billingCycle);
  return formatPrice(calculatedPrice, billingCycle);
}
