import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import { FileDown, FileUp } from "lucide-react";
import { Lead, LeadSource } from "../types/leads";
import { exportToCSV, exportToJSON, handleImportCSV, validateImportFile } from "../utils/exportUtils";

interface ExportActionsProps {
  leads: Lead[];
  leadSources?: { data: LeadSource[] };
  createManyLead: any;
  workspaceId: string;
  onImportSuccess: (leads: any[]) => void;
  isMobile?: boolean;
}

export const ExportActions: React.FC<ExportActionsProps> = ({
  leads,
  leadSources,
  createManyLead,
  workspaceId,
  onImportSuccess,
  isMobile = false,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExportCSV = () => {
    exportToCSV(leads, leadSources);
  };

  const handleExportJSON = () => {
    exportToJSON(leads);
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!validateImportFile(file)) {
      event.target.value = "";
      return;
    }

    try {
      await handleImportCSV(file, createManyLead, workspaceId, onImportSuccess);
    } catch (error) {
      console.error("Import error:", error);
    } finally {
      // Reset input value to allow re-uploading the same file
      event.target.value = "";
    }
  };

  if (isMobile) {
    return (
      <div className="grid grid-cols-3 gap-2 col-span-6 px-4 row-start-3 row-end-4 md:hidden">
        <Button
          variant="outline"
          onClick={handleExportCSV}
          className="text-xs h-9 border-gray-200 dark:border-gray-800 bg-white dark:bg-black text-black dark:text-white"
          size="sm"
        >
          <FileDown className="mr-1 h-3 w-3" /> Export
        </Button>

        <Button
          variant="outline"
          onClick={handleExportJSON}
          className="text-xs h-9 border-gray-200 dark:border-gray-800 bg-white dark:bg-black text-black dark:text-white"
          size="sm"
        >
          <FileDown className="mr-1 h-3 w-3" /> JSON
        </Button>

        <Button
          variant="outline"
          onClick={handleImportClick}
          className="text-xs h-9 border-gray-200 dark:border-gray-800 bg-white dark:bg-black text-black dark:text-white"
          size="sm"
        >
          <FileUp className="mr-1 h-3 w-3" /> Import
        </Button>

        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          className="hidden"
          onChange={handleFileChange}
        />
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        onClick={handleExportCSV}
        className="border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-900"
        size="sm"
      >
        <FileDown className="mr-2 h-4 w-4 text-black dark:text-white" /> CSV
      </Button>
      
      <Button
        variant="outline"
        onClick={handleExportJSON}
        className="border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-900"
        size="sm"
      >
        <FileDown className="mr-2 h-4 w-4 text-black dark:text-white" /> JSON
      </Button>
      
      <Button
        variant="outline"
        onClick={handleImportClick}
        className="border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-900"
        size="sm"
      >
        <FileUp className="mr-2 h-4 w-4 text-black dark:text-white" /> Import
      </Button>

      <input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        className="hidden"
        onChange={handleFileChange}
      />
    </div>
  );
};
