-- Migration for table: leads
CREATE TABLE IF NOT EXISTS "leads" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "user_id" uuid NOT NULL,
  "name" text,
  "email" text,
  "phone" text,
  "custom_data" jsonb,
  "work_id" integer,
  "lead_source_id" uuid,
  "text_area" text,
  "source" text,
  "contact_method" text,
  "message" text,
  "status" jsonb,
  "company" text,
  "position" text,
  "revenue" numeric,
  "assign_to" text,
  "is_email_valid" boolean,
  "is_phone_valid" boolean,
  "businessInfo" text,
  "tag" text,
  "tags" text,
  "url" text,
  "webhook_id" uuid
);

-- Create index on user_id
CREATE INDEX IF NOT EXISTS "leads_user_id_idx" ON "leads" ("user_id");

-- Add foreign key constraint for user_id (commented out, uncomment after verifying)
-- ALTER TABLE "leads" ADD CONSTRAINT "fk_leads_user_id"
--   FOREI<PERSON>N KEY ("user_id") REFERENCES "auth.users" (id);

-- Enable Row Level Security
ALTER TABLE "leads" ENABLE ROW LEVEL SECURITY;

