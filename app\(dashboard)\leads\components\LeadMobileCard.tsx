import React from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Check,
  X,
  ChevronDown,
  ChevronUp,
  Send,
  Phone,
  MessageCircle,
  Pencil,
  Eye,
  SquareCode,
  UserIcon,
} from "lucide-react";
import { Lead, WorkspaceMember, Status } from "../types/leads";
import { formatDate } from "@/utils/date";
import {
  getLeadStatusDisplay,
  getLeadAssignmentDisplay,
  createStatusSelectValue,
  createMemberSelectValue
} from "../utils/leadDataUtils";

interface LeadMobileCardProps {
  lead: Lead;
  isSelected: boolean;
  isExpanded: boolean;
  workspaceMembers?: { data: WorkspaceMember[] };
  statusData?: { data: Status[] };
  onToggleSelection: () => void;
  onToggleExpand: () => void;
  onEdit: () => void;
  onView: () => void;
  onStatusChange: (value: string) => void;
  onAssignChange: (value: string) => void;
  onInitiateContact: (method: string) => void;
}

export const LeadMobileCard: React.FC<LeadMobileCardProps> = ({
  lead,
  isSelected,
  isExpanded,
  workspaceMembers,
  statusData,
  onToggleSelection,
  onToggleExpand,
  onEdit,
  onView,
  onStatusChange,
  onAssignChange,
  onInitiateContact,
}) => {
  const getContactIcon = (method: string) => {
    switch (method) {
      case "WhatsApp":
        return <Send className="h-3.5 w-3.5 text-black dark:text-white" />;
      case "Call":
        return <Phone className="h-3.5 w-3.5 text-black dark:text-white" />;
      case "SMS":
        return <MessageCircle className="h-3.5 w-3.5 text-black dark:text-white" />;
      default:
        return <MessageCircle className="h-3.5 w-3.5 text-black dark:text-white" />;
    }
  };

  return (
    <>
      <TableRow className="flex md:hidden lg:hidden items-center justify-between text-sm border-b border-gray-200 dark:border-gray-800 py-3 last:border-none hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg transition-colors">
        <div className="flex items-center gap-3 flex-1">
          <TableCell className="p-0 pl-2">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onToggleSelection}
              className="h-4 w-4"
            />
          </TableCell>
          <div className="flex-1 min-w-0">
            <div className="flex items-center mb-1">
              <h3 className="font-medium text-black dark:text-white truncate mr-2">
                {lead.Name}
              </h3>
              {lead.isDuplicate && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-black/10 text-black dark:bg-white/10 dark:text-white">
                  Duplicate
                </span>
              )}
            </div>

            <div className="flex items-center text-sm text-black dark:text-white mb-1">
              <div className="flex items-center">
                {lead.is_email_valid ? (
                  <Check className="w-3.5 h-3.5 text-black dark:text-white mr-1 flex-shrink-0" />
                ) : (
                  <X className="w-3.5 h-3.5 text-black dark:text-white mr-1 flex-shrink-0" />
                )}
                <span className="truncate">{lead.email}</span>
              </div>
            </div>

            <div className="flex items-center text-xs text-black dark:text-white">
              {(() => {
                const statusDisplay = getLeadStatusDisplay(lead, statusData?.data || []);
                return (
                  <span className="inline-flex items-center mr-3">
                    <span
                      className="w-2 h-2 rounded-full mr-1"
                      style={{ backgroundColor: statusDisplay.color }}
                    ></span>
                    <span className="truncate max-w-[80px]">
                      {statusDisplay.name}
                    </span>
                  </span>
                );
              })()}
              <span>{formatDate(lead.createdAt)}</span>
            </div>
          </div>
        </div>
        <TableCell className="p-0 pr-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleExpand}
            className="h-8 w-8 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4 text-black dark:text-white" />
            ) : (
              <ChevronDown className="h-4 w-4 text-black dark:text-white" />
            )}
          </Button>
        </TableCell>
      </TableRow>

      {isExpanded && (
        <TableRow className="md:hidden">
          <div className="px-4 py-3 bg-gray-50 dark:bg-black rounded-lg mb-2 space-y-4 border-t border-gray-200 dark:border-gray-800">
            {/* Phone Information */}
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-black dark:text-white">
                <Phone className="w-4 h-4 mr-2 text-black dark:text-white" />
                <span>Phone</span>
              </div>
              <div className="flex items-center">
                {lead.is_phone_valid ? (
                  <Check className="w-3.5 h-3.5 text-black dark:text-white mr-1.5" />
                ) : (
                  <X className="w-3.5 h-3.5 text-black dark:text-white mr-1.5" />
                )}
                <span
                  className={`font-medium ${
                    lead.is_phone_valid
                      ? "text-black dark:text-white"
                      : "text-black dark:text-white"
                  }`}
                >
                  {lead.phone}
                </span>
                {!lead.is_phone_valid && (
                  <span className="ml-2 text-xs bg-black/10 text-black dark:bg-white/10 dark:text-white px-1.5 py-0.5 rounded">
                    Invalid
                  </span>
                )}
              </div>
            </div>

            {/* Company Information */}
            {lead.company && (
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm text-black dark:text-white">
                  <SquareCode className="w-4 h-4 mr-2 text-black dark:text-white" />
                  <span>Company</span>
                </div>
                <div className="text-black dark:text-white font-medium">
                  {lead.company}
                  {lead.position && (
                    <span className="text-black dark:text-white ml-1">
                      ({lead.position})
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Contact Method */}
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-black dark:text-white">
                <MessageCircle className="w-4 h-4 mr-2 text-black dark:text-white" />
                <span>Contact Via</span>
              </div>
              <div className="bg-black/10 text-black dark:bg-white/10 dark:text-white px-2 py-1 rounded text-xs font-medium">
                {lead.contact_method}
              </div>
            </div>

            {/* Actions */}
            <div className="border-t border-gray-200 dark:border-gray-800 pt-3 flex justify-between items-center">
              <span className="text-sm text-black dark:text-white">Actions</span>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onInitiateContact(lead.contact_method)}
                  className="h-8 w-8 p-0 border-gray-200 dark:border-gray-800"
                  title={`Contact via ${lead.contact_method}`}
                >
                  {getContactIcon(lead.contact_method)}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onEdit}
                  className="h-8 w-8 p-0 border-gray-200 dark:border-gray-800"
                >
                  <Pencil className="h-3.5 w-3.5 text-black dark:text-white" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onView}
                  className="h-8 w-8 p-0 border-gray-200 dark:border-gray-800"
                >
                  <Eye className="h-3.5 w-3.5 text-black dark:text-white" />
                </Button>
              </div>
            </div>
          </div>

          {/* Status and Assignment Controls */}
          <div className="px-3 py-2 grid grid-cols-2 items-center">
            <span className="text-black dark:text-white">Status</span>
            <Select
              defaultValue={createStatusSelectValue(lead.status_id, statusData?.data || [], lead.status)}
              onValueChange={onStatusChange}
            >
              <SelectTrigger className="group relative overflow-hidden rounded-xl border-0 bg-white px-4 py-3 shadow-lg transition-all duration-200 hover:-translate-y-0.5 hover:shadow-xl dark:bg-black">
                {(() => {
                  const statusDisplay = getLeadStatusDisplay(lead, statusData?.data || []);
                  return (
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div
                          className="absolute -inset-1 rounded-lg bg-gray-400 opacity-20 blur-sm transition-opacity duration-200 group-hover:opacity-30"
                          style={{
                            backgroundColor: statusDisplay.color,
                          }}
                        />
                        <div
                          className="relative h-3 w-3 rounded-lg bg-gray-400"
                          style={{
                            backgroundColor: statusDisplay.color,
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium truncate max-w-[100px]">
                        {statusDisplay.name}
                      </span>
                    </div>
                  );
                })()}
              </SelectTrigger>

              <SelectContent className="overflow-hidden rounded-xl border-0 bg-white p-2 shadow-2xl dark:bg-black">
                {statusData?.data.map((status: Status) => (
                  <SelectItem
                    key={status.id || status.name}
                    value={JSON.stringify({
                      id: status.id,
                      name: status.name,
                      color: status.color,
                    })}
                    className="cursor-pointer rounded-lg outline-none transition-colors focus:bg-transparent"
                  >
                    <div className="group flex items-center gap-3 rounded-lg p-2 transition-all hover:bg-gray-50 dark:hover:bg-gray-900/50">
                      <div className="relative">
                        <div
                          className="absolute -inset-1 rounded-lg opacity-20 blur-sm transition-all duration-200 group-hover:opacity-40"
                          style={{
                            backgroundColor: status?.color,
                          }}
                        />
                        <div
                          className="relative h-3 w-3 rounded-lg transition-transform duration-200 group-hover:scale-110"
                          style={{
                            backgroundColor: status?.color,
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                        {status.name}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="px-3 py-1 grid grid-cols-2 items-center">
            <span className="text-black dark:text-white">Assign</span>
            <Select
              defaultValue={createMemberSelectValue(lead.assigned_to_id, workspaceMembers?.data || [], lead.assign_to)}
              onValueChange={onAssignChange}
            >
              <SelectTrigger className="group relative overflow-hidden rounded-xl border-0 bg-white px-4 py-3 shadow-lg transition-all duration-200 hover:-translate-y-0.5 hover:shadow-xl dark:bg-black">
                {(() => {
                  const assignmentDisplay = getLeadAssignmentDisplay(lead, workspaceMembers?.data || []);
                  return (
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="absolute -inset-1 rounded-lg bg-gray-400 opacity-20 blur-sm transition-opacity duration-200 group-hover:opacity-30" />
                        <div className="relative">
                          <UserIcon className="h-6 w-6 text-black dark:text-white" />
                        </div>
                      </div>
                      <span className="text-sm font-medium truncate max-w-[100px] text-black dark:text-white">
                        {assignmentDisplay.name}
                      </span>
                    </div>
                  );
                })()}
              </SelectTrigger>

              <SelectContent className="overflow-hidden rounded-xl border-0 bg-white p-2 shadow-2xl dark:bg-black">
                <SelectItem
                  key="unassigned"
                  value={JSON.stringify({
                    id: null,
                    name: "Unassigned",
                    role: "none",
                  })}
                  className="cursor-pointer rounded-lg outline-none transition-colors focus:bg-transparent"
                >
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                      Unassigned
                    </span>
                  </div>
                </SelectItem>
                {workspaceMembers?.data
                  .filter((member: WorkspaceMember) => member.name && member.name !== "null")
                  .map((member: WorkspaceMember) => (
                    <SelectItem
                      key={member.id || member.name}
                      value={JSON.stringify({
                        id: member.id,
                        name: member.name,
                        role: member.role,
                      })}
                      className="cursor-pointer rounded-lg outline-none transition-colors focus:bg-transparent"
                    >
                      <div className="flex items-center gap-3">
                        <span className="text-sm font-medium text-black dark:text-white">
                          {member.name}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>
        </TableRow>
      )}
    </>
  );
};
