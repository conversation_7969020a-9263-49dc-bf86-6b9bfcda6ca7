"use client";

import React, { useState } from "react";
import { useSelector } from "react-redux";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Copy,
  Check,
  BookOpen,
  Code,
  ExternalLink,
  Palette,
} from "lucide-react";

// Hooks
import { useDocumentationState } from "./hooks/useDocumentationState";

// Components
import { DocumentationSkeleton } from "./components/DocumentationSkeleton";
import { OverviewTab } from "./components/OverviewTab";
import { InstallationTab } from "./components/InstallationTab";
import { CodeBlock, InlineCode, CodeSnippet } from "./components/CodeBlock";

// Constants
import {
  EXAMPLE_WEBHOOKS,
  API_ENDPOINTS,
  COMPONENT_PROPS,
  INTEGRATION_PLATFORMS,
  BUTTON_VARIANTS,
  GRADIENT_BUTTONS,
  TEXT_GRADIENTS,
  LOADER_EXAMPLES,
  CODE_EXAMPLES,
  TAB_LABELS,
} from "./constants/documentation";

// Utils
import { getMethodColor } from "./utils/documentationUtils";

// Types
import { RootState } from "@/lib/store/store";
import { Loader, LoadingOverlay } from "@/components/ui/loader";

const DocumentationPage: React.FC = () => {
  // Sidebar state
  const isCollapsed = useSelector(
    (state: RootState) => state.sidebar.isCollapsed
  );

  // Loading state for initial render
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Custom hooks
  const {
    activeTab,
    copied,
    expandedRow,
    loading,
    setActiveTab,
    handleCopy,
    toggleRow,
    triggerLoading,
  } = useDocumentationState();

  // Simulate initial loading
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return <DocumentationSkeleton isCollapsed={isCollapsed} />;
  }

  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[8px] md:rounded-[4px] overflow-hidden bg-white dark:bg-black border border-gray-200 dark:border-gray-800 shadow-sm">
        <CardHeader className="p-3 sm:p-4 md:p-6 pb-4 border-b border-gray-200 dark:border-gray-800">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center gap-3">
              <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-full">
                <BookOpen className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold text-gradient-primary-accent">
                  Documentation
                </CardTitle>
                <CardDescription className="text-black dark:text-white mt-1">
                  Complete guide for implementing and using the Lead Source Manager
                </CardDescription>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
              <Button
                variant="outline"
                onClick={() => window.open('https://github.com/yourusername/lead-source-manager', '_blank')}
                className="border-gray-200 dark:border-gray-800 hover:bg-gray-100 dark:hover:bg-gray-900"
                size="sm"
              >
                <ExternalLink className="mr-2 h-4 w-4 text-black dark:text-white" />
                View Source
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-3 sm:p-4 md:p-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="w-full overflow-x-auto mb-6">
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
                <TabsTrigger value="overview" className="text-xs sm:text-sm">{TAB_LABELS.OVERVIEW}</TabsTrigger>
                <TabsTrigger value="installation" className="text-xs sm:text-sm">{TAB_LABELS.INSTALLATION}</TabsTrigger>
                <TabsTrigger value="usage" className="text-xs sm:text-sm">{TAB_LABELS.USAGE}</TabsTrigger>
                <TabsTrigger value="integration" className="text-xs sm:text-sm">{TAB_LABELS.INTEGRATION}</TabsTrigger>
                <TabsTrigger value="api" className="text-xs sm:text-sm">{TAB_LABELS.API}</TabsTrigger>
                <TabsTrigger value="ui-components" className="text-xs sm:text-sm">UI</TabsTrigger>
              </TabsList>
            </div>

            {/* Overview Tab */}
            <TabsContent value="overview" className="w-full max-w-full overflow-x-hidden">
              <OverviewTab />
            </TabsContent>

            {/* Installation Tab */}
            <TabsContent value="installation" className="w-full max-w-full overflow-x-hidden">
              <InstallationTab copied={copied} onCopy={handleCopy} />
            </TabsContent>

            {/* Usage Guide Tab */}
            <TabsContent value="usage" className="space-y-6 w-full max-w-full overflow-x-hidden">
              <Card>
                <CardHeader>
                  <CardTitle className="text-gradient-primary">Usage Guide</CardTitle>
                  <CardDescription>How to use the Lead Source Manager component</CardDescription>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="create">
                      <AccordionTrigger>Creating a New Lead Source</AccordionTrigger>
                      <AccordionContent className="space-y-4">
                        <ol className="list-decimal list-inside space-y-2">
                          <li>Click the <strong>&quot;Add Source&quot;</strong> button in the top right corner of the card.</li>
                          <li>In the dialog that appears, fill in:
                            <ul className="list-disc list-inside ml-6 mt-1">
                              <li><strong>Source Name</strong>: A unique identifier for your lead source</li>
                              <li><strong>Source Type</strong>: The category or platform (e.g., &quot;Website&quot;, &quot;Facebook&quot;)</li>
                              <li><strong>Description</strong> (optional): Additional information about this source</li>
                            </ul>
                          </li>
                          <li>Click <strong>&quot;Add Source&quot;</strong> to create the webhook.</li>
                          <li>A unique webhook URL will be automatically generated.</li>
                        </ol>

                        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md border border-blue-200 dark:border-blue-800">
                          <p className="text-sm font-medium mb-2 text-blue-800 dark:text-blue-200">💡 Tip</p>
                          <p className="text-sm text-blue-700 dark:text-blue-300">Use descriptive names and types to easily identify your lead sources later.</p>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="manage">
                      <AccordionTrigger>Managing Lead Sources</AccordionTrigger>
                      <AccordionContent className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900 dark:text-white">Copying Webhook URLs</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Click the <strong>Copy</strong> icon next to any webhook URL to copy it to your clipboard.</p>

                        <h4 className="text-md font-medium text-gray-900 dark:text-white">Enabling/Disabling Sources</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Toggle the <strong>Switch</strong> in the Status column to enable or disable a lead source.</p>

                        <h4 className="text-md font-medium text-gray-900 dark:text-white">Editing a Source</h4>
                        <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          <li>Click the <strong>Pencil</strong> icon in the Actions column.</li>
                          <li>Modify the source details as needed.</li>
                          <li>Click <strong>&quot;Update Source&quot;</strong> to save changes.</li>
                        </ol>

                        <h4 className="text-md font-medium text-gray-900 dark:text-white">Deleting a Source</h4>
                        <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">
                          <li>Click the <strong>Trash</strong> icon in the Actions column.</li>
                          <li>Confirm deletion in the dialog that appears.</li>
                        </ol>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="mobile">
                      <AccordionTrigger>Mobile Experience</AccordionTrigger>
                      <AccordionContent>
                        <p className="mb-3 text-sm text-gray-600 dark:text-gray-400">On mobile devices, the Lead Source Manager displays a simplified view:</p>
                        <ul className="list-disc list-inside space-y-2 text-sm text-gray-600 dark:text-gray-400">
                          <li>Sources are displayed in a compact list showing only name and type</li>
                          <li>Tap the dropdown icon to expand and view all details for a specific source</li>
                          <li>All actions (copy, edit, delete) are available in the expanded view</li>
                        </ul>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-gradient-accent">Example Lead Sources Table</CardTitle>
                  <CardDescription>How the component displays lead sources</CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Desktop Table */}
                  <div className="hidden md:block overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Webhook</TableHead>
                          <TableHead>Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {EXAMPLE_WEBHOOKS.map((source) => (
                          <TableRow key={source.id}>
                            <TableCell className="font-medium">{source.name}</TableCell>
                            <TableCell>
                              <Badge variant="outline">{source.type}</Badge>
                            </TableCell>
                            <TableCell className="max-w-xs">
                              <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                {source.description}
                              </p>
                            </TableCell>
                            <TableCell className="max-w-xs">
                              <CodeSnippet
                                snippet={source.webhook_url}
                                copyId={`webhook-${source.id}`}
                                copied={copied}
                                onCopy={handleCopy}
                              />
                            </TableCell>
                            <TableCell>
                              <Badge variant={source.status ? "default" : "secondary"}>
                                {source.status ? "Enabled" : "Disabled"}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Mobile Cards */}
                  <div className="md:hidden space-y-4">
                    {EXAMPLE_WEBHOOKS.map((source) => (
                      <div key={source.id} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-gray-100">{source.name}</h4>
                            <Badge variant="outline" className="mt-1">{source.type}</Badge>
                          </div>
                          <Badge variant={source.status ? "default" : "secondary"}>
                            {source.status ? "Enabled" : "Disabled"}
                          </Badge>
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                          {source.description}
                        </p>

                        <div className="space-y-2">
                          <p className="text-xs font-medium text-gray-700 dark:text-gray-300">Webhook URL:</p>
                          <CodeSnippet
                            snippet={source.webhook_url}
                            copyId={`webhook-mobile-${source.id}`}
                            copied={copied}
                            onCopy={handleCopy}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Integration Tab */}
            <TabsContent value="integration" className="space-y-6 w-full max-w-full overflow-x-hidden">
              <Card>
                <CardHeader>
                  <CardTitle className="text-gradient-purple">Integration with External Platforms</CardTitle>
                  <CardDescription>How to use lead source webhooks with other systems</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-white">Webhook Structure</h3>
                    <p className="mb-3 text-sm text-gray-600 dark:text-gray-400">The generated webhook URL follows this format:</p>
                    <CodeBlock
                      code={CODE_EXAMPLES.WEBHOOK_URL}
                      language="text"
                      title="Webhook URL Format"
                      copyId="webhook-url"
                      copied={copied}
                      onCopy={handleCopy}
                    />
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-white">Sending Data to Webhooks</h3>
                    <p className="mb-3 text-sm text-gray-600 dark:text-gray-400">To send leads to your webhook, make a POST request with this JSON structure:</p>
                    <CodeBlock
                      code={CODE_EXAMPLES.WEBHOOK_PAYLOAD}
                      language="json"
                      title="Webhook Payload Example"
                      copyId="webhook-payload"
                      copied={copied}
                      onCopy={handleCopy}
                    />
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-3 text-gray-900 dark:text-white">Supported Integration Platforms</h3>
                    <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">The Lead Source Manager works seamlessly with:</p>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                      {INTEGRATION_PLATFORMS.map((platform, index) => (
                        <Card key={index} className="border border-gray-200 dark:border-gray-800">
                          <CardHeader className="pb-3 p-3 sm:p-4">
                            <CardTitle className="text-sm sm:text-md text-gray-900 dark:text-white">{platform.category}</CardTitle>
                          </CardHeader>
                          <CardContent className="p-3 sm:p-4 pt-0">
                            <ul className="list-disc list-inside space-y-1 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                              {platform.platforms.map((item, itemIndex) => (
                                <li key={itemIndex}>{item}</li>
                              ))}
                            </ul>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="font-semibold mb-2 text-blue-800 dark:text-blue-200">Integration Tips</h4>
                    <ul className="list-disc list-inside space-y-1 text-sm text-blue-700 dark:text-blue-300">
                      <li>Test your webhook URLs before deploying to production</li>
                      <li>Implement proper error handling for webhook failures</li>
                      <li>Use HTTPS for secure data transmission</li>
                      <li>Monitor webhook performance and response times</li>
                      <li>Set up proper authentication if required by your platform</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* API Reference Tab */}
            <TabsContent value="api" className="space-y-6 w-full max-w-full overflow-x-hidden">
              <Card>
                <CardHeader>
                  <CardTitle className="text-gradient-teal">API Reference</CardTitle>
                  <CardDescription>Backend API endpoints used by the Lead Source Manager</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">API Endpoints</h3>

                    {/* Desktop Table */}
                    <div className="hidden md:block overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Endpoint</TableHead>
                            <TableHead>Method</TableHead>
                            <TableHead>Description</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {API_ENDPOINTS.map((endpoint, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <InlineCode>{endpoint.endpoint}</InlineCode>
                              </TableCell>
                              <TableCell>
                                <Badge className={getMethodColor(endpoint.method)}>
                                  {endpoint.method}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-sm text-gray-600 dark:text-gray-400">
                                {endpoint.description}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Mobile Cards */}
                    <div className="md:hidden space-y-4">
                      {API_ENDPOINTS.map((endpoint, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4">
                          <div className="flex items-start justify-between mb-3">
                            <div className="min-w-0 flex-1">
                              <InlineCode className="text-xs break-all">{endpoint.endpoint}</InlineCode>
                            </div>
                            <Badge className={`${getMethodColor(endpoint.method)} ml-2 flex-shrink-0`}>
                              {endpoint.method}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {endpoint.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">Component Props</h3>

                    {/* Desktop Table */}
                    <div className="hidden md:block overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Prop</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Default</TableHead>
                            <TableHead>Description</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {COMPONENT_PROPS.map((prop, index) => (
                            <TableRow key={index}>
                              <TableCell>
                                <InlineCode>{prop.prop}</InlineCode>
                              </TableCell>
                              <TableCell>
                                <InlineCode>{prop.type}</InlineCode>
                              </TableCell>
                              <TableCell>
                                <InlineCode>{prop.default}</InlineCode>
                              </TableCell>
                              <TableCell className="text-sm text-gray-600 dark:text-gray-400">
                                {prop.description}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Mobile Cards */}
                    <div className="md:hidden space-y-4">
                      {COMPONENT_PROPS.map((prop, index) => (
                        <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4">
                          <div className="space-y-3">
                            <div>
                              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Prop:</p>
                              <InlineCode className="text-xs">{prop.prop}</InlineCode>
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Type:</p>
                                <InlineCode className="text-xs">{prop.type}</InlineCode>
                              </div>
                              <div>
                                <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Default:</p>
                                <InlineCode className="text-xs">{prop.default}</InlineCode>
                              </div>
                            </div>
                            <div>
                              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Description:</p>
                              <p className="text-sm text-gray-600 dark:text-gray-400">{prop.description}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4 text-gray-900 dark:text-white">Source Type Definition</h3>
                    <CodeBlock
                      code={CODE_EXAMPLES.TYPE_DEFINITION}
                      language="typescript"
                      title="TypeScript Interface"
                      copyId="type-definition"
                      copied={copied}
                      onCopy={handleCopy}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* UI Components Tab */}
            <TabsContent value="ui-components" className="space-y-6 w-full max-w-full overflow-x-hidden">
              <div className="w-full max-w-full">
                <h2 className="text-xl sm:text-2xl font-bold mb-4 text-gradient-primary-accent">UI Components</h2>
                <p className="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-6">
                  The Lead Source Manager includes a variety of UI components with enhanced styling options.
                  Below are examples of the available components and their color variants.
                </p>

                <section className="mb-8">
                  <h3 className="text-lg sm:text-xl font-bold mb-4 text-gradient-primary">Button Variants</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 mb-6">
                    {BUTTON_VARIANTS.map((button, index) => (
                      <Button key={index} variant={button.variant as any} className="w-full">
                        {button.label}
                      </Button>
                    ))}
                  </div>

                  <h4 className="text-base sm:text-lg font-bold mb-3 text-gradient-accent">Gradient Button Classes</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 sm:gap-4 mb-6">
                    {GRADIENT_BUTTONS.map((button, index) => (
                      <Button key={index} className={`${button.className} w-full`}>
                        {button.label}
                      </Button>
                    ))}
                  </div>
                </section>

                <section className="mb-8">
                  <h3 className="text-lg sm:text-xl font-bold mb-4 text-gradient-purple">Text Gradient Styles</h3>
                  <div className="grid grid-cols-1 gap-4 mb-6">
                    {TEXT_GRADIENTS.map((gradient, index) => (
                      <div key={index} className="border border-gray-200 dark:border-gray-800 rounded-lg p-4">
                        <h4 className={`${gradient.className} text-base sm:text-lg font-bold mb-2`}>
                          {gradient.title}
                        </h4>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          {gradient.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </section>

                <section className="mb-8">
                  <h3 className="text-lg sm:text-xl font-bold mb-4 text-gradient-teal">Loader Components</h3>
                  <Card className="mb-6">
                    <CardHeader className="p-3 sm:p-4 md:p-6">
                      <CardTitle className="text-base sm:text-lg">Loader Variants</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6 p-3 sm:p-4 md:p-6">
                      {LOADER_EXAMPLES.map((loader, index) => (
                        <div key={index} className="flex flex-col items-center p-4 border border-gray-200 dark:border-gray-800 rounded-lg">
                          <Loader size={loader.size} variant={loader.variant as any} />
                          <p className="mt-2 text-xs sm:text-sm text-gray-600 dark:text-gray-400 text-center">
                            {loader.label}
                          </p>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Loading Overlay Example</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <LoadingOverlay isLoading={loading}>
                        <div className="p-6 border rounded-md">
                          <p className="mb-4">This content will be overlaid with a loading indicator when the button below is clicked.</p>
                          <Button onClick={triggerLoading} variant="accent">
                            Simulate Loading
                          </Button>
                        </div>
                      </LoadingOverlay>
                    </CardContent>
                  </Card>
                </section>

                <section>
                  <h3 className="text-xl font-bold mb-4 text-gradient-primary-accent">Card Styles</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card className="feature-card border border-gray-200 dark:border-gray-800">
                      <CardHeader>
                        <CardTitle className="text-gray-900 dark:text-white">Feature Card</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          This card uses the feature-card class for styling.
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="stats-card border border-gray-200 dark:border-gray-800">
                      <CardHeader>
                        <CardTitle className="text-gray-900 dark:text-white">Stats Card</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          This card uses the stats-card class for styling.
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="dashboard-card border border-gray-200 dark:border-gray-800">
                      <CardHeader>
                        <CardTitle className="text-gray-900 dark:text-white">Dashboard Card</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          This card uses the dashboard-card class for styling.
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </section>

                <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h4 className="text-lg font-bold mb-2 flex items-center text-blue-800 dark:text-blue-200">
                    <Palette className="mr-2 h-5 w-5" />
                    UI Enhancement Guide
                  </h4>
                  <p className="mb-3 text-sm text-blue-700 dark:text-blue-300">
                    To use these enhanced UI components in your application:
                  </p>
                  <ol className="list-decimal list-inside space-y-2 text-sm text-blue-700 dark:text-blue-300">
                    <li>Import the component from the appropriate location</li>
                    <li>Apply the desired variant or CSS class</li>
                    <li>For text gradients, use the appropriate text-gradient-* class</li>
                    <li>For custom loaders, import the Loader component and specify size and variant</li>
                    <li>Ensure proper dark/light mode support with theme-aware classes</li>
                  </ol>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentationPage;