import { useRef } from 'react';
import { toast } from 'sonner';
import { WorkspaceMember } from '../types/member';
import { MEMBER_CONSTANTS } from '../constants/member';
import { validateImageFile } from '../utils/member';

interface UseImageUploadProps {
  members: WorkspaceMember[];
  onMemberUpdate: (member: WorkspaceMember) => void;
}

export function useImageUpload({ members, onMemberUpdate }: UseImageUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleProfileImageUpload = async (
    memberId: string,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    try {
      // In a real app, you would upload to your server here
      // For now, using placeholder
      const imageUrl = MEMBER_CONSTANTS.DEFAULTS.PROFILE_IMAGE;

      const updatedMember = members.find((m) => m.id === memberId);
      if (updatedMember) {
        await onMemberUpdate({ ...updatedMember, profileImage: imageUrl });
        toast.success('Profile image updated successfully');
      } else {
        toast.error(MEMBER_CONSTANTS.MESSAGES.MEMBER_NOT_FOUND);
      }
    } catch (error) {
      toast.error('Failed to update profile image');
    } finally {
      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return {
    fileInputRef,
    handleProfileImageUpload,
    triggerFileInput,
  };
}
