import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, checkWorkspacePermission } from "../../../workspace/utils/auth";

/**
 * Get recent leads with revenue for a workspace
 * @route GET /api/leads/recent/[workspaceId]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { workspaceId: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const workspaceId = params.workspaceId;

    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }

    // Check if user exists
    if (!user || !user.id) {
      return NextResponse.json({ error: "User not authenticated" }, { status: 401 });
    }

    // Check if user has permission to view this workspace
    const permission = await checkWorkspacePermission(user.id, workspaceId);
    if (!permission.hasPermission) {
      return NextResponse.json({ error: permission.error }, { status: permission.status });
    }

    try {
      // Get recent leads with revenue and assigned user information
      const { data, error } = await supabase
        .from("leads")
        .select(`
          id,
          name,
          email,
          revenue,
          created_at,
          assigned_to,
          users:assigned_to (
            id,
            email,
            user_metadata
          )
        `)
        .eq("work_id", parseInt(workspaceId))  // Convert string to number
        .gt("revenue", 0)  // Only get leads with revenue
        .order("created_at", { ascending: false })
        .limit(5);  // Get only 5 recent leads

      // Log the data for debugging
      console.log("Recent leads with assigned users:", data);

      if (error) {
        console.error("Error fetching recent leads:", error);
        return NextResponse.json({ error: error.message }, { status: 400 });
      }

      return NextResponse.json({ data }, { status: 200 });
    } catch (error) {
      console.error("Error fetching recent leads:", error);
      return NextResponse.json({
        error: "Failed to fetch recent leads"
      }, { status: 500 });
    }
  } catch (error) {
    console.error("Error fetching recent leads:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
