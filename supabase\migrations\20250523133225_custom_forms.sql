-- Migration for table: custom_forms
CREATE TABLE IF NOT EXISTS "custom_forms" (
  "id" integer PRIMARY KEY NOT NULL,
  "created_at" timestamp with time zone NOT NULL,
  "workspace_id" integer,
  "name" text,
  "description" text,
  "html_content" text,
  "css_content" text,
  "js_content" text,
  "is_active" boolean,
  "updated_at" timestamp with time zone,
  "lead_source_id" uuid,
  "webhook_url" text
);

-- Create index on workspace_id
CREATE INDEX IF NOT EXISTS "custom_forms_workspace_id_idx" ON "custom_forms" ("workspace_id");

-- Add foreign key constraint for workspace_id (commented out, uncomment after verifying)
-- ALTER TABLE "custom_forms" ADD CONSTRAINT "fk_custom_forms_workspace_id"
--   FOREIGN KEY ("workspace_id") REFERENCES "workspaces" (id);

-- Enable Row Level Security
ALTER TABLE "custom_forms" ENABLE ROW LEVEL SECURITY;

