"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { useLoading } from "@/lib/providers/LoadingProvider";
import { Loader2 } from "lucide-react";
import { cva, type VariantProps } from "class-variance-authority";
import { Progress } from "@/components/ui/progress";

// Define variants for the loader using class-variance-authority
const loaderVariants = cva(
  "animate-spin rounded-full border",
  {
    variants: {
      size: {
        xs: "h-3 w-3 border-2",
        sm: "h-4 w-4 border-2",
        md: "h-8 w-8 border-3",
        lg: "h-12 w-12 border-4",
        xl: "h-16 w-16 border-4",
      },
      variant: {
        primary: "border-primary border-t-transparent",
        secondary: "border-secondary border-t-transparent",
        accent: "border-accent border-t-transparent",
        white: "border-white border-t-transparent",
        destructive: "border-destructive border-t-transparent",
      },
    },
    defaultVariants: {
      size: "md",
      variant: "primary",
    },
  }
);

export interface GlobalLoaderProps extends VariantProps<typeof loaderVariants> {
  className?: string;
  fullScreen?: boolean;
  text?: string;
  showProgress?: boolean;
  showSpinner?: boolean;
  spinnerType?: "border" | "lucide";
}

export function GlobalLoader({
  size,
  variant,
  className,
  fullScreen = false,
  text,
  showProgress = false,
  showSpinner = true,
  spinnerType = "border",
}: GlobalLoaderProps) {
  const { isLoading, loadingMessage, loadingProgress } = useLoading();

  // Don't render anything if not loading
  if (!isLoading) return null;

  // Message to display (from props or context)
  const displayMessage = text || loadingMessage;

  // Container classes
  const containerClasses = cn(
    "flex flex-col items-center justify-center gap-3",
    fullScreen 
      ? "fixed inset-0 bg-background/80 backdrop-blur-sm z-50" 
      : "absolute inset-0 bg-background/50 backdrop-blur-[2px] z-10 rounded-lg",
    className
  );

  return (
    <div className={containerClasses} role="status" aria-live="polite">
      {/* Spinner */}
      {showSpinner && (
        spinnerType === "border" ? (
          <div className={cn(loaderVariants({ size, variant }))} />
        ) : (
          <Loader2 
            className={cn(
              "animate-spin",
              {
                "h-3 w-3": size === "xs",
                "h-4 w-4": size === "sm",
                "h-8 w-8": size === "md",
                "h-12 w-12": size === "lg",
                "h-16 w-16": size === "xl",
              },
              {
                "text-primary": variant === "primary",
                "text-secondary": variant === "secondary",
                "text-accent": variant === "accent",
                "text-white": variant === "white",
                "text-destructive": variant === "destructive",
              }
            )}
          />
        )
      )}
      
      {/* Message */}
      {displayMessage && (
        <p className="text-sm font-medium text-foreground/80 text-center max-w-xs">
          {displayMessage}
        </p>
      )}
      
      {/* Progress bar */}
      {showProgress && loadingProgress > 0 && (
        <div className="w-48 mt-2">
          <Progress value={loadingProgress} className="h-1.5" />
          <p className="text-xs text-center mt-1 text-foreground/70">
            {Math.round(loadingProgress)}%
          </p>
        </div>
      )}
    </div>
  );
}

// Component that wraps the entire app to show global loading state
export function GlobalLoadingIndicator() {
  return <GlobalLoader fullScreen size="lg" showProgress spinnerType="lucide" />;
}

// Component for section loading
export function SectionLoader({ text }: { text?: string }) {
  return (
    <div className="w-full h-full min-h-[200px] flex items-center justify-center">
      <GlobalLoader text={text} size="md" spinnerType="lucide" />
    </div>
  );
}

// Component for inline loading (small)
export function InlineLoader({ text }: { text?: string }) {
  return (
    <div className="inline-flex items-center gap-2">
      <GlobalLoader size="xs" spinnerType="lucide" />
      {text && <span className="text-xs">{text}</span>}
    </div>
  );
}

// Component for button loading state
export function ButtonLoader() {
  return <Loader2 className="mr-2 h-4 w-4 animate-spin" />;
}
