/**
 * Copies text to clipboard
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    return false;
  }
};

/**
 * Formats code for display
 */
export const formatCode = (code: string): string => {
  return code.trim();
};

/**
 * Generates a unique ID for copy operations
 */
export const generateCopyId = (prefix: string, suffix?: string): string => {
  return suffix ? `${prefix}-${suffix}` : prefix;
};

/**
 * Simulates loading state
 */
export const simulateLoading = (
  setLoading: (loading: boolean) => void,
  duration: number = 2000
): void => {
  setLoading(true);
  setTimeout(() => setLoading(false), duration);
};

/**
 * Toggles expanded row state
 */
export const toggleExpandedRow = (
  currentExpanded: string | null,
  targetId: string
): string | null => {
  return currentExpanded === targetId ? null : targetId;
};

/**
 * Handles copy state management
 */
export const handleCopyState = (
  setCopied: (id: string | null) => void,
  id: string,
  timeout: number = 2000
): void => {
  setCopied(id);
  setTimeout(() => setCopied(null), timeout);
};

/**
 * Opens external link in new tab
 */
export const openExternalLink = (url: string): void => {
  window.open(url, '_blank');
};

/**
 * Validates if a string is a valid URL
 */
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Truncates text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
};

/**
 * Formats webhook URL for display
 */
export const formatWebhookUrl = (url: string, maxLength: number = 50): string => {
  return truncateText(url, maxLength);
};

/**
 * Gets status color class
 */
export const getStatusColor = (status: boolean): string => {
  return status ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400";
};

/**
 * Gets status text
 */
export const getStatusText = (status: boolean): string => {
  return status ? "Enabled" : "Disabled";
};

/**
 * Formats method badge color
 */
export const getMethodColor = (method: string): string => {
  const colors: Record<string, string> = {
    GET: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    POST: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
    PUT: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
    DELETE: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
    PATCH: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
  };
  
  return colors[method] || "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
};

/**
 * Debounces a function call
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Scrolls to element by ID
 */
export const scrollToElement = (elementId: string): void => {
  const element = document.getElementById(elementId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

/**
 * Checks if element is in viewport
 */
export const isElementInViewport = (element: HTMLElement): boolean => {
  const rect = element.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
};
