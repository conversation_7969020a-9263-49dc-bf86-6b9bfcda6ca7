import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { validateEmail } from "../../utils/validation";
import { applyCors } from "../../utils/middleware";

/**
 * Validate an email address
 * @route POST /api/leads/validate/email
 */
export async function POST(request: NextRequest) {
  // Apply CORS middleware
  await applyCors(request);
  
  try {
    // Parse request body
    const body = await request.json();
    const { email } = body;
    
    if (!email) {
      return NextResponse.json({ 
        success: false, 
        error: "Email is required" 
      }, { status: 400 });
    }
    
    // Get user metadata for API keys if available
    let userMetadata = null;
    const authHeader = request.headers.get("authorization");
    
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.split(" ")[1];
      const { data: { user }, error } = await supabase.auth.getUser(token);
      
      if (!error && user) {
        userMetadata = user.user_metadata;
      }
    }
    
    // Extract API key from user metadata
    const emailApiKey = userMetadata?.reoonEmail || null;
    
    // Validate the email
    const isValid = await validateEmail(email, emailApiKey);
    
    return NextResponse.json({ 
      success: true, 
      isValid 
    }, { status: 200 });
  } catch (error) {
    console.error("Error validating email:", error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : "Unknown error" 
    }, { status: 500 });
  }
}
