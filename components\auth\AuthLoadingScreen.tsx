"use client";

import React from 'react';
import { Loader2 } from 'lucide-react';

export function AuthLoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        {/* Logo */}
        <div className="flex items-center gap-2 mb-4">
          <div className="flex items-center justify-center bg-black dark:bg-white text-white dark:text-black rounded-md font-bold text-sm w-10 h-10">
            IL
          </div>
          <span className="font-bold text-2xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
            INFILABS
          </span>
        </div>

        {/* Loading spinner */}
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
          <span className="text-sm text-muted-foreground">Loading...</span>
        </div>

        {/* Loading bar */}
        <div className="w-64 h-1 bg-gray-200 dark:bg-gray-800 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-primary to-accent rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
  );
}
