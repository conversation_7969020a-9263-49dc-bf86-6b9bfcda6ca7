import { DEFAULT_TEMPLATES, API_ENDPOINTS, LEAD_SOURCE_OPTIONS } from "../constants/forms";
import { FormValues, FormSubmissionData } from "../types/forms";

/**
 * Get default HTML template
 */
export const getDefaultHtml = (): string => {
  return DEFAULT_TEMPLATES.html;
};

/**
 * Get default CSS template
 */
export const getDefaultCss = (): string => {
  return DEFAULT_TEMPLATES.css;
};

/**
 * Get default JavaScript template
 */
export const getDefaultJs = (): string => {
  return DEFAULT_TEMPLATES.js;
};

/**
 * Get form ID for embedding from URL or provided ID
 */
export const getFormIdForEmbed = (searchParams: ReadonlyURLSearchParams | null, id?: string): string | null => {
  // If an ID is provided, use it
  if (id) return id;

  // Otherwise, try to get the ID from the URL
  const urlId = searchParams ? searchParams.get("id") : null;

  // Return null if the ID is "null" or empty
  if (!urlId || urlId === "null") return null;

  return urlId;
};

/**
 * Generate full HTML for form preview
 */
export const generatePreviewHtml = (
  formValues: FormValues,
  formId: string | null,
  baseUrl: string
): string => {
  const { name, html_content, css_content = '', js_content = '' } = formValues;

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${name}</title>
      <style>${css_content}</style>
    </head>
    <body>
      ${html_content}
      <script>
        // Form submission handler
        document.addEventListener('DOMContentLoaded', function() {
          const forms = document.querySelectorAll('form');
          forms.forEach(form => {
            form.addEventListener('submit', async function(e) {
              e.preventDefault();

              // Collect form data
              const formData = new FormData(form);
              const data = {};
              for (const [key, value] of formData.entries()) {
                data[key] = value;
              }

              // Submit to the API
              try {
                const response = await fetch('${baseUrl}${API_ENDPOINTS.FORMS_SUBMIT}', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    formId: '${formId || "preview"}',
                    data,
                  }),
                });

                if (response.ok) {
                  alert('Form submitted successfully!');
                  form.reset();
                } else {
                  alert('Failed to submit form. Please try again.');
                }
              } catch (error) {
                console.error('Error submitting form:', error);
                alert('An error occurred. Please try again later.');
              }
            });
          });
        });

        ${js_content}
      </script>
    </body>
    </html>
  `;
};

/**
 * Create blob URL for form preview
 */
export const createPreviewUrl = (htmlContent: string): string => {
  const blob = new Blob([htmlContent], { type: 'text/html' });
  return URL.createObjectURL(blob);
};

/**
 * Format form data for API submission
 */
export const formatFormDataForSubmission = (
  values: FormValues,
  workspaceId: string | number
): any => {
  return {
    ...values,
    workspace_id: Number(workspaceId),
    lead_source_id: values.lead_source_id === LEAD_SOURCE_OPTIONS.NONE ? null : values.lead_source_id,
  };
};

/**
 * Format form data for update
 */
export const formatFormDataForUpdate = (values: FormValues): any => {
  return {
    ...values,
    lead_source_id: values.lead_source_id === LEAD_SOURCE_OPTIONS.NONE ? null : values.lead_source_id,
  };
};

/**
 * Get default form values
 */
export const getDefaultFormValues = (): FormValues => {
  return {
    name: "",
    description: "",
    html_content: getDefaultHtml(),
    css_content: getDefaultCss(),
    js_content: getDefaultJs(),
    is_active: true,
    lead_source_id: LEAD_SOURCE_OPTIONS.NONE,
  };
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString();
};

/**
 * Get status color class
 */
export const getStatusColor = (isActive: boolean): string => {
  return isActive ? "text-green-500" : "text-red-500";
};

/**
 * Get status text
 */
export const getStatusText = (isActive: boolean): string => {
  return isActive ? "Active" : "Inactive";
};

/**
 * Validate form ID
 */
export const isValidFormId = (id: string | null): boolean => {
  return id !== null && id !== "" && id !== "null";
};

/**
 * Generate embed URL
 */
export const generateEmbedUrl = (formId: string, baseUrl: string): string => {
  return `${baseUrl}/embed/forms/${formId}`;
};

/**
 * Clean up blob URLs to prevent memory leaks
 */
export const cleanupBlobUrl = (url: string): void => {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Check if form has unsaved changes
 */
export const hasUnsavedChanges = (
  currentValues: FormValues,
  originalValues: FormValues
): boolean => {
  return JSON.stringify(currentValues) !== JSON.stringify(originalValues);
};

/**
 * Sanitize HTML content for security
 */
export const sanitizeHtml = (html: string): string => {
  // Basic HTML sanitization - in production, use a proper library like DOMPurify
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');
};
