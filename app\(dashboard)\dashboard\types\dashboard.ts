export interface Workspace {
  id: string;
  name: string;
  role: string;
  industry?: string;
  status?: boolean;
  type?: string;
}

export interface DashboardStat {
  icon: string;
  title: string;
  value: string;
  change: string;
  trend: "up" | "down" | "neutral";
  note?: string;
}

export interface SalesData {
  month: string;
  sales: number;
  leads: number;
}

export interface RecentLead {
  id: number;
  name: string;
  email: string;
  revenue: number;
  created_at: string;
  users?: {
    id: string;
    email: string;
    user_metadata?: {
      name: string;
    };
  };
}

export interface RecentSale {
  id: string;
  name: string;
  email: string;
  amount: string;
  revenue: number;
  assignedTo: string;
  assignedUserId: string;
  date: string;
}

export interface TeamPerformance {
  name: string;
  totalSales: number;
  totalRevenue: number;
}

export interface WorkspaceRevenue {
  totalRevenue: number;
  change?: string;
}

export interface QualifiedCount {
  qualifiedLeadsCount: number;
}

export interface WorkspaceCount {
  arrivedLeadsCount: number;
}

export interface ROCData {
  conversion_rate: number;
  total_leads: number;
  top_source_id?: string;
  monthly_stats: Array<{
    month: string;
    convertedLeads: number;
    totalLeads: number;
  }>;
}

export interface DashboardLoadingStates {
  isWorkspaceLoading: boolean;
  isRevenueLoading: boolean;
  isRocLoading: boolean;
  isQualifiedCountLoading: boolean;
  isCountLoading: boolean;
  isWebhooksLoading: boolean;
  isRecentLeadsLoading: boolean;
}
