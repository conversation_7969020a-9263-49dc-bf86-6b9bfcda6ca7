import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { supabase } from "../../supabaseClient";

// Define tag types for the API with optimized caching strategy
export const leadsApi = createApi({
  reducerPath: "/api/leads/",
  baseQuery: fetchBaseQuery({
    baseUrl: "/api/leads",
    prepareHeaders: async (headers) => {
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (session?.access_token) {
        headers.set("authorization", `Bearer ${session.access_token}`);
      }
      return headers;
    },
  }),
  tagTypes: [
    'Lead',
    'LeadNotification',
    'LeadByWorkspace',
    'LeadByUser',
    'LeadNotes'
  ],
  // Use a more selective caching strategy for leads data
  // This helps reduce memory usage while maintaining performance
  keepUnusedDataFor: 120, // 2 minutes - balance between performance and memory usage
  refetchOnReconnect: true,
  refetchOnMountOrArgChange: 300, // Refetch after 5 minutes if component remounts
  endpoints: () => ({}),
});
