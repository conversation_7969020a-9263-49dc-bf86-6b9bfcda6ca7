import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, notifyLeadChange } from "../../utils";

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const id = params.id;

    if (!id) {
      return NextResponse.json({ error: "Lead ID is required" }, { status: 400 });
    }

    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");

    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }

    // Parse request body
    const body = await request.json();

    if (!body) {
      return NextResponse.json({ error: "Update data is required" }, { status: 400 });
    }

    // Get current assignment for notification
    const { data: currentLead, error: fetchError } = await supabase
      .from("leads")
      .select("name, assign_to")
      .eq("id", id)
      .single();

    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 400 });
    }

    // Update the lead assignment - handle both ID-based and legacy assignments
    let updateData: any = {};

    if (body.assigned_to_id !== undefined) {
      // New ID-based assignment
      updateData.assigned_to_id = body.assigned_to_id;
      // Also update legacy field for backward compatibility
      if (body.assigned_to_id) {
        // If assigning to someone, we'll need to get member details for legacy field
        const { data: memberData } = await supabase
          .from("workspace_members")
          .select("name, role")
          .eq("id", body.assigned_to_id)
          .single();

        if (memberData) {
          updateData.assign_to = JSON.stringify({
            name: memberData.name,
            role: memberData.role
          });
        }
      } else {
        // Unassigning
        updateData.assign_to = "Not Assigned";
      }
    } else {
      // Legacy assignment (fallback)
      updateData.assign_to = body;
    }

    const { data, error } = await supabase
      .from("leads")
      .update(updateData)
      .eq("id", id)
      .select();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Create notification
    await notifyLeadChange(
      id,
      "assigned",
      user.id,
      workspaceId,
      {
        lead_id: id,
        lead_name: currentLead.name,
        previous_assignee: currentLead.assign_to,
        new_assignee: body,
        assigned_by: user.id
      }
    );

    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error assigning lead:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
