import { useState } from "react";
import { Lead } from "../types/leads";

interface UseLeadSelectionProps {
  leads: Lead[];
}

export const useLeadSelection = ({ leads }: UseLeadSelectionProps) => {
  const [selectedLeads, setSelectedLeads] = useState<number[]>([]);

  // Toggle lead selection
  const toggleLeadSelection = (leadId: number) => {
    setSelectedLeads((prev) =>
      prev.includes(leadId)
        ? prev.filter((id) => id !== leadId)
        : [...prev, leadId]
    );
  };

  // Deselect all leads
  const deselectAll = () => {
    setSelectedLeads([]);
  };

  // Select all leads on current page
  const toggleSelectAllOnPage = () => {
    const currentPageLeadIds = leads.map((lead) => lead.id);
    const allSelected = currentPageLeadIds.every((id) =>
      selectedLeads.includes(id)
    );

    setSelectedLeads((prev) =>
      allSelected
        ? prev.filter((id) => !currentPageLeadIds.includes(id))
        : Array.from(new Set([...prev, ...currentPageLeadIds]))
    );
  };

  // Check if all leads on current page are selected
  const isAllSelected = leads.length > 0 && leads.every((lead) =>
    selectedLeads.includes(lead.id)
  );

  return {
    selectedLeads,
    toggleLeadSelection,
    deselectAll,
    toggleSelectAllOnPage,
    isAllSelected,
    setSelectedLeads,
  };
};
