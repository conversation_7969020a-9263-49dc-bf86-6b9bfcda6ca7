import { NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { PlanTier, SUBSCRIPTION_PLANS } from '@/lib/types/subscription';
import crypto from 'crypto';

// Initialize Razorpay
const keyId = process.env.RAZORPAY_KEY_ID;
const keySecret = process.env.RAZORPAY_KEY_SECRET;

/**
 * API route for creating a Razorpay order
 *
 * Following industry best practices:
 * - Input validation
 * - Proper error handling
 * - Detailed logging
 * - Consistent error responses
 * - Security considerations
 */
export async function POST(req: Request) {
  console.log('Received request to create Razorpay order');

  // Request validation
  if (req.method !== 'POST') {
    return NextResponse.json(
      { error: 'Method not allowed' },
      { status: 405 }
    );
  }

  try {
    // Parse and validate request body with error handling
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (parseError) {
      console.error('Error parsing request body:', parseError);
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    const { planId } = requestBody;

    // Validate required fields
    if (!planId) {
      console.error('Missing required field: planId');
      return NextResponse.json(
        { error: 'Missing required field: planId' },
        { status: 400 }
      );
    }

    // Validate the plan
    if (!Object.keys(SUBSCRIPTION_PLANS).includes(planId)) {
      console.error(`Invalid plan ID: ${planId}`);
      return NextResponse.json(
        { error: 'Invalid plan selected' },
        { status: 400 }
      );
    }

    // Check if Razorpay credentials are configured
    if (!keyId || !keySecret) {
      console.error('Razorpay credentials not configured');
      return NextResponse.json(
        { error: 'Payment gateway not configured' },
        { status: 500 }
      );
    }

    // Get the authenticated user with proper error handling
    let supabase;
    let session;

    try {
      supabase = createRouteHandlerClient({ cookies });
      const sessionResponse = await supabase.auth.getSession();
      session = sessionResponse.data.session;
    } catch (authError) {
      console.error('Error getting user session:', authError);
      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 500 }
      );
    }

    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the plan details
    const plan = SUBSCRIPTION_PLANS[planId as PlanTier];
    console.log(`Creating order for plan: ${plan.name} (${planId})`);

    // Generate a unique receipt ID with better entropy
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 10);
    const receipt = `order_${timestamp}_${randomString}`;

    // Create a Razorpay order with proper error handling
    let response;
    try {
      response = await fetch('https://api.razorpay.com/v1/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${Buffer.from(`${keyId}:${keySecret}`).toString('base64')}`,
        },
        body: JSON.stringify({
          amount: plan.price * 100, // Amount in smallest currency unit (paise for INR)
          currency: 'INR',
          receipt,
          notes: {
            planId,
            userId: session.user.id,
            planName: plan.name,
            createdAt: new Date().toISOString(),
          },
        }),
      });
    } catch (fetchError) {
      console.error('Network error creating Razorpay order:', fetchError);
      return NextResponse.json(
        { error: 'Network error. Please try again later.' },
        { status: 503 }
      );
    }

    // Handle HTTP errors
    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Unknown error');
      console.error('Failed to create Razorpay order:', {
        status: response.status,
        statusText: response.statusText,
        errorText
      });
      return NextResponse.json(
        { error: `Payment gateway error (${response.status}): ${response.statusText}` },
        { status: 502 }
      );
    }

    // Parse the response with error handling
    let order;
    try {
      order = await response.json();
    } catch (jsonError) {
      console.error('Error parsing Razorpay order response:', jsonError);
      return NextResponse.json(
        { error: 'Invalid response from payment gateway' },
        { status: 502 }
      );
    }

    if (order.error) {
      console.error('Razorpay API error:', order.error);
      return NextResponse.json(
        { error: order.error.description || 'Payment gateway error' },
        { status: 502 }
      );
    }

    // Validate order data
    if (!order.id || !order.amount || !order.currency) {
      console.error('Incomplete order data from Razorpay:', order);
      return NextResponse.json(
        { error: 'Incomplete data received from payment gateway' },
        { status: 502 }
      );
    }

    // Save order information to user metadata with error handling
    try {
      await supabase.auth.updateUser({
        data: {
          razorpay_order: {
            id: order.id,
            planId,
            status: order.status,
            created_at: new Date().toISOString(),
            amount: order.amount,
            currency: order.currency,
          },
        },
      });
    } catch (updateError) {
      console.error('Error updating user metadata:', updateError);
      // Continue anyway since the order was created successfully
      console.log('Continuing despite metadata update error');
    }

    console.log(`Razorpay order created successfully: ${order.id}`);

    // Return only necessary data (security best practice)
    return NextResponse.json({
      id: order.id,
      amount: order.amount,
      currency: order.currency,
      keyId: keyId,
    });
  } catch (error: any) {
    console.error('Razorpay order creation error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred creating the payment order' },
      { status: 500 }
    );
  }
}

// Verify Razorpay payment
export async function PUT(req: Request) {
  try {
    const { orderId, paymentId, signature } = await req.json();

    // Get the authenticated user
    const supabase = createRouteHandlerClient({ cookies });
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify the payment signature
    const generatedSignature = crypto
      .createHmac('sha256', keySecret!)
      .update(`${orderId}|${paymentId}`)
      .digest('hex');

    if (generatedSignature !== signature) {
      return NextResponse.json(
        { error: 'Invalid payment signature' },
        { status: 400 }
      );
    }

    // Get the order details from user metadata
    const razorpayOrder = session.user.user_metadata?.razorpay_order;

    if (!razorpayOrder || razorpayOrder.id !== orderId) {
      return NextResponse.json(
        { error: 'Order not found or mismatch' },
        { status: 400 }
      );
    }

    // Get the plan details
    const planId = razorpayOrder.planId as PlanTier;
    const plan = SUBSCRIPTION_PLANS[planId];

    if (!plan) {
      return NextResponse.json(
        { error: 'Invalid plan' },
        { status: 400 }
      );
    }

    // Calculate subscription end date (30 days from now)
    const currentPeriodEnd = new Date();
    currentPeriodEnd.setDate(currentPeriodEnd.getDate() + 30);

    // Update user subscription in metadata
    await supabase.auth.updateUser({
      data: {
        subscription: {
          planId,
          status: 'active',
          currentPeriodEnd: currentPeriodEnd.toISOString(),
          cancelAtPeriodEnd: false,
          paymentMethod: 'razorpay',
          paymentId,
        },
        razorpay_order: {
          ...razorpayOrder,
          status: 'paid',
          paymentId,
          signature,
          updated_at: new Date().toISOString(),
        },
      },
    });

    return NextResponse.json({
      success: true,
      subscription: {
        planId,
        status: 'active',
        currentPeriodEnd: currentPeriodEnd.toISOString(),
      },
    });
  } catch (error: any) {
    console.error('Razorpay payment verification error:', error);
    return NextResponse.json(
      { error: error.message || 'An error occurred verifying the payment' },
      { status: 500 }
    );
  }
}
