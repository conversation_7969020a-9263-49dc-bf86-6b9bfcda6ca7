import { DefaultTemplates } from "../types/forms";

export const TAB_VALUES = {
  FORMS: "forms",
  EDITOR: "editor",
  PREVIEW: "preview"
} as const;

export const EDITOR_TAB_VALUES = {
  HTML: "html",
  CSS: "css",
  JS: "js"
} as const;

export const FORM_MESSAGES = {
  SUCCESS: {
    CREATED: "Form created successfully",
    UPDATED: "Form updated successfully",
    DELETED: "Form deleted successfully"
  },
  ERROR: {
    SAVE_FAILED: "Failed to save form",
    DELETE_FAILED: "Failed to delete form",
    LOAD_FAILED: "Failed to load form details",
    SUBMIT_FAILED: "Failed to submit form. Please try again.",
    NETWORK_ERROR: "An error occurred. Please try again later."
  },
  CONFIRM: {
    DELETE: "Are you sure you want to delete this form? This action cannot be undone."
  },
  REALTIME: {
    NEW_LEAD: "New lead received!"
  }
} as const;

export const FORM_PLACEHOLDERS = {
  NAME: "Contact Form",
  DESCRIPTION: "A brief description of this form's purpose",
  LEAD_SOURCE: "Select a lead source"
} as const;

export const FORM_LABELS = {
  NAME: "Form Name",
  DESCRIPTION: "Description",
  LEAD_SOURCE: "Lead Source",
  ACTIVE: "Active",
  HTML: "HTML",
  CSS: "CSS",
  JAVASCRIPT: "JavaScript"
} as const;

export const FORM_DESCRIPTIONS = {
  LEAD_SOURCE: "Connect this form to a lead source to track submissions",
  ACTIVE: "When active, this form can receive submissions"
} as const;

export const BUTTON_LABELS = {
  NEW_FORM: "New Form",
  CREATE_FIRST: "Create Your First Form",
  EDIT: "Edit",
  SAVE: "Save Form",
  CANCEL: "Cancel",
  PREVIEW: "Preview",
  GENERATE_PREVIEW: "Generate Preview",
  OPEN_PREVIEW: "Open Preview in New Tab",
  GET_EMBED_CODE: "Get Embed Code",
  DELETE: "Delete"
} as const;

export const EMPTY_STATE_MESSAGES = {
  NO_FORMS: "No forms created yet",
  NO_FORMS_DESCRIPTION: "Create a form to collect leads from your website. Forms can be embedded directly into your website using our embed code.",
  NO_WORKSPACE: "Please select a workspace to continue"
} as const;

export const DEFAULT_TEMPLATES: DefaultTemplates = {
  html: `<form class="contact-form">
  <h2>Contact Us</h2>
  <div class="form-group">
    <label for="name">Name</label>
    <input type="text" id="name" name="name" required>
  </div>
  <div class="form-group">
    <label for="email">Email</label>
    <input type="email" id="email" name="email" required>
  </div>
  <div class="form-group">
    <label for="phone">Phone</label>
    <input type="tel" id="phone" name="phone">
  </div>
  <div class="form-group">
    <label for="company">Company</label>
    <input type="text" id="company" name="company">
  </div>
  <div class="form-group">
    <label for="message">Message</label>
    <textarea id="message" name="message" rows="4"></textarea>
  </div>
  <button type="submit">Submit</button>
</form>`,

  css: `* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

body {
  background-color: #f8f9fa;
  padding: 20px;
}

.contact-form {
  max-width: 600px;
  margin: 0 auto;
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

input, textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

textarea {
  resize: vertical;
}

button {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  width: 100%;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #3a5ad9;
}`,

  js: `// Add custom JavaScript here
// This will be executed after the form submission handler

// Example: Form validation
document.addEventListener('DOMContentLoaded', function() {
  const form = document.querySelector('.contact-form');
  const emailInput = document.querySelector('#email');

  emailInput.addEventListener('blur', function() {
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    if (!emailRegex.test(emailInput.value) && emailInput.value !== '') {
      emailInput.style.borderColor = 'red';
      const errorMsg = document.createElement('div');
      errorMsg.className = 'error-message';
      errorMsg.textContent = 'Please enter a valid email address';
      errorMsg.style.color = 'red';
      errorMsg.style.fontSize = '12px';
      errorMsg.style.marginTop = '5px';

      // Remove any existing error message
      const existingError = emailInput.parentNode.querySelector('.error-message');
      if (existingError) {
        existingError.remove();
      }

      emailInput.parentNode.appendChild(errorMsg);
    } else {
      emailInput.style.borderColor = '#ddd';
      const existingError = emailInput.parentNode.querySelector('.error-message');
      if (existingError) {
        existingError.remove();
      }
    }
  });
});`
} as const;

export const FORM_VALIDATION_RULES = {
  NAME: {
    MIN_LENGTH: 1,
    MESSAGE: "Form name is required"
  },
  HTML_CONTENT: {
    MIN_LENGTH: 1,
    MESSAGE: "HTML content is required"
  }
} as const;

export const EDITOR_CONFIG = {
  MIN_HEIGHT: "400px",
  FONT_FAMILY: "monospace"
} as const;

export const PREVIEW_CONFIG = {
  HEIGHT: "600px",
  TITLE: "Form Preview"
} as const;

export const API_ENDPOINTS = {
  FORMS_SUBMIT: "/api/forms/submit"
} as const;

export const LEAD_SOURCE_OPTIONS = {
  NONE: "none"
} as const;
