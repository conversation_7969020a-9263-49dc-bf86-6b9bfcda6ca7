import { PlatformIntegration, IntegrationStatus, FilterType } from "../types/integration";
import { STATUS_COLORS, STATUS_DISPLAY_NAMES } from "../constants/integration";

/**
 * Get status badge color class
 */
export const getStatusBadgeColor = (status: IntegrationStatus): string => {
  return STATUS_COLORS[status] || "bg-blue-500 hover:bg-blue-600";
};

/**
 * Get status display name
 */
export const getStatusDisplayName = (status: IntegrationStatus): string => {
  return STATUS_DISPLAY_NAMES[status] || status;
};

/**
 * Filter platforms by search term
 */
export const filterPlatformsBySearch = (
  platforms: PlatformIntegration[],
  searchTerm: string
): PlatformIntegration[] => {
  if (!searchTerm.trim()) {
    return platforms;
  }

  const lowercaseSearch = searchTerm.toLowerCase();
  
  return platforms.filter(platform => 
    platform.name.toLowerCase().includes(lowercaseSearch) ||
    platform.category.toLowerCase().includes(lowercaseSearch) ||
    platform.description.toLowerCase().includes(lowercaseSearch)
  );
};

/**
 * Filter platforms by status
 */
export const filterPlatformsByStatus = (
  platforms: PlatformIntegration[],
  status: FilterType
): PlatformIntegration[] => {
  if (status === "all") {
    return platforms;
  }

  return platforms.filter(platform => platform.status === status);
};

/**
 * Apply all filters to platforms
 */
export const applyFilters = (
  platforms: PlatformIntegration[],
  searchTerm: string,
  activeFilter: FilterType
): PlatformIntegration[] => {
  let results = platforms;
  
  // Apply search filter
  results = filterPlatformsBySearch(results, searchTerm);
  
  // Apply status filter
  results = filterPlatformsByStatus(results, activeFilter);
  
  return results;
};

/**
 * Open documentation URL in new tab
 */
export const openDocumentation = (url: string): void => {
  window.open(url, "_blank", "noopener,noreferrer");
};

/**
 * Check if platform has documentation
 */
export const hasDocumentation = (platform: PlatformIntegration): boolean => {
  return Boolean(platform.documentationUrl);
};

/**
 * Get platform count by status
 */
export const getPlatformCountByStatus = (
  platforms: PlatformIntegration[],
  status: IntegrationStatus
): number => {
  return platforms.filter(platform => platform.status === status).length;
};

/**
 * Get total platform count
 */
export const getTotalPlatformCount = (platforms: PlatformIntegration[]): number => {
  return platforms.length;
};

/**
 * Sort platforms by name
 */
export const sortPlatformsByName = (platforms: PlatformIntegration[]): PlatformIntegration[] => {
  return [...platforms].sort((a, b) => a.name.localeCompare(b.name));
};

/**
 * Sort platforms by category
 */
export const sortPlatformsByCategory = (platforms: PlatformIntegration[]): PlatformIntegration[] => {
  return [...platforms].sort((a, b) => a.category.localeCompare(b.category));
};

/**
 * Sort platforms by status priority (active > beta > coming-soon)
 */
export const sortPlatformsByStatus = (platforms: PlatformIntegration[]): PlatformIntegration[] => {
  const statusPriority = { active: 1, beta: 2, "coming-soon": 3 };
  
  return [...platforms].sort((a, b) => {
    return statusPriority[a.status] - statusPriority[b.status];
  });
};

/**
 * Debounce function for search optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Validate platform data
 */
export const validatePlatform = (platform: any): platform is PlatformIntegration => {
  return (
    typeof platform === 'object' &&
    platform !== null &&
    typeof platform.id === 'string' &&
    typeof platform.name === 'string' &&
    typeof platform.category === 'string' &&
    typeof platform.description === 'string' &&
    ['active', 'beta', 'coming-soon'].includes(platform.status)
  );
};

/**
 * Generate platform analytics data
 */
export const generatePlatformAnalytics = (platforms: PlatformIntegration[]) => {
  const total = getTotalPlatformCount(platforms);
  const active = getPlatformCountByStatus(platforms, "active");
  const beta = getPlatformCountByStatus(platforms, "beta");
  const comingSoon = getPlatformCountByStatus(platforms, "coming-soon");
  
  return {
    total,
    active,
    beta,
    comingSoon,
    activePercentage: total > 0 ? Math.round((active / total) * 100) : 0,
    betaPercentage: total > 0 ? Math.round((beta / total) * 100) : 0,
    comingSoonPercentage: total > 0 ? Math.round((comingSoon / total) * 100) : 0,
  };
};
