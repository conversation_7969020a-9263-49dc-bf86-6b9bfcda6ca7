import { createClient, SupabaseClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY as string;

// Track client creation time for potential refresh
let clientCreationTime = Date.now();
const CLIENT_REFRESH_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

// Create the Supabase client with optimized settings
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: { 'x-my-custom-header': 'my-app-name' },
  },
  realtime: {
    timeout: 20000, // Further reduced timeout
    params: {
      eventsPerSecond: 3, // Further reduced to lower resource usage
      heartbeatIntervalMs: 15000 // Reduced heartbeat interval
    }
  }
});

// Store active channel subscriptions with timestamps for monitoring
interface ChannelSubscription {
  id: string;
  unsubscribe: () => void;
  createdAt: number; // timestamp
  lastActivity?: number; // timestamp of last activity
}

const activeChannels: ChannelSubscription[] = [];

// Helper function to add a channel subscription with monitoring
export const addChannelSubscription = (channelId: string, unsubscribe: () => void) => {
  // Check for duplicate subscriptions and clean them up
  const existingIndex = activeChannels.findIndex(channel => channel.id === channelId);
  if (existingIndex >= 0) {
    console.warn(`Duplicate subscription detected for channel ${channelId}, cleaning up old one`);
    try {
      activeChannels[existingIndex].unsubscribe();
    } catch (e) {
      console.error(`Error unsubscribing from duplicate channel ${channelId}:`, e);
    }
    activeChannels.splice(existingIndex, 1);
  }

  // Add the new subscription with timestamp
  activeChannels.push({
    id: channelId,
    unsubscribe,
    createdAt: Date.now()
  });

  // Log active subscriptions count for monitoring
  if (activeChannels.length > 10) {
    console.warn(`High number of active Supabase subscriptions: ${activeChannels.length}`);
  }
};

// Update channel activity timestamp
export const updateChannelActivity = (channelId: string) => {
  const channel = activeChannels.find(ch => ch.id === channelId);
  if (channel) {
    channel.lastActivity = Date.now();
  }
};

// Helper function to explicitly close realtime subscriptions when needed
export const closeRealtimeSubscriptions = () => {
  try {
    // Unsubscribe from all active channels
    activeChannels.forEach(channel => {
      try {
        channel.unsubscribe();
      } catch (e) {
        console.error(`Error unsubscribing from channel ${channel.id}:`, e);
      }
    });

    // Clear the array
    const count = activeChannels.length;
    activeChannels.length = 0;
    console.log(`Closed ${count} realtime subscriptions`);
    return true;
  } catch (error) {
    console.error('Error closing realtime subscriptions:', error);
    return false;
  }
};

// Close inactive subscriptions to prevent memory leaks
export const cleanupInactiveSubscriptions = (maxAgeMs: number = 30 * 60 * 1000) => { // Default 30 minutes
  const now = Date.now();
  const initialCount = activeChannels.length;

  // Find and close inactive subscriptions
  const toRemove: number[] = [];
  activeChannels.forEach((channel, index) => {
    const lastActive = channel.lastActivity || channel.createdAt;
    if (now - lastActive > maxAgeMs) {
      try {
        channel.unsubscribe();
        toRemove.push(index);
      } catch (e) {
        console.error(`Error unsubscribing from inactive channel ${channel.id}:`, e);
      }
    }
  });

  // Remove closed subscriptions from the array (in reverse order to maintain indices)
  for (let i = toRemove.length - 1; i >= 0; i--) {
    activeChannels.splice(toRemove[i], 1);
  }

  if (toRemove.length > 0) {
    console.log(`Cleaned up ${toRemove.length} inactive subscriptions out of ${initialCount}`);
  }

  return toRemove.length;
};

// Get a refreshed client if needed
export const getRefreshedClient = (): SupabaseClient => {
  const now = Date.now();

  // Check if we need to refresh the client
  if (now - clientCreationTime > CLIENT_REFRESH_INTERVAL) {
    // Close existing subscriptions
    closeRealtimeSubscriptions();

    // Update creation time
    clientCreationTime = now;

    console.log('Refreshed Supabase client after 24 hours');
  }

  return supabase;
};
