-- Data migration script to populate new ID-based fields from existing legacy data
-- This script should be run after the schema migration

-- Function to migrate status data
DO $$
DECLARE
    lead_record RECORD;
    status_record RECORD;
    status_name TEXT;
    status_color TEXT;
BEGIN
    -- Loop through all leads that have legacy status data but no status_id
    FOR lead_record IN 
        SELECT id, status 
        FROM leads 
        WHERE status IS NOT NULL 
        AND status_id IS NULL
        AND status::text != 'null'
    LOOP
        BEGIN
            -- Extract name and color from JSON status
            IF lead_record.status IS NOT NULL THEN
                -- Handle both string and JSON status formats
                IF jsonb_typeof(lead_record.status) = 'object' THEN
                    status_name := lead_record.status->>'name';
                    status_color := lead_record.status->>'color';
                ELSE
                    status_name := lead_record.status#>>'{}'::text[];
                    status_color := '#ea1212'; -- default color
                END IF;
                
                -- Find matching status by name and color
                SELECT id INTO status_record
                FROM status 
                WHERE name = status_name 
                AND (color = status_color OR status_color IS NULL)
                LIMIT 1;
                
                -- If found, update the lead with status_id
                IF status_record IS NOT NULL THEN
                    UPDATE leads 
                    SET status_id = status_record.id 
                    WHERE id = lead_record.id;
                    
                    RAISE NOTICE 'Updated lead % with status_id %', lead_record.id, status_record.id;
                END IF;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error processing lead %: %', lead_record.id, SQLERRM;
                CONTINUE;
        END;
    END LOOP;
END $$;

-- Function to migrate assignment data
DO $$
DECLARE
    lead_record RECORD;
    member_record RECORD;
    assign_name TEXT;
    assign_email TEXT;
BEGIN
    -- Loop through all leads that have legacy assign_to data but no assigned_to_id
    FOR lead_record IN 
        SELECT id, assign_to 
        FROM leads 
        WHERE assign_to IS NOT NULL 
        AND assigned_to_id IS NULL
        AND assign_to != 'Not Assigned'
        AND assign_to != 'Unassigned'
    LOOP
        BEGIN
            -- Extract name from assign_to field
            IF lead_record.assign_to IS NOT NULL THEN
                -- Handle both string and JSON assignment formats
                BEGIN
                    -- Try to parse as JSON first
                    assign_name := (lead_record.assign_to::jsonb)->>'name';
                EXCEPTION
                    WHEN OTHERS THEN
                        -- If not JSON, treat as plain text
                        assign_name := lead_record.assign_to;
                END;
                
                -- Find matching workspace member by name
                SELECT id INTO member_record
                FROM workspace_members 
                WHERE name = assign_name
                AND name IS NOT NULL
                AND name != 'null'
                LIMIT 1;
                
                -- If found, update the lead with assigned_to_id
                IF member_record IS NOT NULL THEN
                    UPDATE leads 
                    SET assigned_to_id = member_record.id 
                    WHERE id = lead_record.id;
                    
                    RAISE NOTICE 'Updated lead % with assigned_to_id %', lead_record.id, member_record.id;
                END IF;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Error processing lead assignment %: %', lead_record.id, SQLERRM;
                CONTINUE;
        END;
    END LOOP;
END $$;

-- Create a summary of the migration
DO $$
DECLARE
    total_leads INTEGER;
    migrated_status INTEGER;
    migrated_assignments INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_leads FROM leads;
    SELECT COUNT(*) INTO migrated_status FROM leads WHERE status_id IS NOT NULL;
    SELECT COUNT(*) INTO migrated_assignments FROM leads WHERE assigned_to_id IS NOT NULL;
    
    RAISE NOTICE 'Migration Summary:';
    RAISE NOTICE 'Total leads: %', total_leads;
    RAISE NOTICE 'Leads with migrated status: %', migrated_status;
    RAISE NOTICE 'Leads with migrated assignments: %', migrated_assignments;
END $$;
