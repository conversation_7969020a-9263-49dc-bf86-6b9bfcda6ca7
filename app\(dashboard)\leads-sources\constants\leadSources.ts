import { SourceType } from "../types/leadSources";

export const SOURCE_TYPES: SourceType[] = [
  { id: "website", label: "Website" },
  { id: "crm", label: "CRM" },
  { id: "marketing", label: "Marketing" },
  { id: "social", label: "Social Media" },
  { id: "email", label: "Email Campaign" },
  { id: "referral", label: "Referral" },
  { id: "other", label: "Other" },
] as const;

export const FORM_DEFAULT_VALUES = {
  name: "",
  type: "",
  description: "",
} as const;

export const TABLE_HEADERS = {
  DESKTOP: [
    "Name",
    "Type", 
    "Description",
    "Count",
    "Processing Rate",
    "Qualification Rate",
    "Webhook",
    "Status",
    "Actions"
  ],
  MOBILE: ["Name", "Type", "Actions"]
} as const;

export const EMPTY_STATE_MESSAGES = {
  NO_SOURCES: {
    title: "No Lead Sources Found",
    description: "Create your first lead source to start collecting leads from various channels.",
    action: "Add Lead Source"
  },
  NO_WORKSPACE: {
    title: "No Workspace Selected",
    description: "Please select a workspace to manage lead sources."
  }
} as const;

export const SUCCESS_MESSAGES = {
  CREATED: "Lead source created successfully",
  UPDATED: "Lead source updated successfully", 
  DELETED: "Lead source deleted successfully",
  STATUS_UPDATED: "Webhook status updated successfully",
  WEBHOOK_COPIED: "Webhook URL copied to clipboard"
} as const;

export const ERROR_MESSAGES = {
  NO_WORKSPACE: "No workspace selected. Please select a workspace",
  CREATE_FAILED: "Failed to create lead source",
  UPDATE_FAILED: "Failed to update lead source", 
  DELETE_FAILED: "Failed to delete lead source",
  STATUS_UPDATE_FAILED: "Failed to update webhook status",
  DUPLICATE_NAME: "A lead source with this name already exists",
  NO_PERMISSION: "You don't have permission to perform this action"
} as const;

export const DIALOG_TITLES = {
  CREATE: "Add New Lead Source",
  EDIT: "Edit Lead Source",
  DELETE: "Confirm Deletion"
} as const;

export const BUTTON_LABELS = {
  ADD_SOURCE: "Add Source",
  UPDATE_SOURCE: "Update Source", 
  DELETE_SOURCE: "Delete Source",
  CANCEL: "Cancel",
  ADDING: "Adding...",
  UPDATING: "Updating...",
  DELETING: "Deleting..."
} as const;
