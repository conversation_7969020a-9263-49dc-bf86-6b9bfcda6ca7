import { supabase } from '@/lib/supabaseServer';

export interface AuditLogData {
  workspaceId: number;
  userId?: string;
  email?: string;
  action: 'invited' | 'accepted' | 'declined' | 'removed' | 'role_changed' | 'reactivated';
  oldRole?: string;
  newRole?: string;
  performedBy: string | null;
  metadata?: Record<string, any>;
}

/**
 * Create an audit log entry for workspace member actions
 */
export async function createAuditLog(data: AuditLogData): Promise<void> {
  try {
    const { error } = await supabase
      .from('workspace_member_audit')
      .insert({
        workspace_id: data.workspaceId,
        user_id: data.userId || null,
        email: data.email || null,
        action: data.action,
        old_role: data.oldRole || null,
        new_role: data.newRole || null,
        performed_by: data.performedBy,
        metadata: data.metadata || null
      });
    
    if (error) {
      console.error('Error creating audit log:', error);
      // Don't throw error as audit logging shouldn't break the main flow
    }
  } catch (error) {
    console.error('Unexpected error creating audit log:', error);
  }
}

/**
 * Get audit logs for a workspace (admin only)
 */
export async function getWorkspaceAuditLogs(
  workspaceId: number, 
  limit: number = 50, 
  offset: number = 0
) {
  try {
    const { data, error, count } = await supabase
      .from('workspace_member_audit')
      .select('*', { count: 'exact' })
      .eq('workspace_id', workspaceId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      console.error('Error fetching audit logs:', error);
      return { data: [], count: 0, error: error.message };
    }
    
    return { data: data || [], count: count || 0, error: null };
  } catch (error) {
    console.error('Unexpected error fetching audit logs:', error);
    return { data: [], count: 0, error: 'Internal server error' };
  }
}

/**
 * Get audit logs for a specific user across all workspaces
 */
export async function getUserAuditLogs(
  userId: string, 
  limit: number = 50, 
  offset: number = 0
) {
  try {
    const { data, error, count } = await supabase
      .from('workspace_member_audit')
      .select(`
        *,
        workspaces (
          id,
          name
        )
      `, { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);
    
    if (error) {
      console.error('Error fetching user audit logs:', error);
      return { data: [], count: 0, error: error.message };
    }
    
    return { data: data || [], count: count || 0, error: null };
  } catch (error) {
    console.error('Unexpected error fetching user audit logs:', error);
    return { data: [], count: 0, error: 'Internal server error' };
  }
}

/**
 * Clean up old audit logs (keep last 6 months)
 */
export async function cleanupOldAuditLogs(): Promise<void> {
  try {
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
    
    const { error } = await supabase
      .from('workspace_member_audit')
      .delete()
      .lt('created_at', sixMonthsAgo.toISOString());
    
    if (error) {
      console.error('Error cleaning up audit logs:', error);
    }
  } catch (error) {
    console.error('Unexpected error cleaning up audit logs:', error);
  }
}
