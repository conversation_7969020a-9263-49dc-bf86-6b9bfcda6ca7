"use client";

import React from 'react';
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface ProfileSkeletonProps {
  isCollapsed?: boolean;
}

export function ProfileSkeleton({ isCollapsed = false }: ProfileSkeletonProps) {
  return (
    <div
      className={`transition-all duration-500 ease-in-out p-2 sm:p-4 md:p-6 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden max-w-8xl mx-auto`}
    >
      {/* Header Skeleton */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <Skeleton className="h-8 w-48 md:h-10 md:w-56" />
        <Skeleton className="h-9 w-28 md:w-32 rounded-md" />
      </div>

      {/* Profile Content Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        
        {/* Profile Header Card Skeleton */}
        <Card className="md:col-span-1 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
          <CardContent className="pt-6 flex flex-col items-center p-4 md:p-6">
            {/* Avatar Skeleton */}
            <Skeleton className="h-20 w-20 md:h-32 md:w-32 rounded-full mb-4" />
            
            {/* Name Skeleton */}
            <Skeleton className="h-6 w-32 md:h-7 md:w-40 mb-4" />
            
            {/* Contact Info Skeleton */}
            <div className="w-full space-y-3 px-2 md:px-4">
              <div className="flex items-center gap-3">
                <Skeleton className="h-4 w-4 flex-shrink-0" />
                <Skeleton className="h-4 w-full max-w-[200px]" />
              </div>
              <div className="flex items-center gap-3">
                <Skeleton className="h-4 w-4 flex-shrink-0" />
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="flex items-center gap-3">
                <Skeleton className="h-4 w-4 flex-shrink-0" />
                <Skeleton className="h-4 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Professional Details Card Skeleton */}
        <Card className="md:col-span-2 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
          <CardHeader className="p-4 md:p-6">
            <div className="flex items-center gap-2 mb-2">
              <Skeleton className="h-5 w-5 md:h-6 md:w-6" />
              <Skeleton className="h-6 w-40 md:h-7 md:w-48" />
            </div>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0 space-y-4">
            {/* Professional Info Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-5 w-24" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-5 w-28" />
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-5 w-32" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-5 w-24" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Keys Card Skeleton */}
        <Card className="md:col-span-3 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
          <CardHeader className="p-4 md:p-6">
            <div className="flex items-center gap-2 mb-2">
              <Skeleton className="h-5 w-5 md:h-6 md:w-6" />
              <Skeleton className="h-6 w-24 md:h-7 md:w-32" />
            </div>
          </CardHeader>
          <CardContent className="p-4 md:p-6 pt-0 space-y-4">
            {/* API Keys Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-28" />
                <Skeleton className="h-5 w-20" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Skeleton className="h-4 w-36" />
                <Skeleton className="h-5 w-20" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-40" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Social Links Card Skeleton */}
      <Card className="mt-4 md:mt-6 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
        <CardHeader className="p-4 md:p-6">
          <Skeleton className="h-6 w-32 md:h-7 md:w-40" />
        </CardHeader>
        <CardContent className="p-4 md:p-6 pt-0">
          <div className="flex flex-wrap gap-3">
            <Skeleton className="h-9 w-20 rounded-md" />
            <Skeleton className="h-9 w-16 rounded-md" />
            <Skeleton className="h-9 w-18 rounded-md" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Mobile-specific skeleton for smaller screens
export function ProfileMobileSkeleton({ isCollapsed = false }: ProfileSkeletonProps) {
  return (
    <div
      className={`transition-all duration-500 ease-in-out p-3 ${
        isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"
      } w-auto overflow-hidden`}
    >
      {/* Mobile Header */}
      <div className="flex justify-between items-center mb-4">
        <Skeleton className="h-7 w-36" />
        <Skeleton className="h-8 w-24 rounded-md" />
      </div>

      {/* Mobile Profile Card */}
      <Card className="mb-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
        <CardContent className="p-4">
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-16 w-16 rounded-full flex-shrink-0" />
            <div className="flex-1">
              <Skeleton className="h-5 w-28 mb-2" />
              <Skeleton className="h-4 w-36" />
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-32" />
            </div>
            <div className="flex items-center gap-3">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mobile Professional Details */}
      <Card className="mb-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
        <CardHeader className="p-4 pb-2">
          <Skeleton className="h-5 w-32" />
        </CardHeader>
        <CardContent className="p-4 pt-2 space-y-3">
          <div className="space-y-2">
            <Skeleton className="h-3 w-16" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-3 w-20" />
            <Skeleton className="h-4 w-28" />
          </div>
        </CardContent>
      </Card>

      {/* Mobile API Keys */}
      <Card className="mb-4 bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
        <CardHeader className="p-4 pb-2">
          <Skeleton className="h-5 w-24" />
        </CardHeader>
        <CardContent className="p-4 pt-2 space-y-3">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="space-y-2">
              <Skeleton className="h-3 w-28" />
              <Skeleton className="h-4 w-20" />
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Mobile Social Links */}
      <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
        <CardHeader className="p-4 pb-2">
          <Skeleton className="h-5 w-28" />
        </CardHeader>
        <CardContent className="p-4 pt-2">
          <div className="flex gap-2">
            <Skeleton className="h-8 w-16 rounded-md" />
            <Skeleton className="h-8 w-14 rounded-md" />
            <Skeleton className="h-8 w-16 rounded-md" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
