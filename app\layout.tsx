import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import { Toaster } from "@/components/ui/sonner";
import { RouteGuard } from '@/components/auth/RouteGuard';
const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'INFILABS CRM',
  description: 'Modern CRM solution for sales teams by INFILABS',
  icons: {
    icon: [
      {
        url: '/favicon.ico',
        sizes: 'any',
      },
      {
        url: '/icon.png',
        type: 'image/png',
        sizes: '32x32',
      },
    ],
    apple: {
      url: '/apple-icon.png',
      type: 'image/png',
      sizes: '180x180',
    },
  },
};


export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>

      <body className={inter.className}>
        <Providers>
          <RouteGuard>
            {children}
          </RouteGuard>
          <Toaster />
        </Providers>
      </body>
    </html>
  );
}
