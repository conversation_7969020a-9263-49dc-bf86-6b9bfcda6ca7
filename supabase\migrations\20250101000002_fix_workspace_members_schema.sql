-- Fix workspace_members table for scalability and data integrity
-- This migration addresses critical issues in the current schema

-- 1. Add auto-increment sequence for primary key
CREATE SEQUENCE IF NOT EXISTS workspace_members_id_seq;
ALTER TABLE workspace_members ALTER COLUMN id SET DEFAULT nextval('workspace_members_id_seq');
ALTER SEQUENCE workspace_members_id_seq OWNED BY workspace_members.id;

-- 2. Add proper constraints and indexes
ALTER TABLE workspace_members 
  ADD CONSTRAINT workspace_members_user_workspace_unique 
  UNIQUE (user_id, workspace_id);

-- 3. Add foreign key constraints (enable after data cleanup)
ALTER TABLE workspace_members 
  ADD CONSTRAINT fk_workspace_members_workspace_id 
  FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE;

-- 4. Add composite indexes for performance
CREATE INDEX IF NOT EXISTS idx_workspace_members_user_active 
  ON workspace_members (user_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_workspace_members_workspace_status 
  ON workspace_members (workspace_id, status);

CREATE INDEX IF NOT EXISTS idx_workspace_members_email_status 
  ON workspace_members (email, status) WHERE status = 'pending';

-- 5. Add check constraints for data integrity
ALTER TABLE workspace_members 
  ADD CONSTRAINT chk_workspace_members_status 
  CHECK (status IN ('pending', 'accepted', 'declined', 'revoked'));

ALTER TABLE workspace_members 
  ADD CONSTRAINT chk_workspace_members_role 
  CHECK (role IN ('SuperAdmin', 'admin', 'member', 'viewer'));

-- 6. Add default values
ALTER TABLE workspace_members 
  ALTER COLUMN created_at SET DEFAULT NOW(),
  ALTER COLUMN status SET DEFAULT 'pending',
  ALTER COLUMN role SET DEFAULT 'member',
  ALTER COLUMN is_active SET DEFAULT false;

-- 7. Create invitation tokens table for secure invitations
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id INTEGER NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  role TEXT NOT NULL DEFAULT 'member',
  token TEXT NOT NULL UNIQUE,
  invited_by UUID NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '7 days'),
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  CONSTRAINT chk_invitation_role 
    CHECK (role IN ('SuperAdmin', 'admin', 'member', 'viewer')),
  CONSTRAINT chk_invitation_email 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- Indexes for invitations table
CREATE INDEX idx_workspace_invitations_token ON workspace_invitations (token);
CREATE INDEX idx_workspace_invitations_email ON workspace_invitations (email);
CREATE INDEX idx_workspace_invitations_workspace ON workspace_invitations (workspace_id);
CREATE INDEX idx_workspace_invitations_expires ON workspace_invitations (expires_at) WHERE accepted_at IS NULL;

-- 8. Create audit trail table
CREATE TABLE IF NOT EXISTS workspace_member_audit (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id INTEGER NOT NULL,
  user_id UUID,
  email TEXT,
  action TEXT NOT NULL, -- 'invited', 'accepted', 'declined', 'removed', 'role_changed'
  old_role TEXT,
  new_role TEXT,
  performed_by UUID NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Index for audit table
CREATE INDEX idx_workspace_member_audit_workspace ON workspace_member_audit (workspace_id);
CREATE INDEX idx_workspace_member_audit_user ON workspace_member_audit (user_id);
CREATE INDEX idx_workspace_member_audit_created ON workspace_member_audit (created_at);

-- 9. Add updated_at trigger for workspace_members
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_workspace_members_updated_at 
  BEFORE UPDATE ON workspace_members 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workspace_invitations_updated_at 
  BEFORE UPDATE ON workspace_invitations 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Enable RLS and create policies
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_member_audit ENABLE ROW LEVEL SECURITY;

-- RLS Policies for workspace_members
CREATE POLICY "Users can view their own memberships" ON workspace_members
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Workspace admins can view all members" ON workspace_members
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM workspace_members wm 
      WHERE wm.workspace_id = workspace_members.workspace_id 
      AND wm.user_id = auth.uid() 
      AND wm.role IN ('SuperAdmin', 'admin')
      AND wm.status = 'accepted'
    )
  );

-- RLS Policies for workspace_invitations
CREATE POLICY "Workspace admins can manage invitations" ON workspace_invitations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM workspace_members wm 
      WHERE wm.workspace_id = workspace_invitations.workspace_id 
      AND wm.user_id = auth.uid() 
      AND wm.role IN ('SuperAdmin', 'admin')
      AND wm.status = 'accepted'
    )
  );

CREATE POLICY "Users can view their own invitations" ON workspace_invitations
  FOR SELECT USING (email = auth.email());

-- RLS Policies for audit table
CREATE POLICY "Workspace admins can view audit logs" ON workspace_member_audit
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM workspace_members wm 
      WHERE wm.workspace_id = workspace_member_audit.workspace_id 
      AND wm.user_id = auth.uid() 
      AND wm.role IN ('SuperAdmin', 'admin')
      AND wm.status = 'accepted'
    )
  );

-- 11. Create function to ensure only one active workspace per user
CREATE OR REPLACE FUNCTION ensure_single_active_workspace()
RETURNS TRIGGER AS $$
BEGIN
  -- If setting a workspace as active, deactivate all others for this user
  IF NEW.is_active = true AND (OLD.is_active IS NULL OR OLD.is_active = false) THEN
    UPDATE workspace_members 
    SET is_active = false 
    WHERE user_id = NEW.user_id 
    AND id != NEW.id 
    AND is_active = true;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER ensure_single_active_workspace_trigger
  BEFORE UPDATE ON workspace_members
  FOR EACH ROW
  EXECUTE FUNCTION ensure_single_active_workspace();

-- 12. Create function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS void AS $$
BEGIN
  DELETE FROM workspace_invitations 
  WHERE expires_at < NOW() 
  AND accepted_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-expired-invitations', '0 2 * * *', 'SELECT cleanup_expired_invitations();');
