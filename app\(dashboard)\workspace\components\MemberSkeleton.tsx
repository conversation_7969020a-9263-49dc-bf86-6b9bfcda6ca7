"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function MemberSkeleton() {
  return (
    <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-[16px] md:rounded-[4px]">
      <CardHeader className="p-4 md:p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </CardHeader>
      
      <CardContent className="p-4 md:p-6 pt-0 space-y-6">
        {/* Invite Form Skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-5 w-32" />
          <div className="flex flex-col md:flex-row gap-2">
            <Skeleton className="h-10 flex-1" />
            <div className="flex flex-col md:flex-row gap-2">
              <Skeleton className="h-10 w-full md:w-[140px]" />
              <Skeleton className="h-10 w-full md:w-auto md:px-6" />
            </div>
          </div>
        </div>

        {/* Members List Skeleton */}
        <div className="space-y-4">
          <Skeleton className="h-5 w-48" />
          <div className="space-y-2">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="flex flex-col sm:flex-row sm:items-center justify-between p-2 md:p-4 bg-secondary rounded-lg gap-2"
              >
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Skeleton className="md:w-10 md:h-10 w-8 h-8 rounded-full" />
                    <Skeleton className="absolute -bottom-1 -right-1 md:w-6 md:h-6 w-5 h-5 rounded-full" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-3 w-48" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="flex md:flex-row flex-col items-center space-x-2 self-end sm:self-center">
                  <Skeleton className="h-8 w-16" />
                  <div className="flex gap-1">
                    <Skeleton className="h-6 w-6 rounded" />
                    <Skeleton className="h-6 w-6 rounded" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Mobile-specific skeleton for smaller screens
export function MemberMobileSkeleton() {
  return (
    <Card className="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-lg">
      <CardHeader className="p-4 pb-2">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-5 w-32" />
            <Skeleton className="h-3 w-48" />
          </div>
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
      </CardHeader>
      
      <CardContent className="p-4 pt-2 space-y-4">
        {/* Mobile Invite Form */}
        <div className="space-y-3">
          <Skeleton className="h-4 w-24" />
          <div className="space-y-2">
            <Skeleton className="h-9 w-full" />
            <div className="flex gap-2">
              <Skeleton className="h-9 flex-1" />
              <Skeleton className="h-9 w-20" />
            </div>
          </div>
        </div>

        {/* Mobile Members List */}
        <div className="space-y-3">
          <Skeleton className="h-4 w-36" />
          <div className="space-y-2">
            {[1, 2].map((i) => (
              <div
                key={i}
                className="flex items-center justify-between p-3 bg-secondary rounded-lg"
              >
                <div className="flex items-center space-x-2">
                  <Skeleton className="w-8 h-8 rounded-full" />
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
                <div className="flex gap-1">
                  <Skeleton className="h-6 w-6 rounded" />
                  <Skeleton className="h-6 w-6 rounded" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
