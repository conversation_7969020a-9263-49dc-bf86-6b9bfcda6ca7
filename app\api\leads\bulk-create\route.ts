import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, notify<PERSON><PERSON><PERSON>hang<PERSON>, validateEmail, validatePhoneNumber } from "../utils";

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;
    
    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");
    
    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }
    
    // Parse request body
    const body = await request.json();
    
    if (!Array.isArray(body) || body.length === 0) {
      return NextResponse.json({ error: "At least one lead is required" }, { status: 400 });
    }
    
    // Process and validate each lead
    const validLeads = await Promise.all(
      body.map(async (lead) => {
        if (!lead.name || !lead.email) return null;
        
        const isValidEmail = await validateEmail(lead.email);
        const isValidPhone = await validatePhoneNumber(lead.phone);
        
        return {
          name: lead.name,
          email: lead.email,
          phone: lead.phone || null,
          status: { name: "Arrived", color: "#FFA500" },
          company: lead.company || null,
          position: lead.position || null,
          contact_method: lead.contact_method || "Call",
          assign_to: null,
          lead_source_id: lead.sourceId || null,
          work_id: workspaceId,
          user_id: user.id,
          text_area: lead.text_area || "",
          is_email_valid: isValidEmail,
          is_phone_valid: isValidPhone,
          created_at: lead.createdAt
            ? new Date(lead.createdAt).toISOString()
            : new Date().toISOString(),
        };
      })
    );
    
    const filteredLeads = validLeads.filter(Boolean);
    
    if (filteredLeads.length === 0) {
      return NextResponse.json({ error: "No valid leads provided" }, { status: 400 });
    }
    
    // Insert leads into database
    const { data, error } = await supabase
      .from("leads")
      .insert(filteredLeads)
      .select();
    
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }
    
    // Create notification
    await notifyLeadChange(
      "bulk",
      "bulk_created",
      user.id,
      workspaceId,
      { 
        count: filteredLeads.length,
        lead_names: filteredLeads.filter(lead => lead !== null).map(lead => lead.name)
      }
    );
    
    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating leads in bulk:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
