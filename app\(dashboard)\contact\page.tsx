"use client";

import React from "react";
import { MessageSquare } from "lucide-react";
import { Table, TableBody } from "@/components/ui/table";
import { useSelector } from "react-redux";
import { RootState } from "@/lib/store/store";
import { useGetTagsQuery } from "@/lib/store/services/tags";
import { useGetWorkspaceMembersQuery } from "@/lib/store/services/workspace";

// Internal imports
import { useContactData, useContactActions, useTableManagement } from "./hooks";
import {
  ContactTableRow,
  ContactTableHeader,
  ContactFilters,
  ContactLoading
} from "./components";
import { CONTACT_CONSTANTS } from "./constants/contact";

import "react-resizable/css/styles.css";

export default function ContactPage() {
  // Custom hooks for data and actions
  const {
    paginatedContacts,
    contactStatuses,
    workspaceId,
    currentPage,
    itemsPerPage,
    totalPages,
    totalContacts,
    search,
    statusFilter,
    isLoading,
    updateSearch,
    updateStatusFilter,
    updateCurrentPage,
    updateItemsPerPage,
  } = useContactData();

  const {
    editState,
    nameInfo,
    emailInfo,
    phoneInfo,
    businessInfo,
    emailValidation,
    isUpdating,
    setNameInfo,
    setEmailInfo,
    setPhoneInfo,
    setBusinessInfo,
    setEmailValidation,
    enterEditMode,
    exitEditMode,
    handleUpdate,
    initiateDirectContact,
    sendEmail,
    openInGmail,
    handleView,
    handleTagChange,
    handleRemoveTag,
    expandedRow,
    setExpandedRow,
    handleKeyDown,
  } = useContactActions();

  const {
    selectedHeaders,
    columnWidths,
    dropdownOpen,
    dropdownOpenRemove,
    availableColumns,
    addColumn,
    removeColumn,
    handleResize,
    toggleDropdown,
    toggleAddColumnDropdown,
  } = useTableManagement();

  // External data
  const { data: tags = [] } = useGetTagsQuery(workspaceId);
  const { data: workspaceMembers } = useGetWorkspaceMembersQuery(workspaceId);
  const isCollapsed = useSelector((state: RootState) => state.sidebar.isCollapsed);

  // Toggle row expansion for mobile
  const toggleRow = (id: number) => {
    setExpandedRow(prev => prev === id ? null : id);
  };

  // Show loading state
  if (isLoading) {
    return <ContactLoading />;
  }

  return (
    <div className={`transition-all duration-300 ${isCollapsed ? 'ml-16' : 'ml-64'} p-6 min-h-screen overflow-hidden`}>
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-primary/10 rounded-lg">
          <MessageSquare className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-foreground">Contacts</h1>
          <p className="text-muted-foreground">
            Manage your qualified contacts ({totalContacts} total)
          </p>
        </div>
      </div>

      {/* Filters */}
      <ContactFilters
        search={search}
        statusFilter={statusFilter}
        contactStatuses={contactStatuses}
        onSearchChange={updateSearch}
        onStatusFilterChange={updateStatusFilter}
      />

      {/* Table */}
      <div className="border rounded-lg overflow-hidden bg-card">
        <div className="w-full overflow-x-auto">
          <Table className="min-w-full">
            <ContactTableHeader
              selectedHeaders={selectedHeaders}
              columnWidths={columnWidths}
              availableColumns={availableColumns}
              dropdownOpen={dropdownOpen}
              dropdownOpenRemove={dropdownOpenRemove}
              onResize={handleResize}
              onToggleDropdown={toggleDropdown}
              onToggleAddDropdown={toggleAddColumnDropdown}
              onAddColumn={addColumn}
              onRemoveColumn={removeColumn}
            />

            <TableBody>
              {paginatedContacts.map((contact) => (
                <ContactTableRow
                  key={contact.id}
                  contact={contact}
                  selectedHeaders={selectedHeaders}
                  workspaceId={workspaceId}
                  editState={editState}
                  nameInfo={nameInfo}
                  emailInfo={emailInfo}
                  phoneInfo={phoneInfo}
                  businessInfo={businessInfo}
                  emailValidation={emailValidation}
                  expandedRow={expandedRow}
                  onEnterEditMode={enterEditMode}
                  onUpdate={handleUpdate}
                  onKeyDown={handleKeyDown}
                  onToggleRow={toggleRow}
                  onSetNameInfo={setNameInfo}
                  onSetEmailInfo={setEmailInfo}
                  onSetPhoneInfo={setPhoneInfo}
                  onSetBusinessInfo={setBusinessInfo}
                  onSetEmailValidation={setEmailValidation}
                  onSendEmail={sendEmail}
                  onOpenInGmail={openInGmail}
                  onInitiateContact={initiateDirectContact}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-4 mt-6">
        <div className="text-sm text-muted-foreground">
          Showing {((currentPage - 1) * itemsPerPage) + 1} to{' '}
          {Math.min(currentPage * itemsPerPage, totalContacts)} of {totalContacts} contacts
        </div>

        <div className="flex items-center gap-2">
          {/* Previous Button */}
          <button
            onClick={() => updateCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="h-8 w-8 p-0 border rounded hover:bg-muted disabled:opacity-50"
          >
            ←
          </button>

          {/* Page Numbers */}
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + Math.max(1, currentPage - 2);
              if (page > totalPages) return null;

              return (
                <button
                  key={page}
                  onClick={() => updateCurrentPage(page)}
                  className={`h-8 w-8 text-sm rounded ${
                    currentPage === page
                      ? 'bg-primary text-primary-foreground'
                      : 'border hover:bg-muted'
                  }`}
                >
                  {page}
                </button>
              );
            })}
          </div>

          {/* Next Button */}
          <button
            onClick={() => updateCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="h-8 w-8 p-0 border rounded hover:bg-muted disabled:opacity-50"
          >
            →
          </button>

          {/* Items per page */}
          <select
            value={itemsPerPage}
            onChange={(e) => {
              updateItemsPerPage(Number(e.target.value));
              updateCurrentPage(1);
            }}
            className="h-8 w-[70px] border rounded bg-background"
          >
            {CONTACT_CONSTANTS.PAGINATION.ITEMS_PER_PAGE_OPTIONS.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
          <span className="text-sm text-muted-foreground">per page</span>
        </div>
      </div>
    </div>
  );
}