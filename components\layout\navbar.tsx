"use client";

import { usePathname, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { UserNav } from "@/components/layout/user-nav";
import { Bell, CheckCircle, AlertCircle, UserPlus, Trash, Edit, FileText, Eye, EyeOff } from "lucide-react";
import { supabase } from "@/lib/supabaseClient";
import { useEffect, useState } from "react";
import Link from "next/link";
import { User } from "@supabase/supabase-js";
import SubscriptionBanner from "./subscription-banner";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import useLeadNotifications from "@/hooks/useLeadNotifications";
import { formatDistanceToNow } from "date-fns";

export function Navbar() {
  const pathname = usePathname();
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { notifications, unreadCount, markAsRead, markAllAsRead } = useLeadNotifications();

  useEffect(() => {
    const fetchSession = async () => {
      setLoading(true);
      const { data } = await supabase.auth.getSession();
      setUser(data.session?.user ?? null);
      setLoading(false);
    };

    fetchSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Helper function to get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (true) {
      case type.includes('created'):
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case type.includes('updated'):
        return <Edit className="h-4 w-4 text-blue-500" />;
      case type.includes('assigned'):
        return <UserPlus className="h-4 w-4 text-purple-500" />;
      case type.includes('deleted'):
        return <Trash className="h-4 w-4 text-red-500" />;
      case type.includes('notes'):
        return <FileText className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  // Helper function to format notification message
  const formatNotificationMessage = (notification: any) => {
    const { action_type, details } = notification;
    const userName = details?.user_name || details?.updated_by_name || "Someone";

    switch (action_type) {
      case "created":
        return (
          <>
            <span className="font-medium">{userName}</span> created a new lead:{' '}
            <span className="font-medium">{details?.lead_name || "Unnamed lead"}</span>
          </>
        );
      case "bulk_created":
        return (
          <>
            <span className="font-medium">{userName}</span> imported{' '}
            <span className="font-medium">{details?.count || "multiple"}</span> new leads
          </>
        );
      case "updated":
        return (
          <>
            <span className="font-medium">{userName}</span> updated lead:{' '}
            <span className="font-medium">{details?.lead_name || ""}</span>
          </>
        );
      case "notes_updated":
        return (
          <>
            <span className="font-medium">{userName}</span> updated notes for{' '}
            <span className="font-medium">{details?.lead_name || "a lead"}</span>
          </>
        );
      case "assigned":
        return (
          <>
            <span className="font-medium">{userName}</span> assigned lead:{' '}
            <span className="font-medium">{details?.lead_name || ""}</span>
          </>
        );
      case "leads_deleted":
        return (
          <>
            <span className="font-medium">{userName}</span> deleted{' '}
            <span className="font-medium">{details?.lead_count || "multiple"}</span> leads
          </>
        );
      case "data_updated":
        return (
          <>
            <span className="font-medium">{userName}</span> updated data for{' '}
            <span className="font-medium">{details?.lead_name || "a lead"}</span>
          </>
        );
      default:
        return (
          <>
            <span className="font-medium">{userName}</span> performed an action on a lead
          </>
        );
    }
  };

  // Helper function to get lead ID from notification
  const getLeadIdFromNotification = (notification: any): string | null => {
    const { action_type, details } = notification;

    // For bulk actions or deletions, there's no single lead to navigate to
    if (action_type === "bulk_created" || action_type === "leads_deleted") {
      return null;
    }

    return details?.lead_id || null;
  };

  // Handle notification click to navigate to lead details
  const handleNotificationClick = (notification: any) => {
    const leadId = getLeadIdFromNotification(notification);

    // Mark the notification as read
    markAsRead(notification.id);

    // If we have a lead ID, navigate to the lead details page
    if (leadId) {
      router.push(`/leads/${leadId}`);
    }
  };

  const NotificationButton = () => (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative notification-icon">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-destructive text-xs text-white flex items-center justify-center">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Notifications</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0 dark:bg-slate-800 dark:border-slate-700">
        <div className="p-3 border-b dark:border-slate-700 flex justify-between items-center">
          <h3 className="font-medium dark:text-white">Notifications</h3>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="text-xs hover:bg-slate-100 dark:hover:bg-slate-700"
            >
              Mark all as read
            </Button>
          )}
        </div>
        <div className="max-h-80 overflow-y-auto">
          {notifications && notifications.length > 0 ? (
            notifications.slice(0, 5).map((notification: any) => (
              <div
                key={notification.id}
                className={`p-3 border-b dark:border-slate-700 last:border-b-0 hover:bg-slate-100 dark:hover:bg-slate-700 ${!notification.read ? 'bg-slate-50 dark:bg-slate-700/50' : ''} ${getLeadIdFromNotification(notification) ? 'cursor-pointer' : ''}`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start">
                  <div className="mr-2 mt-1">{getNotificationIcon(notification.action_type)}</div>
                  <div className="flex-1">
                    <div className="text-sm dark:text-white">{formatNotificationMessage(notification)}</div>
                    <div className="flex justify-between items-center mt-1">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                      </p>
                      {getLeadIdFromNotification(notification) && (
                        <span className="text-xs text-blue-500 dark:text-blue-400 flex items-center">
                          <span className="mr-1">ID: {notification.lead_id || notification.details?.lead_id}</span>
                          <span>View →</span>
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="p-4 text-center text-sm text-slate-500 dark:text-slate-400">
              No notifications
            </div>
          )}
        </div>
        <div className="p-2 border-t dark:border-slate-700">
          <Button
            variant="ghost"
            size="sm"
            className="w-full dark:text-white hover:bg-slate-100 dark:hover:bg-slate-700"
            onClick={() => router.push('/notifications')}
          >
            View all notifications
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );

  return (
    <>
      <SubscriptionBanner />
      <div >
        {/* Mobile Navbar */}
        <div className="md:hidden bg-white dark:bg-black backdrop-blur fixed top-0 left-0 right-0 z-50 w-full">
          <div className="relative flex h-16 items-center justify-center px-4">

            {/* Centered actions */}
            <div className="flex items-center space-x-4">
              <NotificationButton />
              <ThemeToggle />
              {!loading && user && <UserNav />}
            </div>

            {/* Logo positioned absolutely so the actions remain centered */}
            <div className="absolute left-4">
              <Link href="/dashboard" className="flex items-center gap-2 hover:opacity-90 transition-opacity">
                <div className="flex items-center justify-center bg-black dark:bg-white text-white dark:text-black rounded-md font-bold text-xs w-8 h-8">
                  IL
                </div>
                <span className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent hidden sm:inline">
                  INFILABS
                </span>
              </Link>
            </div>
          </div>
        </div>

        {/* Desktop Navbar */}
        <div className="hidden md:flex bg-white dark:bg-black backdrop-blur fixed top-0 left-0 right-0 z-50 w-full h-16 items-center px-4 md:px-6 lg:px-8 justify-between">

          <div className="flex-1">
            <Link href="/dashboard" className="flex items-center gap-2 hover:opacity-90 transition-opacity">
              <div className="flex items-center justify-center bg-black dark:bg-white text-white dark:text-black rounded-md font-bold text-xs w-8 h-8">
                IL
              </div>
              <span className="font-bold text-xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
                INFILABS
              </span>
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <NotificationButton />
            <ThemeToggle />

            {!loading && (
              <>
                {user ? (
                  <UserNav />
                ) : (
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" asChild className="dark:text-white hover:bg-slate-100 dark:hover:bg-slate-800">
                      <Link href="/login">Log in</Link>
                    </Button>
                    <Button size="sm" asChild className="btn-gradient">
                      <Link href="/register">Sign up</Link>
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        <div className="h-16" />
      </div>
    </>
  );
}