import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabaseServer";
import { authenticateUser, notifyLeadChange } from "../utils";

// GET lead by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const id = params.id;

    if (!id) {
      return NextResponse.json({ error: "Lead ID is required" }, { status: 400 });
    }

    // Fetch lead by ID
    const { data, error } = await supabase
      .from("leads")
      .select("*")
      .eq("id", id);

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error fetching lead:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// UPDATE lead by ID
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const id = params.id;

    if (!id) {
      return NextResponse.json({ error: "Lead ID is required" }, { status: 400 });
    }

    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");

    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }

    // Parse request body
    const body = await request.json();

    if (!body || Object.keys(body).length === 0) {
      return NextResponse.json({ error: "Update data is required" }, { status: 400 });
    }

    // Get current lead data to determine changes
    const { data: currentLead, error: fetchError } = await supabase
      .from("leads")
      .select("*")
      .eq("id", id)
      .single();

    if (fetchError) {
      return NextResponse.json({ error: fetchError.message }, { status: 400 });
    }

    // Handle both ID-based and legacy status updates
    const updatedBody: any = { ...body };

    // Handle status updates
    if (body.status_id !== undefined) {
      // New ID-based status
      updatedBody.status_id = body.status_id;
      // Also update legacy field for backward compatibility
      if (body.status_id) {
        const { data: statusData } = await supabase
          .from("status")
          .select("name, color")
          .eq("id", body.status_id)
          .single();

        if (statusData) {
          updatedBody.status = JSON.stringify({
            name: statusData.name,
            color: statusData.color
          });
        }
      }
    } else if (body.status) {
      // Legacy status update
      updatedBody.status = body.status;
    }

    // Handle tags
    if (body.tags) {
      updatedBody.tags = JSON.stringify(body.tags);
    }

    // Update the lead
    const { data, error } = await supabase
      .from("leads")
      .update(updatedBody)
      .eq("id", id)
      .select();

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Determine what changed
    const changes: Record<string, { old: any; new: any }> = {};
    Object.keys(updatedBody).forEach(key => {
      if (JSON.stringify(currentLead[key]) !== JSON.stringify(updatedBody[key]) && updatedBody[key] !== undefined) {
        changes[key] = {
          old: currentLead[key],
          new: updatedBody[key]
        };
      }
    });

    // Create notification
    await notifyLeadChange(
      id,
      "updated",
      user.id,
      workspaceId,
      {
        lead_id: id,
        lead_name: currentLead.name,
        updated_by: user.id,
        changes: changes
      }
    );

    return NextResponse.json({ data }, { status: 200 });
  } catch (error) {
    console.error("Error updating lead:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE lead by ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate user
    const auth = await authenticateUser(request);
    if (auth.error) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    const { user } = auth;

    const id = params.id;

    // Get workspaceId from URL
    const searchParams = request.nextUrl.searchParams;
    const workspaceId = searchParams.get("workspaceId");

    if (!workspaceId) {
      return NextResponse.json({ error: "Workspace ID is required" }, { status: 400 });
    }

    // Check if user is workspace owner
    const { data: workspaceData, error: workspaceError } = await supabase
      .from("workspaces")
      .select("owner_id")
      .eq("id", workspaceId)
      .single();

    if (workspaceError) {
      console.error("Workspace Check Error:", workspaceError.message);
      return NextResponse.json({ error: workspaceError.message }, { status: 400 });
    }

    const isWorkspaceOwner = workspaceData?.owner_id === user.id;

    // If not workspace owner, check if user is admin
    if (!isWorkspaceOwner) {
      const { data: memberData, error: memberError } = await supabase
        .from("workspace_members")
        .select("role")
        .eq("workspace_id", workspaceId)
        .eq("user_id", user.id)
        .single();

      if (memberError) {
        console.error("Workspace Member Check Error:", memberError.message);
        return NextResponse.json({ error: memberError.message }, { status: 400 });
      }

      const isAdmin = memberData?.role === "admin" || memberData?.role === "SuperAdmin";

      if (!isAdmin) {
        return NextResponse.json({
          error: "Only workspace owners and admins can delete leads"
        }, { status: 403 });
      }
    }

    // Get lead data before deletion
    const { data: leadToDelete, error: fetchError } = await supabase
      .from("leads")
      .select("id, name")
      .eq("id", id)
      .eq("work_id", workspaceId)
      .single();

    if (fetchError) {
      console.error("Fetch lead error:", fetchError.message);
      return NextResponse.json({ error: fetchError.message }, { status: 400 });
    }

    // Delete the lead
    const { data, error } = await supabase
      .from("leads")
      .delete()
      .eq("id", id)
      .eq("work_id", workspaceId);

    if (error) {
      console.error("Supabase Delete Error:", error.message);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Create notification
    await notifyLeadChange(
      id,
      "lead_deleted",
      user.id,
      workspaceId,
      {
        deleted_by: user.id,
        lead_name: leadToDelete.name,
        lead_id: leadToDelete.id
      }
    );

    return NextResponse.json({
      message: "Lead deleted successfully",
      data
    }, { status: 200 });
  } catch (error) {
    console.error("Error deleting lead:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
