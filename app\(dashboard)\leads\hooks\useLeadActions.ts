import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import * as z from "zod";
import {
  useCreateLeadMutation,
  useUpdateLeadMutation,
  useBulkDeleteLeadsMutation,
  useCreateManyLeadMutation,
  useUpdateLeadDataMutation,
  useAssignRoleMutation,
} from "@/lib/store/services/leadsApi";
import { CRM_MESSAGES } from "@/lib/constant/crm";
import { Lead, LeadFormData, DialogMode } from "../types/leads";
import { leadSchema } from "../constants/leads";

interface UseLeadActionsProps {
  workspaceId: string;
  leads: Lead[];
  setLeads: (leads: Lead[] | ((prev: Lead[]) => Lead[])) => void;
  selectedLeads: number[];
  setSelectedLeads: (leads: number[]) => void;
}

export const useLeadActions = ({
  workspaceId,
  leads,
  setLeads,
  selectedLeads,
  setSelectedLeads,
}: UseLeadActionsProps) => {
  const router = useRouter();

  // Dialog state
  const [dialogMode, setDialogMode] = useState<DialogMode>(null);
  const [editingLead, setEditingLead] = useState<Lead | null>(null);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);

  // API mutations
  const [createLead, { isLoading: isCreateLoading }] = useCreateLeadMutation();
  const [createManyLead, { isLoading: isCreateManyLoading }] = useCreateManyLeadMutation();
  const [updateLeadData, { isLoading: isUpdateLoading }] = useUpdateLeadDataMutation();
  const [updateLead] = useUpdateLeadMutation();
  const [deleteLeadsData] = useBulkDeleteLeadsMutation();
  const [assignRole, { isLoading: isAssignLoading }] = useAssignRoleMutation();

  // Form setup
  const form = useForm<z.infer<typeof leadSchema>>({
    resolver: zodResolver(leadSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      company: "",
      position: "",
      contact_method: undefined,
      revenue: 0,
    },
  });

  // Reset dialog state
  const resetDialog = () => {
    form.reset({
      name: "",
      email: "",
      phone: "",
      company: "",
      position: "",
      contact_method: undefined,
      revenue: 0,
    });
    setEditingLead(null);
    setDialogMode(null);
  };

  // Open create dialog
  const openCreateDialog = () => {
    resetDialog();
    setDialogMode("create");
  };

  // Open edit dialog
  const openEditDialog = (lead: Lead) => {
    form.reset({
      name: lead.Name,
      email: lead.email,
      phone: lead.phone,
      company: lead.company,
      position: lead.position,
      contact_method: lead.contact_method,
      revenue: lead.revenue,
    });
    setEditingLead(lead);
    setDialogMode("edit");
  };

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof leadSchema>) => {
    if (dialogMode === "create") {
      try {
        const response = await createLead({
          workspaceId: workspaceId,
          body: data,
        });

        if (response.error) {
          let errorMessage = "An unknown error occurred";
          let errorParts: string[] = [];

          if ("data" in response.error && response.error.data) {
            errorMessage = JSON.stringify(response.error.data);
            errorParts = errorMessage.split(":");
          } else if ("error" in response.error) {
            errorMessage = response.error.error;
            errorParts = errorMessage.split(":");
          }

          if (errorParts.length > 1) {
            errorMessage = errorParts[1].trim().replace(/["}]/g, "");
          }

          toast.error(errorMessage);
          resetDialog();
          return;
        }

        setLeads([
          ...leads,
          {
            ...data,
            id: Date.now(), // Temporary ID
            Name: data.name || "",
            company: data.company || "",
            position: data.position || "",
            revenue: data.revenue || 0,
            createdAt: new Date().toISOString(),
            isDuplicate: false,
            is_email_valid: true,
            is_phone_valid: true,
          } as unknown as Lead,
        ]);

        toast.success("Lead created successfully");
        resetDialog();
      } catch (error) {
        console.error(error);
        toast.error("An error occurred while creating the lead.");
      }
    } else if (dialogMode === "edit" && editingLead) {
      // Update existing lead
      try {
        updateLeadData({ id: String(editingLead.id), leads: data, workspaceId });
        setLeads((prevLeads) =>
          prevLeads.map((lead) =>
            lead.id === editingLead.id
              ? {
                ...lead,
                ...data,
                company: data.company || "",
                position: data.position || "",
                revenue: data.revenue || 0,
              }
              : lead
          )
        );
        setEditingLead(null);
        toast.success(CRM_MESSAGES.LEAD_UPDATED_SUCCESS);
      } catch (error) {
        console.error("Error updating lead", error);
        toast.error(CRM_MESSAGES.LEAD_UPDATED_ERROR);
      }
    }
    resetDialog();
  };

  // Delete selected leads
  const handleDelete = async () => {
    try {
      await deleteLeadsData({
        id: selectedLeads.map(String),
        workspaceId: workspaceId,
      }).unwrap();

      setLeads(leads.filter((lead) => !selectedLeads.includes(lead.id)));
      setSelectedLeads([]);
      setDialogMode(null);
      toast.success("Selected leads deleted successfully");
    } catch (error: any) {
      console.error("Delete error:", error);

      const errorMessage =
        error.data?.message ||
        error.data?.error ||
        error.error ||
        "Failed to delete leads";

      toast.error(errorMessage);
    }
  };

  // Handle status change
  const handleStatusChange = async (id: number, value: string) => {
    const { id: statusId, name, color } = JSON.parse(value);

    try {
      // Use ID-based assignment if statusId is available, otherwise fall back to legacy
      const updateData = statusId
        ? { status_id: statusId }
        : { status: { name, color } };

      await updateLead({ id: String(id), leads: updateData, workspaceId });

      setLeads((prevLeads) =>
        prevLeads.map((lead) =>
          lead.id === id
            ? {
              ...lead,
              status_id: statusId,
              status: {
                name,
                color,
              },
            }
            : lead
        )
      );

      toast.success(`Lead status updated to ${name}`);
    } catch (error) {
      console.error("Error updating lead status:", error);
      toast.error("Failed to update lead status");
    }
  };

  // Handle assignment change
  const handleAssignChange = async (id: number, assign: string) => {
    const { id: memberId, name, role } = JSON.parse(assign);

    try {
      // Use ID-based assignment if memberId is available, otherwise fall back to legacy
      const assignmentData = memberId
        ? { assigned_to_id: memberId }
        : { name, role };

      await assignRole({ id: String(id), data: assignmentData, workspaceId: workspaceId });

      setLeads((prevLeads) =>
        prevLeads.map((lead) =>
          lead.id === id
            ? {
              ...lead,
              assigned_to_id: memberId,
              assign_to: {
                name,
                role,
              },
            }
            : lead
        )
      );

      toast.success(`Lead assigned to ${name}`);
    } catch (error) {
      console.error("Error assigning lead:", error);
      toast.error("Failed to assign lead");
    }
  };

  // Handle view lead
  const handleView = (id: number) => {
    router.push(`/leads/${id}`);
  };

  // Toggle row expansion
  const toggleRow = (id: number) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  // Initiate direct contact
  const initiateDirectContact = (lead: Lead, method: string) => {
    const sanitizedPhone = lead.phone.replace(/\D/g, "");

    switch (method) {
      case "WhatsApp":
        window.open(`https://wa.me/${sanitizedPhone}`, "_blank");
        break;
      case "Call":
        window.location.href = `tel:${lead.phone}`;
        break;
      case "SMS":
        window.location.href = `sms:${lead.phone}`;
        break;
      default:
    }
  };

  return {
    // Dialog state
    dialogMode,
    editingLead,
    expandedRow,

    // Form
    form,

    // Loading states
    isCreateLoading,
    isCreateManyLoading,
    isUpdateLoading,
    isAssignLoading,

    // Actions
    openCreateDialog,
    openEditDialog,
    resetDialog,
    onSubmit,
    handleDelete,
    handleStatusChange,
    handleAssignChange,
    handleView,
    toggleRow,
    initiateDirectContact,
    setDialogMode,
  };
};
