"use client";

import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { LayoutGrid } from "lucide-react";

// Hooks
import { useIntegrationData } from "./hooks/useIntegrationData";
import { useIntegrationState } from "./hooks/useIntegrationState";
import { useFilteredPlatforms } from "./hooks/useFilteredPlatforms";

// Components
import { IntegrationSkeleton } from "./components/IntegrationSkeleton";
import { IntegrationFilters } from "./components/IntegrationFilters";
import { IntegrationTable } from "./components/IntegrationTable";

// Constants
import { INTEGRATION_LABELS } from "./constants/integration";

// Utils
import { openDocumentation } from "./utils/integrationUtils";

const PlatformIntegrations: React.FC = () => {
  // Custom hooks
  const {
    workspaceData,
    isWorkspaceLoading,
    isCollapsed,
    platforms,
    isInitialLoading,
  } = useIntegrationData();

  const {
    expandedRow,
    searchTerm,
    activeFilter,
    toggleRow,
    handleSearch<PERSON>hange,
    handleFilterChange,
  } = useIntegrationState();

  const { filteredPlatforms } = useFilteredPlatforms({
    platforms,
    searchTerm,
    activeFilter,
  });

  // Show skeleton loader during initial load
  if (isInitialLoading) {
    return <IntegrationSkeleton isCollapsed={isCollapsed} />;
  }

  return (
    <div className={`p-4 md:p-6 transition-all duration-300 ease-in-out ${isCollapsed ? "md:ml-[80px]" : "md:ml-[250px]"} max-w-8xl mx-auto`}>
      <Card className="w-full rounded-[16px] md:rounded-[4px] overflow-hidden">
        {/* Header */}
        <CardHeader className="flex flex-row justify-between items-center bg-gray-100 dark:bg-gray-800 md:bg-white md:dark:bg-gray-900">
          <div className="flex gap-6">
            <div className="md:hidden lg:hidden w-2 h-2 pb-4 text-gray-700 dark:text-gray-300">
              <LayoutGrid />
            </div>
            <CardTitle className="text-sm md:text-xl lg:text-2xl text-gray-900 dark:text-white">
              {INTEGRATION_LABELS.TITLE}
            </CardTitle>
          </div>

          <IntegrationFilters
            searchTerm={searchTerm}
            activeFilter={activeFilter}
            onSearchChange={handleSearchChange}
            onFilterChange={handleFilterChange}
          />
        </CardHeader>

        <CardContent>
          <IntegrationTable
            platforms={filteredPlatforms}
            expandedRow={expandedRow}
            onToggleRow={toggleRow}
            onOpenDocumentation={openDocumentation}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default PlatformIntegrations;