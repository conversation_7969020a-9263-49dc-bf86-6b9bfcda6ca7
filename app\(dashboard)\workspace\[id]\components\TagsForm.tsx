"use client";

import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TagsFormProps } from '../types/workspace';
import { WORKSPACE_CONSTANTS } from '../constants/workspace';

export function TagsForm({ tags, onSubmit }: TagsFormProps) {
  return (
    <div className="grid gap-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <Input
          placeholder={WORKSPACE_CONSTANTS.UI_TEXT.TAGS_NAME_PLACEHOLDER}
          value={tags.name}
          onChange={(e) => onSubmit({ ...tags, name: e.target.value })}
          className="flex-1"
        />
        <div className="flex items-center gap-2">
          <Label htmlFor="tags-color" className="whitespace-nowrap">
            {WORKSPACE_CONSTANTS.UI_TEXT.PICK_COLOR_LABEL}
          </Label>
          <Input
            id="tags-color"
            type="color"
            value={tags.color}
            onChange={(e) => onSubmit({ ...tags, color: e.target.value })}
            className="w-20 h-10 p-1 bg-transparent"
          />
        </div>
      </div>
    </div>
  );
}
