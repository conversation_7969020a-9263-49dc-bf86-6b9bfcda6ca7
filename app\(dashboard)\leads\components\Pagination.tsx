import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LEADS_PER_PAGE } from "../constants/leads";

interface PaginationProps {
  currentPage: number;
  totalLeads: number;
  hasMore: boolean;
  isLoading: boolean;
  isFetching: boolean;
  filteredLeadsCount: number;
  onNextPage: () => void;
  onPreviousPage: () => void;
}

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalLeads,
  hasMore,
  isLoading,
  isFetching,
  filteredLeadsCount,
  onNextPage,
  onPreviousPage,
}) => {
  return (
    <>
      {/* Pagination */}
      <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-4">
        <div className="text-sm text-black dark:text-white order-2 sm:order-1">
          Showing <span className="font-medium">{Math.min(filteredLeadsCount, LEADS_PER_PAGE)}</span> of{" "}
          <span className="font-medium">{totalLeads}</span> leads
        </div>

        <div className="flex items-center space-x-2 order-1 sm:order-2">
          <Button
            onClick={onPreviousPage}
            disabled={currentPage === 0 || isLoading || isFetching}
            variant="outline"
            size="sm"
            className="h-9 px-4 border-gray-200 dark:border-gray-800 text-black dark:text-white"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
            Previous
          </Button>

          <div className="text-sm font-medium bg-gray-100 dark:bg-gray-900 text-black dark:text-white px-3 py-1.5 rounded-md border border-gray-200 dark:border-gray-800">
            {currentPage + 1} / {Math.ceil(totalLeads / LEADS_PER_PAGE) || 1}
          </div>

          <Button
            onClick={onNextPage}
            disabled={!hasMore || isLoading || isFetching}
            variant="outline"
            size="sm"
            className="h-9 px-4 border-gray-200 dark:border-gray-800 text-black dark:text-white"
          >
            Next
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 ml-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </Button>
        </div>
      </div>


    </>
  );
};
